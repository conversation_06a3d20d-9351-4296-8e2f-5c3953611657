<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${packageName}.mapper.${ClassName}Mapper">

    <resultMap type="${ClassName}" id="${ClassName}Result">
        #foreach ($column in $columns)
            <result property="${column.javaField}" column="${column.columnName}"/>
        #end
    </resultMap>
    #if($table.sub)

        <resultMap id="${ClassName}${subClassName}Result" type="${ClassName}" extends="${ClassName}Result">
            <collection property="${subclassName}List" notNullColumn="sub_${subTable.pkColumn.columnName}"
                        javaType="java.util.List" resultMap="${subClassName}Result"/>
        </resultMap>

        <resultMap type="${subClassName}" id="${subClassName}Result">
            #foreach ($column in $subTable.columns)
                <result property="${column.javaField}" column="sub_${column.columnName}"/>
            #end
        </resultMap>
    #end

    <sql id="select${ClassName}Vo">
        select#foreach($column in $columns) $column.columnName#if($velocityCount != $columns.size()),#end#end from ${tableName} t
    </sql>

    <select id="select${ClassName}List" parameterType="${ClassName}" resultMap="${ClassName}Result">
        <include refid="select${ClassName}Vo"/>
        where 1=1
        #foreach($column in $columns)
            #set($queryType=$column.queryType)
            #set($javaField=$column.javaField)
            #set($javaType=$column.javaType)
            #set($columnName=$column.columnName)
            #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #if($column.query)
                #if($column.queryType == "EQ")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName = #{$javaField}
                    </if>
                #elseif($queryType == "NE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName != #{$javaField}
                    </if>
                #elseif($queryType == "GT")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName &gt; #{$javaField}
                    </if>
                #elseif($queryType == "GTE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName &gt;= #{$javaField}
                    </if>
                #elseif($queryType == "LT")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName &lt; #{$javaField}
                    </if>
                #elseif($queryType == "LTE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName &lt;= #{$javaField}
                    </if>
                #elseif($queryType == "LIKE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end">
                        and t.$columnName like concat('%', #{$javaField}, '%')
                    </if>
                #elseif($queryType == "BETWEEN")
                    <if test="params.begin$AttrName != null and params.begin$AttrName != '' and params.end$AttrName != null and params.end$AttrName != ''">
                        and t.$columnName between #{params.begin$AttrName} and #{params.end$AttrName}
                    </if>
                #end
            #end
        #end
        #if($dataScope)
            ${params.dataScope}
        #end
    </select>

    <select id="select${ClassName}ById" parameterType="${pkColumn.javaType}"
            resultMap="#if($table.sub)${ClassName}${subClassName}Result#else${ClassName}Result#end">
        #if($table.crud || $table.tree)
            <include refid="select${ClassName}Vo"/>
            where t.${pkColumn.columnName} = #{${pkColumn.javaField}}
        #elseif($table.sub)
            select#foreach($column in $columns) a.$column.columnName#if($velocityCount != $columns.size()),#end#end,
            #foreach($column in $subTable.columns) b.$column.columnName as sub_$column.columnName#if($velocityCount != $subTable.columns.size()),#end#end
            from ${tableName} a
            left join ${subTableName} b on b.${subTableFkName} = a.${pkColumn.columnName}
            where a.${pkColumn.columnName} = #{${pkColumn.javaField}}
        #end
    </select>

    <insert id="insert${ClassName}" parameterType="${ClassName}"#if($pkColumn.increment) useGeneratedKeys="true"
            keyProperty="$pkColumn.javaField"#end>
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #foreach($column in $columns)
                #if($column.columnName != $pkColumn.columnName || !$pkColumn.increment)
                    <if test="$column.javaField != null#if($column.javaType ==
                        'String' && $column.required) and $column.javaField != ''#end">$column.columnName,
                    </if>
                #end
            #end
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #foreach($column in $columns)
                #if($column.columnName != $pkColumn.columnName || !$pkColumn.increment)
                    <if test="$column.javaField != null#if($column.javaType ==
                        'String' && $column.required) and $column.javaField != ''#end">#{$column.javaField},
                    </if>
                #end
            #end
        </trim>
    </insert>

    <update id="update${ClassName}" parameterType="${ClassName}">
        update ${tableName}
        <trim prefix="SET" suffixOverrides=",">
            #foreach($column in $columns)
                #if($column.columnName != $pkColumn.columnName)
                    <if test="$column.javaField != null#if($column.javaType ==
                        'String' && $column.required) and $column.javaField != ''#end">$column.columnName =
                        #{$column.javaField},
                    </if>
                #end
            #end
        </trim>
        where ${pkColumn.columnName} = #{${pkColumn.javaField}}
    </update>

    <delete id="delete${ClassName}ById" parameterType="${pkColumn.javaType}">
        delete from ${tableName} where ${pkColumn.columnName} = #{${pkColumn.javaField}}
    </delete>

    <delete id="delete${ClassName}ByIds" parameterType="String">
        delete from ${tableName} where ${pkColumn.columnName} in
        <foreach item="${pkColumn.javaField}" collection="array" open="(" separator="," close=")">
            #{${pkColumn.javaField}}
        </foreach>
    </delete>
    #if($table.sub)

        <delete id="delete${subClassName}By${subTableFkClassName}s" parameterType="String">
            delete from ${subTableName} where ${subTableFkName} in
            <foreach item="${subTableFkclassName}" collection="array" open="(" separator="," close=")">
                #{${subTableFkclassName}}
            </foreach>
        </delete>

        <delete id="delete${subClassName}By${subTableFkClassName}" parameterType="Long">
            delete from ${subTableName} where ${subTableFkName} = #{${subTableFkclassName}}
        </delete>

        <insert id="batch${subClassName}">
            insert into ${subTableName}
            (#foreach($column in $subTable.columns) $column.columnName#if($velocityCount != $subTable.columns.size())
            ,#end#end) values
            <foreach item="item" index="index" collection="list" separator=",">
                (#foreach($column in $subTable.columns) #{item.$column.javaField}#if($velocityCount != $subTable.columns.size()),#end#end)
            </foreach>
        </insert>
    #end
</mapper>