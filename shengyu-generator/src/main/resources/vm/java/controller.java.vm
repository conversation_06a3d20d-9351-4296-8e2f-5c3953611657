package ${packageName}.controller;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.ApiOperation;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.shengyu.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
#if($table.crud || $table.sub)
import com.shengyu.common.core.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}" )
@Api(tags = "${functionName}" )
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

/**
 * 查询${functionName}列表
 */
@ApiOperation(value = "查询${functionName}列表" )
@PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')" )
@PostMapping("/list" )
    #if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}> list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
    #elseif($table.tree)
        public AjaxResult list(${ClassName} ${className}) {
            List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
            return AjaxResult.success(list);
        }
    #end

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation(value = "获取${functionName}详细信息" )
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')" )
    @PostMapping(value = "/getInfo" )
    public AjaxResult<${ClassName}> getInfo(@RequestParam ${pkColumn.javaType} ${pkColumn.javaField}) {
        return AjaxResult.success(${className}Service.select${ClassName}ById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation(value = "新增${functionName}" )
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')" )
    @Log(title = "${functionName}" , businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<String> add(@RequestBody @Validated ${ClassName} ${className}) {
        return toAjax(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation(value = "修改${functionName}" )
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')" )
    @Log(title = "${functionName}" , businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<String> edit(@RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation(value = "删除${functionName}" )
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')" )
    @Log(title = "${functionName}" , businessType = BusinessType.DELETE)
    @PostMapping("/delete" )
    public AjaxResult<String> remove(@RequestParam ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.delete${ClassName}ByIds(${pkColumn.javaField}s));
    }

    /**
     * 导出${functionName}列表
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}. class);
        return util.exportExcel(list, "${functionName}数据");
    }
}
