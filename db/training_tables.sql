-- 农业培训系统表结构设计
-- 创建时间: 2025-07-31
-- 说明: 包含讲师管理、课件管理、学习记录等核心表

-- 1. 讲师管理表
DROP TABLE IF EXISTS `t_business_training_lecturer`;
CREATE TABLE `t_business_training_lecturer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '讲师主键ID',
  `lecturer_name` varchar(50) NOT NULL COMMENT '讲师姓名',
  `lecturer_no` varchar(30) NOT NULL COMMENT '讲师编号',
  `lecturer_phone` varchar(20) DEFAULT '' COMMENT '讲师联系电话',
  `lecturer_avatar` varchar(500) DEFAULT '' COMMENT '讲师头像地址',
  `lecturer_title` varchar(100) DEFAULT '' COMMENT '讲师职称',
  `lecturer_organization` varchar(200) DEFAULT '' COMMENT '讲师所属机构',
  `lecturer_specialty` varchar(200) DEFAULT '' COMMENT '讲师专业领域',
  `lecturer_introduction` varchar(1000) DEFAULT '' COMMENT '讲师个人简介',
  `lecturer_status` char(1) DEFAULT '1' COMMENT '讲师状态（1正常 0停用）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_tl_lecturer_no` (`lecturer_no`) USING BTREE,
  KEY `idx_tl_status` (`lecturer_status`) USING BTREE,
  KEY `idx_tl_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='农业培训讲师信息表';

-- 2. 培训资料表
DROP TABLE IF EXISTS `t_business_training_material`;
CREATE TABLE `t_business_training_material` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资料ID',
  `material_name` varchar(200) NOT NULL COMMENT '资料名称',
  `material_no` varchar(50) NOT NULL COMMENT '资料编号',
  `category_type` varchar(30) NOT NULL COMMENT '分类类型',
  `lecturer_id` bigint(20) DEFAULT NULL COMMENT '讲师ID',
  `file_paths` text COMMENT '文件路径（JSON格式存储多个文件）',
  `cover_image` varchar(500) DEFAULT '' COMMENT '封面图片',
  `description` varchar(1000) DEFAULT '' COMMENT '资料描述',
  `view_count` int(11) DEFAULT 0 COMMENT '观看次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `material_status` char(1) DEFAULT '1' COMMENT '资料状态（1正常 0停用）',
  `recommend_sort` int(4) DEFAULT 0 COMMENT '推荐排序（0不推荐，>0推荐，数值越大排序越靠前）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_tm_material_no` (`material_no`) USING BTREE,
  KEY `idx_tm_category_type` (`category_type`) USING BTREE,
  KEY `idx_tm_lecturer_id` (`lecturer_id`) USING BTREE,
  KEY `idx_tm_status` (`material_status`) USING BTREE,
  KEY `idx_tm_recommend_sort` (`recommend_sort`) USING BTREE,
  KEY `idx_tm_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训资料表';

-- 3. 学习记录表
DROP TABLE IF EXISTS `t_business_training_study_record`;
CREATE TABLE `t_business_training_study_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `material_id` bigint(20) NOT NULL COMMENT '资料ID',
  `study_progress` decimal(5,2) DEFAULT 0.00 COMMENT '学习进度（百分比）',
  `last_position` int(11) DEFAULT 0 COMMENT '最后学习位置（秒）',
  `is_completed` char(1) DEFAULT '0' COMMENT '是否完成（1是 0否）',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `study_start_time` datetime DEFAULT NULL COMMENT '开始学习时间',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tsr_user_id` (`user_id`) USING BTREE,
  KEY `idx_tsr_material_id` (`material_id`) USING BTREE,
  KEY `idx_tsr_user_material` (`user_id`, `material_id`) USING BTREE,
  KEY `idx_tsr_completed` (`is_completed`) USING BTREE,
  KEY `idx_tsr_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='学习记录表';


-- 初始化字典数据
-- 培训分类字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('培训分类', 'business_training_category', '1', 'admin', NOW(), '农业培训分类');

-- 培训分类字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '热门推荐', 'hot_recommend', 'business_training_category', '', 'danger', 'Y', '1', 'admin', NOW(), '热门推荐课程'),
(2, '种植技术', 'planting_tech', 'business_training_category', '', 'success', 'N', '1', 'admin', NOW(), '农作物种植技术培训'),
(3, '经营管理', 'business_mgmt', 'business_training_category', '', 'info', 'N', '1', 'admin', NOW(), '农业经营管理培训'),
(4, '专家课程', 'expert_course', 'business_training_category', '', 'warning', 'N', '1', 'admin', NOW(), '专家授课课程'),
(5, '政策解读', 'policy_explain', 'business_training_category', '', 'primary', 'N', '1', 'admin', NOW(), '农业政策解读'),
(6, '致富经验', 'wealth_experience', 'business_training_category', '', 'default', 'N', '1', 'admin', NOW(), '致富经验分享');
