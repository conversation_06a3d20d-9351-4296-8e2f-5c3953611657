-- 专家问诊系统表结构设计（专业工单版）
-- 创建时间: 2025-01-27
-- 说明: 包含专家管理、工单管理、操作记录等核心表

-- 1. 专家管理表
DROP TABLE IF EXISTS `t_business_expert`;
CREATE TABLE `t_business_expert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '专家主键ID',
  `expert_name` varchar(50) NOT NULL COMMENT '专家姓名',
  `expert_no` varchar(30) NOT NULL COMMENT '专家编号',
  `expert_type` varchar(30) NOT NULL COMMENT '专家类型（breeding-育种专家，cultivation-栽培专家，soil-土壤专家）',
  `title` varchar(100) DEFAULT '' COMMENT '专家职称',
  `organization` varchar(200) DEFAULT '' COMMENT '所属机构',
  `phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `introduction` varchar(1000) DEFAULT '' COMMENT '个人简介',
  `service_price` decimal(10,2) DEFAULT 0.00 COMMENT '服务价格（元/次）',
  `rating` decimal(3,2) DEFAULT 5.00 COMMENT '评分（1-5分）',
  `consultation_count` int(11) DEFAULT 0 COMMENT '问诊次数',
  `recommend_order` int(11) DEFAULT 999 COMMENT '推荐排序（数字越小越靠前）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（0-否，1-是）',
  `status` char(1) DEFAULT '1' COMMENT '专家状态（0-停用，1-启用）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_expert_no` (`expert_no`) USING BTREE,
  KEY `idx_expert_type` (`expert_type`) USING BTREE,
  KEY `idx_expert_status` (`status`) USING BTREE,
  KEY `idx_expert_recommend` (`is_recommend`, `recommend_order`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='专家管理表';

-- 2. 问诊工单主表
DROP TABLE IF EXISTS `t_business_consultation_ticket`;
CREATE TABLE `t_business_consultation_ticket` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工单ID',
  `ticket_no` varchar(30) NOT NULL COMMENT '工单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `expert_id` bigint(20) DEFAULT NULL COMMENT '专家ID',
  `expert_type` varchar(30) NOT NULL COMMENT '咨询专家类型',
  `title` varchar(200) NOT NULL COMMENT '工单标题',
  `description` text NOT NULL COMMENT '问题描述',
  `crop_type` varchar(100) DEFAULT '' COMMENT '作物类型',
  `urgency_level` char(1) DEFAULT '2' COMMENT '紧急程度（1-紧急，2-一般，3-不急）',
  `priority_level` char(1) DEFAULT '2' COMMENT '优先级（1-高，2-中，3-低）',
  `status` varchar(20) DEFAULT 'pending' COMMENT '工单状态（pending-待接诊，accepted-已接诊，processing-处理中，waiting-等待回复，resolved-已解决，closed-已关闭）',
  `category` varchar(50) DEFAULT '' COMMENT '问题分类',
  `tags` varchar(500) DEFAULT '' COMMENT '标签（多个用逗号分隔）',
  `estimated_time` int(11) DEFAULT 0 COMMENT '预计处理时间（小时）',
  `actual_time` int(11) DEFAULT 0 COMMENT '实际处理时间（小时）',
  `response_time` int(11) DEFAULT 0 COMMENT '首次响应时间（分钟）',
  `resolution_time` datetime DEFAULT NULL COMMENT '解决时间',
  `close_time` datetime DEFAULT NULL COMMENT '关闭时间',
  `solution` text COMMENT '解决方案',
  `satisfaction_rating` int(11) DEFAULT 0 COMMENT '满意度评分（1-5分）',
  `satisfaction_comment` varchar(1000) DEFAULT '' COMMENT '满意度评价',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0-否，1-是）',
  `source` varchar(20) DEFAULT 'web' COMMENT '工单来源（web-网页，app-手机应用，phone-电话）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_ticket_no` (`ticket_no`) USING BTREE,
  KEY `idx_ticket_user_id` (`user_id`) USING BTREE,
  KEY `idx_ticket_expert_id` (`expert_id`) USING BTREE,
  KEY `idx_ticket_status` (`status`) USING BTREE,
  KEY `idx_ticket_urgency` (`urgency_level`) USING BTREE,
  KEY `idx_ticket_priority` (`priority_level`) USING BTREE,
  KEY `idx_ticket_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='问诊工单主表';

-- 3. 工单操作记录表
DROP TABLE IF EXISTS `t_business_consultation_operation`;
CREATE TABLE `t_business_consultation_operation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '操作记录ID',
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_type` char(1) NOT NULL COMMENT '操作人类型（1-用户，2-专家，3-系统）',
  `operation_type` varchar(30) NOT NULL COMMENT '操作类型（create-创建，accept-接诊，reply-回复，transfer-转诊，resolve-解决，close-关闭）',
  `operation_content` text COMMENT '操作内容',
  `before_status` varchar(20) DEFAULT '' COMMENT '操作前状态',
  `after_status` varchar(20) DEFAULT '' COMMENT '操作后状态',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT '' COMMENT '操作IP地址',
  `user_agent` varchar(500) DEFAULT '' COMMENT '用户代理',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_operation_ticket_id` (`ticket_id`) USING BTREE,
  KEY `idx_operation_operator_id` (`operator_id`) USING BTREE,
  KEY `idx_operation_type` (`operation_type`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工单操作记录表';

-- 4. 工单回复记录表
DROP TABLE IF EXISTS `t_business_consultation_reply`;
CREATE TABLE `t_business_consultation_reply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '回复记录ID',
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `reply_no` varchar(30) NOT NULL COMMENT '回复编号',
  `replier_id` bigint(20) NOT NULL COMMENT '回复人ID',
  `replier_type` char(1) NOT NULL COMMENT '回复人类型（1-用户，2-专家）',
  `reply_type` varchar(20) DEFAULT 'text' COMMENT '回复类型（text-文本，image-图片，file-文件）',
  `content` text COMMENT '回复内容',
  `file_url` varchar(500) DEFAULT '' COMMENT '文件URL',
  `file_name` varchar(200) DEFAULT '' COMMENT '文件名',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `is_internal` char(1) DEFAULT '0' COMMENT '是否内部回复（0-否，1-是）',
  `is_read` char(1) DEFAULT '0' COMMENT '是否已读（0-未读，1-已读）',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_reply_no` (`reply_no`) USING BTREE,
  KEY `idx_reply_ticket_id` (`ticket_id`) USING BTREE,
  KEY `idx_reply_replier_id` (`replier_id`) USING BTREE,
  KEY `idx_reply_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工单回复记录表';

-- 5. 工单附件表
DROP TABLE IF EXISTS `t_business_consultation_attachment`;
CREATE TABLE `t_business_consultation_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `ticket_id` bigint(20) DEFAULT NULL COMMENT '工单ID',
  `reply_id` bigint(20) DEFAULT NULL COMMENT '回复ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) DEFAULT '' COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT '' COMMENT '文件类型',
  `file_ext` varchar(20) DEFAULT '' COMMENT '文件扩展名',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `uploader_id` bigint(20) NOT NULL COMMENT '上传人ID',
  `uploader_type` char(1) NOT NULL COMMENT '上传人类型（1-用户，2-专家）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_attachment_ticket_id` (`ticket_id`) USING BTREE,
  KEY `idx_attachment_reply_id` (`reply_id`) USING BTREE,
  KEY `idx_attachment_uploader_id` (`uploader_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工单附件表';

-- 初始化字典数据
-- 专家类型字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('专家类型', 'business_expert_type', '1', 'admin', NOW(), '农业专家类型分类');

-- 专家类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '育种专家', 'breeding', 'business_expert_type', '', 'primary', 'N', '1', 'admin', NOW(), '农作物育种专家'),
(2, '栽培专家', 'cultivation', 'business_expert_type', '', 'success', 'N', '1', 'admin', NOW(), '农作物栽培专家'),
(3, '土壤专家', 'soil', 'business_expert_type', '', 'warning', 'N', '1', 'admin', NOW(), '土壤改良专家');

-- 工单状态字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('工单状态', 'business_ticket_status', '1', 'admin', NOW(), '问诊工单状态分类');

-- 工单状态字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待接诊', 'pending', 'business_ticket_status', '', 'warning', 'N', '1', 'admin', NOW(), '等待专家接诊'),
(2, '已接诊', 'accepted', 'business_ticket_status', '', 'info', 'N', '1', 'admin', NOW(), '专家已接诊'),
(3, '处理中', 'processing', 'business_ticket_status', '', 'primary', 'N', '1', 'admin', NOW(), '正在处理中'),
(4, '等待回复', 'waiting', 'business_ticket_status', '', 'warning', 'N', '1', 'admin', NOW(), '等待用户回复'),
(5, '已解决', 'resolved', 'business_ticket_status', '', 'success', 'N', '1', 'admin', NOW(), '问题已解决'),
(6, '已关闭', 'closed', 'business_ticket_status', '', 'default', 'N', '1', 'admin', NOW(), '工单已关闭');

-- 紧急程度字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('紧急程度', 'business_urgency_level', '1', 'admin', NOW(), '工单紧急程度分类');

-- 紧急程度字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '紧急', '1', 'business_urgency_level', '', 'danger', 'N', '1', 'admin', NOW(), '紧急问题'),
(2, '一般', '2', 'business_urgency_level', '', 'warning', 'Y', '1', 'admin', NOW(), '一般问题'),
(3, '不急', '3', 'business_urgency_level', '', 'info', 'N', '1', 'admin', NOW(), '不急问题');

-- 优先级字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('优先级', 'business_priority_level', '1', 'admin', NOW(), '工单优先级分类');

-- 优先级字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '高', '1', 'business_priority_level', '', 'danger', 'N', '1', 'admin', NOW(), '高优先级'),
(2, '中', '2', 'business_priority_level', '', 'warning', 'Y', '1', 'admin', NOW(), '中优先级'),
(3, '低', '3', 'business_priority_level', '', 'info', 'N', '1', 'admin', NOW(), '低优先级'); 