# 农业培训系统表结构总结

## 📋 表结构概览

根据您的要求，最终设计了3张核心表，所有表名都添加了`t_business_`前缀，索引名包含表名首拼：

### 1. 讲师管理表
- **表名**: `t_business_training_instructor`
- **首拼**: `ti` (training_instructor)
- **说明**: 管理培训讲师的基本信息、专业领域、资质等

### 2. 课件管理表  
- **表名**: `t_business_training_courseware`
- **首拼**: `tc` (training_courseware)
- **说明**: 管理培训课件资源，支持视频、音频、文档、图片等多种格式

### 3. 学习记录表
- **表名**: `t_business_training_study_record`  
- **首拼**: `tsr` (training_study_record)
- **说明**: 记录用户的学习情况，包含学习进度、时长、完成状态等

## 🔧 索引命名规范

所有索引都按照以下规范命名：
- **唯一索引**: `uk_{表名首拼}_{字段描述}`
- **普通索引**: `idx_{表名首拼}_{字段描述}`

### 示例：
```sql
-- 讲师表 (ti)
UNIQUE KEY `uk_ti_instructor_code` (`instructor_code`)
KEY `idx_ti_status` (`status`)
KEY `idx_ti_level` (`level`)

-- 课件表 (tc)  
UNIQUE KEY `uk_tc_courseware_code` (`courseware_code`)
KEY `idx_tc_category_type` (`category_type`)
KEY `idx_tc_status` (`status`)

-- 学习记录表 (tsr)
KEY `idx_tsr_user_id` (`user_id`)
KEY `idx_tsr_courseware_id` (`courseware_id`)
KEY `idx_tsr_user_courseware` (`user_id`, `courseware_id`)
```

## 📊 字典数据配置

系统通过5种字典类型管理各种分类和枚举值：

1. **培训分类** (`training_category_type`)
   - 热门推荐、种植技术、经营管理、专家课程、政策解读、致富经验

2. **讲师级别** (`training_instructor_level`)  
   - 初级(1)、中级(2)、高级(3)、专家(4)

3. **课件文件类型** (`training_file_type`)
   - 视频(video)、音频(audio)、文档(document)、图片(image)

4. **难度级别** (`training_difficulty_level`)
   - 入门(1)、初级(2)、中级(3)、高级(4)

5. **设备类型** (`training_device_type`)
   - PC端(pc)、网页端(web)、手机端(mobile)、APP端(app)

## 🎯 设计特点

1. **规范统一**: 
   - 表名前缀：`t_business_`
   - 主键统一：`id`
   - 索引命名包含表名首拼

2. **结构简化**:
   - 去掉复杂的课程体系
   - 直接基于课件进行学习管理
   - 通过字典数据管理分类

3. **性能优化**:
   - 索引直接集成在建表语句中
   - 创建了必要的复合索引

4. **遵循项目规范**:
   - 继承BaseEntity基类
   - 包含通用字段(create_by, create_time等)
   - 符合项目命名约定

## 📁 文件清单

- `db/training_tables.sql` - 完整的建表SQL脚本 + 字典数据初始化
- `docs/training_database_design.md` - 详细的数据库设计文档  
- `docs/training_tables_summary.md` - 本总结文档

## 🚀 使用说明

1. 执行 `db/training_tables.sql` 创建表结构和初始化字典数据
2. 参考 `docs/training_database_design.md` 了解详细的字段说明
3. 根据字典配置进行前端下拉选项的数据绑定

所有设计都已按照您的要求完成，可以直接投入使用！
