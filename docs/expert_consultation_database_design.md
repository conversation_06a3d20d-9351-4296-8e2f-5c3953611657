# 专家问诊系统数据库设计文档（专业工单版）

## 概述

专家问诊系统是一个农业技术咨询平台，采用专业的工单管理模式，为农户提供规范化的技术咨询服务。系统包含专家管理、工单管理、操作记录、回复记录和附件管理等核心功能。

## 数据库表结构

### 1. 专家管理表 (t_business_expert)

**表说明**: 存储专家基本信息和服务能力

**主要字段**:
- `id`: 专家主键ID
- `expert_name`: 专家姓名
- `expert_no`: 专家编号（唯一）
- `expert_type`: 专家类型（育种、栽培、土壤）
- `title`: 专家职称
- `organization`: 所属机构
- `phone`: 联系电话
- `introduction`: 个人简介
- `service_price`: 服务价格（元/次）
- `rating`: 评分（1-5分）
- `consultation_count`: 问诊次数
- `recommend_order`: 推荐排序（数字越小越靠前）
- `is_recommend`: 是否推荐（0-否，1-是）
- `status`: 专家状态（0-停用，1-启用）

**索引设计**:
- 主键索引: `id`
- 唯一索引: `expert_no`
- 普通索引: `expert_type`, `status`, `is_recommend`

### 2. 问诊工单主表 (t_business_consultation_ticket)

**表说明**: 存储工单的核心信息，类似ITSM工单系统

**主要字段**:
- `id`: 工单ID
- `ticket_no`: 工单号（唯一）
- `user_id`: 用户ID
- `expert_id`: 专家ID
- `expert_type`: 咨询专家类型
- `title`: 工单标题
- `description`: 问题描述
- `crop_type`: 作物类型
- `urgency_level`: 紧急程度（1-紧急，2-一般，3-不急）
- `priority_level`: 优先级（1-高，2-中，3-低）
- `status`: 工单状态（待接诊、已接诊、处理中、等待回复、已解决、已关闭）
- `category`: 问题分类
- `tags`: 标签（多个用逗号分隔）
- `estimated_time`: 预计处理时间（小时）
- `actual_time`: 实际处理时间（小时）
- `response_time`: 首次响应时间（分钟）
- `resolution_time`: 解决时间
- `close_time`: 关闭时间
- `solution`: 解决方案
- `satisfaction_rating`: 满意度评分（1-5分）
- `satisfaction_comment`: 满意度评价
- `is_public`: 是否公开（0-否，1-是）
- `source`: 工单来源（web-网页，app-手机应用，phone-电话）

**索引设计**:
- 主键索引: `id`
- 唯一索引: `ticket_no`
- 普通索引: `user_id`, `expert_id`, `status`, `urgency_level`, `priority_level`

### 3. 工单操作记录表 (t_business_consultation_operation)

**表说明**: 记录工单的所有操作历史，用于审计和追踪

**主要字段**:
- `id`: 操作记录ID
- `ticket_id`: 工单ID
- `operator_id`: 操作人ID
- `operator_type`: 操作人类型（1-用户，2-专家，3-系统）
- `operation_type`: 操作类型（create-创建，accept-接诊，reply-回复，transfer-转诊，resolve-解决，close-关闭）
- `operation_content`: 操作内容
- `before_status`: 操作前状态
- `after_status`: 操作后状态
- `operation_time`: 操作时间
- `ip_address`: 操作IP地址
- `user_agent`: 用户代理

**索引设计**:
- 主键索引: `id`
- 普通索引: `ticket_id`, `operator_id`, `operation_type`, `operation_time`

### 4. 工单回复记录表 (t_business_consultation_reply)

**表说明**: 存储工单的回复内容，支持文本、图片、文件等多种类型

**主要字段**:
- `id`: 回复记录ID
- `ticket_id`: 工单ID
- `reply_no`: 回复编号（唯一）
- `replier_id`: 回复人ID
- `replier_type`: 回复人类型（1-用户，2-专家）
- `reply_type`: 回复类型（text-文本，image-图片，file-文件）
- `content`: 回复内容
- `file_url`: 文件URL
- `file_name`: 文件名
- `file_size`: 文件大小（字节）
- `is_internal`: 是否内部回复（0-否，1-是）
- `is_read`: 是否已读（0-未读，1-已读）
- `read_time`: 阅读时间

**索引设计**:
- 主键索引: `id`
- 唯一索引: `reply_no`
- 普通索引: `ticket_id`, `replier_id`, `create_time`

### 5. 工单附件表 (t_business_consultation_attachment)

**表说明**: 存储工单相关的附件文件

**主要字段**:
- `id`: 附件ID
- `ticket_id`: 工单ID
- `reply_id`: 回复ID
- `file_name`: 文件名
- `file_path`: 文件路径
- `file_url`: 文件URL
- `file_size`: 文件大小（字节）
- `file_type`: 文件类型
- `file_ext`: 文件扩展名
- `upload_time`: 上传时间
- `uploader_id`: 上传人ID
- `uploader_type`: 上传人类型（1-用户，2-专家）

**索引设计**:
- 主键索引: `id`
- 普通索引: `ticket_id`, `reply_id`, `uploader_id`

## 专家类型设计

系统支持以下专家类型：

1. **育种专家** (breeding) - 农作物育种专家
2. **栽培专家** (cultivation) - 农作物栽培专家
3. **土壤专家** (soil) - 土壤改良专家

## 工单状态流转

工单状态流转如下：

1. **待接诊** (pending) - 用户提交工单，等待专家接诊
2. **已接诊** (accepted) - 专家已接诊，开始处理
3. **处理中** (processing) - 专家正在处理问题
4. **等待回复** (waiting) - 等待用户回复或补充信息
5. **已解决** (resolved) - 问题已解决
6. **已关闭** (closed) - 工单已关闭

## 工单优先级和紧急程度

### 紧急程度
- **紧急** (1) - 需要立即处理的问题
- **一般** (2) - 正常处理的问题
- **不急** (3) - 可以延后处理的问题

### 优先级
- **高** (1) - 高优先级问题
- **中** (2) - 中优先级问题
- **低** (3) - 低优先级问题

## 操作记录设计

### 操作类型
- **create** - 创建工单
- **accept** - 接诊工单
- **reply** - 回复工单
- **transfer** - 转诊工单
- **resolve** - 解决工单
- **close** - 关闭工单

### 操作人类型
- **用户** (1) - 农户操作
- **专家** (2) - 专家操作
- **系统** (3) - 系统自动操作

## 回复记录设计

### 回复类型
- **文本** (text) - 普通文字回复
- **图片** (image) - 图片文件
- **文件** (file) - 其他文件

### 内部回复
- 支持内部回复功能，专家可以在内部进行讨论，用户看不到

## 数据字典

系统包含以下数据字典：

1. **专家类型** (business_expert_type)
2. **工单状态** (business_ticket_status)
3. **紧急程度** (business_urgency_level)
4. **优先级** (business_priority_level)

## 核心功能

1. **专家管理**: 专家信息维护、状态管理
2. **工单管理**: 工单创建、状态流转、优先级管理
3. **操作审计**: 完整的操作记录和审计追踪
4. **回复管理**: 多类型回复、内部回复、已读状态
5. **附件管理**: 文件上传、类型管理
6. **统计分析**: 响应时间、处理时间、满意度统计

## 扩展性考虑

1. **多租户支持**: 所有表都包含 `tenant_id` 字段
2. **软删除**: 所有表都支持软删除
3. **审计字段**: 包含创建人、创建时间、更新人、更新时间
4. **索引优化**: 针对常用查询场景建立合适的索引
5. **分表策略**: 操作记录表可按时间分表，回复记录表可按工单ID分表
6. **文件存储**: 支持本地存储和云存储
7. **通知机制**: 支持邮件、短信、站内信等多种通知方式 