package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shengyu.common.annotation.Excel;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商城服务商信息对象 t_mall_service_provider
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_mall_service_provider")
@ApiModel(value = "商城服务商信息对象")
public class ServiceProvider extends TenantEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 服务商编号
     */
    @Excel(name = "服务商编号")
    @ApiModelProperty("服务商编号")
    private String providerNo;

    /**
     * 关联用户ID
     */
    @Excel(name = "关联用户ID")
    @ApiModelProperty("关联用户ID")
    private Long userId;

    /**
     * 服务商名称
     */
    @Excel(name = "服务商名称")
    @ApiModelProperty("服务商名称")
    private String providerName;

    /**
     * 营业执照号
     */
    @Excel(name = "营业执照号")
    @ApiModelProperty("营业执照号")
    private String businessLicense;

    /**
     * 营业执照图片
     */
    @Excel(name = "营业执照图片")
    @ApiModelProperty("营业执照图片")
    private String businessLicenseImage;

    /**
     * 法人姓名
     */
    @Excel(name = "法人姓名")
    @ApiModelProperty("法人姓名")
    private String legalPerson;

    /**
     * 法人身份证号
     */
    @Excel(name = "法人身份证号")
    @ApiModelProperty("法人身份证号")
    private String legalPersonIdCard;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Excel(name = "联系邮箱")
    @ApiModelProperty("联系邮箱")
    private String contactEmail;

    /**
     * 营业地址
     */
    @Excel(name = "营业地址")
    @ApiModelProperty("营业地址")
    private String businessAddress;

    /**
     * 注册地址
     */
    @Excel(name = "注册地址")
    @ApiModelProperty("注册地址")
    private String registeredAddress;

    /**
     * 经营范围
     */
    @Excel(name = "经营范围")
    @ApiModelProperty("经营范围")
    private String businessScope;

    /**
     * 服务商类型(01企业 02个体 03个人)
     */
    @Excel(name = "服务商类型(01企业 02个体 03个人)")
    @ApiModelProperty("服务商类型(01企业 02个体 03个人)")
    private String providerType;

    /**
     * 服务商等级(01普通 02银牌 03金牌)
     */
    @Excel(name = "服务商等级(01普通 02银牌 03金牌)")
    @ApiModelProperty("服务商等级(01普通 02银牌 03金牌)")
    private String providerLevel;

    /**
     * 审核状态(0待审核 1已通过 2已拒绝)
     */
    @Excel(name = "审核状态(0待审核 1已通过 2已拒绝)")
    @ApiModelProperty("审核状态(0待审核 1已通过 2已拒绝)")
    private String auditStatus;

    /**
     * 审核意见
     */
    @Excel(name = "审核意见")
    @ApiModelProperty("审核意见")
    private String approvalOpinion;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("审核时间")
    private Date approvalTime;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    @ApiModelProperty("审核人")
    private String approvalBy;

    /**
     * 服务区域
     */
    @Excel(name = "服务区域")
    @ApiModelProperty("服务区域")
    private String serviceArea;

    /**
     * 服务类别
     */
    @Excel(name = "服务类别")
    @ApiModelProperty("服务类别")
    private String serviceCategory;

    /**
     * 服务商简介
     */
    @Excel(name = "服务商简介")
    @ApiModelProperty("服务商简介")
    private String introduction;

    /**
     * 服务商logo
     */
    @Excel(name = "服务商logo")
    @ApiModelProperty("服务商logo")
    private String logo;

    /**
     * 评分(0-5分)
     */
    @Excel(name = "评分(0-5分)")
    @ApiModelProperty("评分(0-5分)")
    private BigDecimal score;

    /**
     * 服务次数
     */
    @Excel(name = "服务次数")
    @ApiModelProperty("服务次数")
    private Long serviceCount;

    /**
     * 删除标志(0存在 1删除)
     */
    @Excel(name = "删除标志(0存在 1删除)")
    @ApiModelProperty("删除标志(0存在 1删除)")
    @TableLogic
    private String deleteFlag;
}
