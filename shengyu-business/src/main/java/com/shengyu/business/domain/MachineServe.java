package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 农机服务表
 *
 * @TableName t_business_machine_serve
 */
@TableName(value = "t_business_machine_serve")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "农机服务表")
public class MachineServe extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务编号
     */
    @ApiModelProperty("服务编号")
    @TableField(value = "serve_no")
    private String serveNo;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    @TableField(value = "serve_name")
    private String serveName;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    @TableField(value = "serve_type")
    private String serveType;

    /**
     * 状态（0上架，下架）
     */
    @ApiModelProperty("状态（0下架，1下架）")
    @TableField(value = "status")
    private Integer status;

    /**
     * 服务banner（0上架，下架）
     */
    @ApiModelProperty("服务banner（0上架，下架）")
    @TableField(value = "serve_banner")
    private String serveBanner;

    /**
     * 服务描述
     */
    @ApiModelProperty("服务描述")
    @TableField(value = "serve_description")
    private String serveDescription;

    /**
     * 软删除标识:0-未删除,1-已删除
     */
    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}