package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.PlaneServe;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞防服务DTO
 * <p>
 * 继承自BusinessPlaneServe实体类，用于业务平面服务相关接口的数据传输
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlaneServeDto", description = "飞防服务DTO")
public class PlaneServeDto extends PlaneServe {

    @ApiModelProperty(value = "服务类型名称")
    private String serveTypeName;

    @ApiModelProperty(value = "服务状态名称")
    private String statusName;

}
