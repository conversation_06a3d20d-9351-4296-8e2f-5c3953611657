package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 飞防服务预定表
 *
 * @TableName t_business_plane_book
 */
@TableName(value = "t_business_plane_book")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "飞防服务预定表")
public class PlaneBook extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预定编号
     */
    @ApiModelProperty("预定编号")
    @TableField(value = "book_no")
    private String bookNo;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableField(value = "book_user_id")
    private Long bookUserId;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    @TableField(value = "serve_type")
    private String serveType;

    /**
     * 飞防服务ID
     */
    @ApiModelProperty("飞防服务ID")
    @TableField(value = "serve_id")
    private Long serveId;

    /**
     * 预定状态（booking预约中，processing处理中，completed已完成）
     */
    @ApiModelProperty("预定状态（booking预约中，processing处理中，completed已完成）")
    @TableField(value = "book_status")
    private String bookStatus;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "phone")
    private String phone;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 地块数量
     */
    @ApiModelProperty("地块数量")
    @TableField(value = "plot_count")
    private Integer plotCount;

    /**
     * 服务面积(亩)
     */
    @ApiModelProperty("服务面积(亩)")
    @TableField(value = "area")
    private BigDecimal area;

    /**
     * 所属地区
     */
    @ApiModelProperty("所属地区")
    @TableField(value = "address_region")
    private String addressRegion;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @TableField(value = "address_detail")
    private String addressDetail;

    /**
     * 软删除标识:0-未删除,1-已删除
     */
    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}