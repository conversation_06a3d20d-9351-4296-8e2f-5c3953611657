package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.ServiceProvider;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 服务商dto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "服务商dto")
public class ServiceProviderDto extends ServiceProvider {

    @ApiModelProperty("服务商类型名称")
    private String providerTypeName;

    @ApiModelProperty("服务类别名称")
    private String serviceCategoryName;

    @ApiModelProperty("服务商等级名称")
    private String providerLevelName;

    @ApiModelProperty("审核状态名称")
    private String auditStatusName;
}