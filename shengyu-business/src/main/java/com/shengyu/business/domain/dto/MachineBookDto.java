package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.MachineBook;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 农机预约记录DTO（包含字典名称）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "农机预约记录DTO")
public class MachineBookDto extends MachineBook {
    /**
     * 服务类型名称
     */
    private String serveTypeName;

    /**
     * 预约状态名称
     */
    private String bookStatusName;
}

