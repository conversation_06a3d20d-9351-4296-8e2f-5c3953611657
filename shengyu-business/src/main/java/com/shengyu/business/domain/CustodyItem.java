package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 点餐式作业项实体类
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "点餐式作业项表")
@TableName(value = "t_business_custody_item")
public class CustodyItem extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 作业项名称
     */
    @ApiModelProperty("作业项名称")
    private String name;

    /**
     * 作业项内容
     */
    @ApiModelProperty("作业项内容")
    private String content;
    
    /**
     * 作业项单价
     */
    @ApiModelProperty("作业项单价")
    private BigDecimal unitPrice;

    /**
     * 作业项单位（如亩、次）
     */
    @ApiModelProperty("作业项单位（如亩、次）")
    private String unit;

    /**
     * 补贴金额
     */
    @ApiModelProperty("补贴金额")
    private BigDecimal subsidy;

    /**
     * 折扣率
     */
    @ApiModelProperty("折扣率")
    private BigDecimal discount;

    /**
     * 作物类型
     */
    @ApiModelProperty("作物类型")
    private String cropType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 软删除标识: 0-未删除, 1-已删除
     */
    @ApiModelProperty("软删除标识: 0-未删除, 1-已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;

}