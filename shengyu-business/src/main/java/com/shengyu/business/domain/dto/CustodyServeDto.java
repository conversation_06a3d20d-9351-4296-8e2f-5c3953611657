package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.CustodyServe;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管服务dto")
public class CustodyServeDto extends CustodyServe {
    @ApiModelProperty("作业类型名称")
    private String cropTypeName;

    @ApiModelProperty("托管状态名称")
    private String statusName;

    @ApiModelProperty("托管状态名称")
    private String custodyCategoryName;

    @ApiModelProperty("作业项列表")
    private List<CustodyItemDto> items;
}