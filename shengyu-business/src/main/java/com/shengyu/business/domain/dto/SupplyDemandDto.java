package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.SupplyDemand;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 托管预定dto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管预定dto")
public class SupplyDemandDto extends SupplyDemand {

    @ApiModelProperty("供需类型名称")
    private String infoTypeName;

    @ApiModelProperty("作物类型名称")
    private String cropTypeName;

    @ApiModelProperty("审核状态名称")
    private String auditStatusName;
}