package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyServeDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 全程托管Mapper接口
 * 提供对全程托管数据访问的定义，包括增删改查操作
 */
public interface CustodyServeMapper extends BaseMapper<CustodyServe> {

    /**
     * 查询符合条件的全程托管列表
     *
     * @param custodyServe 包含查询条件的全程托管对象
     * @return 符合条件的全程托管集合
     */
    List<CustodyServeDto> selectCustodyFullList(CustodyServe custodyServe);

    /**
     * 根据ID查询全程托管信息
     *
     * @param id 全程托管ID
     * @return 符合条件的全程托管对象
     */
    CustodyServeDto selectById(@Param("id") Long id);

    /**
     * 托管作业项是否包含在全程托管中
     *
     * @param singleId 服务项id
     * @return 包含记录数
     */
    Integer itemIdIsInServe(@Param("singleId") Long singleId);
}