package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.ServiceProvider;
import com.shengyu.business.domain.dto.ServiceProviderDto;

import java.util.List;

/**
 * 商城服务商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface ServiceProviderMapper extends BaseMapper<ServiceProvider> {
    /**
     * 查询商城服务商信息
     *
     * @param id 商城服务商信息ID
     * @return 商城服务商信息
     */
    ServiceProviderDto selectServiceProviderById(Long id);

    /**
     * 查询商城服务商信息列表
     *
     * @param serviceProvider 商城服务商信息
     * @return 商城服务商信息集合
     */
    List<ServiceProviderDto> selectServiceProviderList(ServiceProvider serviceProvider);

}
