package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.PlaneServe;

import java.util.List;

/**
 * 飞防服务表Mapper接口
 *
 * <AUTHOR>
 * @description 针对表【t_business_plane_serve(飞防服务表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessPlaneServe
 */
public interface BusinessPlaneServeMapper extends BaseMapper<PlaneServe> {

    /**
     * 根据条件查询飞防服务列表
     *
     * @param planeServe 查询条件对象，包含飞防服务的各种筛选条件
     * @return 符合条件的飞防服务列表
     */
    List<PlaneServe> getList(PlaneServe planeServe);

    /**
     * 根据ID查询飞防服务详情
     *
     * @param id 飞防服务ID
     * @return 飞防服务详情
     */
    PlaneServe selectById(Long id);
}
