package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.MachineServe;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessMachineServe
 */
public interface BusinessMachineServeMapper extends BaseMapper<MachineServe> {

    /**
     * 根据条件查询农机服务列表
     *
     * @param machineServe 查询条件对象，包含农机服务的各种筛选条件
     * @return 符合条件的农机服务列表，若无则返回空列表
     */
    List<MachineServe> getList(MachineServe machineServe);

    /**
     * 根据ID查询农机服务详情
     *
     * @param id 农机服务ID
     * @return 农机服务详情
     */
    MachineServe selectById(Long id);
}
