package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.PlaneBook;

import java.util.List;

/**
 * 飞防服务预定表Mapper接口
 *
 * <AUTHOR>
 * @description 针对表【t_business_plane_book(飞防服务预定表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessPlaneBook
 */
public interface BusinessPlaneBookMapper extends BaseMapper<PlaneBook> {

    /**
     * 根据查询条件获取飞防服务预定列表
     *
     * @param book 查询条件对象，包含筛选条件
     * @return 符合条件的飞防服务预定列表
     */
    List<PlaneBook> getList(PlaneBook book);
}
