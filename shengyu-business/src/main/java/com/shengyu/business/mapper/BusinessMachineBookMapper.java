package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.MachineBook;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessMachineBook
 */
public interface BusinessMachineBookMapper extends BaseMapper<MachineBook> {

    /**
     * 根据查询条件获取农机服务预定列表
     *
     * @param book 查询条件对象，包含筛选条件
     * @return 符合查询条件的农机服务预定列表
     */
    List<MachineBook> getList(MachineBook book);
}




