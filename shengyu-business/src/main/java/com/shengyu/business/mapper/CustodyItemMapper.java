package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 托管服务作业项Mapper
 */
public interface CustodyItemMapper extends BaseMapper<CustodyItem> {

    /**
     * 查询托管服务作业项列表
     *
     * @param custodyItem 作业项
     * @return 作业项集合
     */
    List<CustodyItemDto> selectCustodyItemList(CustodyItem custodyItem);

    /**
     * 根据ID查询作业项
     *
     * @param id 作业项ID
     * @return 作业项
     */
    CustodyItemDto selectById(@Param("id") Long id);

    /**
     * 根据ID列表查询作业项
     *
     * @param ids 作业项ID列表
     * @return 作业项集合
     */
    List<CustodyItemDto> selectList(List<Long> ids);
}