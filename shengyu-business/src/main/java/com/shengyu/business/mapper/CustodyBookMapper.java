package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 托管预定表数据库操作Mapper
 *
 * <AUTHOR>
 * @description 针对表【t_custody_book(托管预定表)】的数据库操作Mapper
 * @createDate 2025-07-12 15:22:06
 * @Entity com.shengyu.base.domain.CustodyBook
 */
public interface CustodyBookMapper extends BaseMapper<CustodyBook> {

    /**
     * 查询托管预定列表
     *
     * @param book 托管预定查询条件对象
     * @return 托管预定记录列表
     */
    List<CustodyBookDto> selectCustodyBookList(CustodyBook book);

    /**
     * 根据ID查询托管预定记录
     *
     * @param id 托管预定记录ID
     * @return 托管预定记录对象
     */
    CustodyBookDto selectCustodyBookById(@Param("id") Long id);
}
