package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供需信息表数据访问层接口
 *
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Mapper
 * @createDate 2025-07-16 14:32:12
 * @Entity com.shengyu.business.domain.BusinessSupplyDemand
 */
public interface BusinessSupplyDemandMapper extends BaseMapper<SupplyDemand> {

    /**
     * 根据条件查询供需信息列表
     *
     * @param supplyDemand 查询条件对象
     * @return 供需信息DTO列表
     */
    List<SupplyDemandDto> getList(SupplyDemand supplyDemand);

    /**
     * 根据ID查询供需信息详情
     *
     * @param id 供需信息ID
     * @return 供需信息DTO对象
     */
    SupplyDemandDto selectById(@Param("id") Long id);

    /**
     * 保存供需信息
     *
     * @param entity 待保存的供需信息对象
     * @return 插入结果，返回插入的行数
     */
    int updateById(@Param("entity") SupplyDemand entity);

    /**
     * 根据ID查询有效的供需信息
     *
     * @param id 供需信息ID
     * @return 有效的供需信息DTO对象
     */
    SupplyDemandDto selectValidById(@Param("id") Long id);
}
