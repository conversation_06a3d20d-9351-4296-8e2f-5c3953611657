package com.shengyu.business.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.mapper.BusinessMachineBookMapper;
import com.shengyu.business.service.IMachineBookService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class MachineBookServiceImpl extends ServiceImpl<BusinessMachineBookMapper, MachineBook>
        implements IMachineBookService {


    @Override
    public List<MachineBook> selectMachineBookList(MachineBook book) {
        return baseMapper.getList(book);
    }

    @Override
    public MachineBook selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public MachineBook selectByIdUserId(Long id, Long userId) {
        MachineBook book = selectById(id);
        if (book == null || !userId.equals(book.getBookUserId())) {
            throw new CustomException("无权限操作");
        }
        return book;
    }

    @Override
    @Transactional
    public boolean saveMachineBook(MachineBook machineBook) {
        machineBook.setBookNo("MB" + SnowflakeIdUtils.getAsString());
        return super.save(machineBook);
    }

    @Override
    @Transactional
    public boolean updateByIdWithUserCheck(MachineBook machineBook, Long userId) {
        selectByIdUserId(machineBook.getId(), userId);
        return updateById(machineBook);
    }

    @Override
    @Transactional
    public boolean removeByIdWithUserCheck(Long id, Long userId) {
        selectByIdUserId(id, userId);
        return removeById(id);
    }
}




