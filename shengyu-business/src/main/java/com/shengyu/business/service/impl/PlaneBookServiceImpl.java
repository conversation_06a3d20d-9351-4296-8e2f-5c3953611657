package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.mapper.BusinessPlaneBookMapper;
import com.shengyu.business.service.IPlaneBookService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_plane_book(飞防服务预定表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class PlaneBookServiceImpl extends ServiceImpl<BusinessPlaneBookMapper, PlaneBook>
        implements IPlaneBookService {

    @Override
    public List<PlaneBook> selectList(PlaneBook book) {
        return baseMapper.getList(book);
    }

    @Override
    public PlaneBook selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public PlaneBook selectByIdUserId(Long id, Long userId) {
        PlaneBook planeBook = this.getById(id);
        if (planeBook == null || !userId.equals(planeBook.getBookUserId())) {
            throw new CustomException("无权限操作");
        }
        return planeBook;
    }

    @Override
    @Transactional
    public boolean savePlaneBook(PlaneBook planeBook) {
        planeBook.setBookNo("PB" + SnowflakeIdUtils.getAsString());
        return super.save(planeBook);
    }

    @Override
    @Transactional
    public boolean updateByIdWithUserCheck(PlaneBook planeBook, Long userId) {
        selectByIdUserId(planeBook.getId(), userId);
        return this.updateById(planeBook);
    }

    @Override
    @Transactional
    public boolean removeByIdWithUserCheck(Long id, Long userId) {
        selectByIdUserId(id, userId);
        return this.removeById(id);
    }


}




