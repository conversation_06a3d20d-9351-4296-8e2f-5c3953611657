package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.MachineServe;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IMachineServeService extends IService<MachineServe> {

    /**
     * 查询农机服务列表
     *
     * @param machineServe 查询条件
     * @return 农机服务列表
     */
    List<MachineServe> selectList(MachineServe machineServe);

    /**
     * 获取指定ID的农机服务对象
     *
     * @param id 农机服务ID
     * @return 符合指定ID的农机服务对象
     */
    MachineServe selectById(Long id);

    /**
     * 保存农机服务
     *
     * @param machineServe 待保存的农机服务对象
     * @return 是否保存成功
     */
    boolean saveMachineServe(MachineServe machineServe);

    /**
     * 根据ID和用户校验后更新农机服务
     *
     * @param machineServe 待更新的农机服务对象
     * @return 是否更新成功
     */
    boolean updateMachineServe(MachineServe machineServe);

    /**
     * 根据ID批量删除农机服务
     *
     * @param ids 主键ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
}
