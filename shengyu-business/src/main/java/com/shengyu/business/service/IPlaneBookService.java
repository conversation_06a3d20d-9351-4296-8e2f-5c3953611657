package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.PlaneBook;

import java.util.List;


/**
 * <AUTHOR>
 * @description 针对表【t_business_plane_book(飞防服务预定表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IPlaneBookService extends IService<PlaneBook> {

    /**
     * 查询飞防服务预定列表
     *
     * @param book 查询条件
     * @return 飞防服务预定列表
     */
    List<PlaneBook> selectList(PlaneBook book);

    /**
     * 获取指定ID的飞防服务预定对象
     *
     * @param id 飞防服务预定ID
     * @return 符合指定ID的飞防服务预定对象
     */
    PlaneBook selectById(Long id);

    /**
     * 根据ID和用户ID查询飞防服务预定
     *
     * @param id     飞防服务预定ID
     * @param userId 用户ID
     * @return 符合条件的飞防服务预定对象
     */
    PlaneBook selectByIdUserId(Long id, Long userId);

    /**
     * 保存飞防服务预定
     *
     * @param planeBook 待保存的飞防服务预定对象
     * @return 是否保存成功
     */
    boolean savePlaneBook(PlaneBook planeBook);

    /**
     * 根据ID和用户校验后更新飞防服务预定
     *
     * @param planeBook 待更新的飞防服务预定对象
     * @param userId    用户ID
     * @return 是否更新成功
     */
    boolean updateByIdWithUserCheck(PlaneBook planeBook, Long userId);

    /**
     * 根据ID和用户校验后删除飞防服务预定
     *
     * @param id     飞防服务预定ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByIdWithUserCheck(Long id, Long userId);
}
