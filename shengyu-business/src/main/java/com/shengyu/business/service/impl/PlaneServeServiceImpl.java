package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.mapper.BusinessPlaneServeMapper;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_plane_serve(飞防服务表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class PlaneServeServiceImpl extends ServiceImpl<BusinessPlaneServeMapper, PlaneServe>
        implements IPlaneServeService {

    @Override
    public List<PlaneServe> selectList(PlaneServe planeServe) {
        return baseMapper.getList(planeServe);
    }

    @Override
    public PlaneServe selectById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional
    public boolean savePlaneServe(PlaneServe planeServe) {
        planeServe.setServeNo("MS" + SnowflakeIdUtils.getAsLong());
        return super.save(planeServe);
    }

    @Override
    @Transactional
    public boolean updatePlaneServe(PlaneServe planeServe) {
        PlaneServe oldPlaneServe = selectById(planeServe.getId());
        if (null == oldPlaneServe) {
            throw new CustomException("飞防服务不存在");
        }
        if (oldPlaneServe.getStatus().equals(1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        return this.updateById(planeServe);
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            PlaneServe planeServe = this.getById(id);
            if (null == planeServe) {
                throw new CustomException("该操作项已不存在!");
            }
            if (planeServe.getStatus().equals(1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

}




