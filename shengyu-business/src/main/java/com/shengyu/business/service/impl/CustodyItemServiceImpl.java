package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.mapper.CustodyItemMapper;
import com.shengyu.business.mapper.CustodyServeMapper;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 托管作业项服务层实现
 */
@Service
public class CustodyItemServiceImpl extends ServiceImpl<CustodyItemMapper, CustodyItem> implements ICustodyItemService {

    @Resource
    private CustodyServeMapper custodyServeMapper;

    @Override
    public List<CustodyItemDto> selectList(CustodyItem custodyItem) {
        return baseMapper.selectCustodyItemList(custodyItem);
    }

    @Override
    public CustodyItemDto selectById(Long id) {
        CustodyItemDto dto = baseMapper.selectById(id);
        if (null == dto) {
            throw new CustomException("该操作项已不存在!");
        }
        return dto;
    }

    @Override
    @Transactional
    public boolean saveCustodyItem(CustodyItem custodyItem) {
        return baseMapper.insert(custodyItem) > 0;
    }

    @Override
    @Transactional
    public boolean updateCustodyItem(CustodyItem custodyItem) {
        CustodyItem oldCustodyItem = selectById(custodyItem.getId());
        if (null == oldCustodyItem) {
            throw new CustomException("该操作项已不存在");
        }
        return baseMapper.updateById(custodyItem) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            CustodyItem custodyItem = this.getById(id);
            if (null == custodyItem) {
                throw new CustomException("该操作项已不存在!");
            }
            if (custodyServeMapper.itemIdIsInServe(id) > 0) {
                throw new CustomException("该作业项已被托管服务使用，请先下架托管服务解除关联再删除!");
            }
        }
        return ids.size() == baseMapper.deleteBatchIds(ids);
    }

    @Override
    public List<CustodyItemDto> selectList(List<Long> ids) {
        ids = ids.stream()
                .sorted()
                .collect(Collectors.toList());
        return baseMapper.selectList(ids);
    }
}