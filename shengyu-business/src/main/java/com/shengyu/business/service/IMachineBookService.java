package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.MachineBook;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IMachineBookService extends IService<MachineBook> {

    /**
     * 查询农机服务预定列表
     *
     * @param book 查询条件
     * @return 农机服务预定列表
     */
    List<MachineBook> selectMachineBookList(MachineBook book);

    /**
     * 获取指定ID的农机服务预定对象
     *
     * @param id 农机服务预定ID
     * @return 符合指定ID的农机服务预定对象
     */
    MachineBook selectById(Long id);

    /**
     * 根据ID和用户ID查询农机服务预定
     *
     * @param id     农机服务预定ID
     * @param userId 用户ID
     * @return 符合条件的农机服务预定对象
     */
    MachineBook selectByIdUserId(Long id, Long userId);

    /**
     * 保存农机服务预定
     *
     * @param machineBook 待保存的农机服务预定对象
     * @return 是否保存成功
     */
    boolean saveMachineBook(MachineBook machineBook);

    /**
     * 用户校验后更新
     *
     * @param machineBook 待更新的农机服务预定对象
     * @param userId      用户ID
     * @return 是否更新成功
     */
    boolean updateByIdWithUserCheck(MachineBook machineBook, Long userId);

    /**
     * 用户校验后删除
     *
     * @param id     农机服务预定ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByIdWithUserCheck(Long id, Long userId);
}

