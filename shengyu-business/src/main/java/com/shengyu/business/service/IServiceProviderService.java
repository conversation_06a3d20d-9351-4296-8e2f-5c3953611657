package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.ServiceProvider;
import com.shengyu.business.domain.dto.ServiceProviderDto;

import java.util.List;

/**
 * 商城服务商信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IServiceProviderService extends IService<ServiceProvider> {
    /**
     * 查询商城服务商信息
     *
     * @param id 商城服务商信息ID
     * @return 商城服务商信息
     */
    ServiceProviderDto selectOne(Long id);

    /**
     * 查询商城服务商信息列表
     *
     * @param serviceProvider 商城服务商信息
     * @return 商城服务商信息集合
     */
    List<ServiceProviderDto> selectList(ServiceProvider serviceProvider);

    /**
     * 保存服务商信息
     *
     * @param entity 服务商实体
     * @return 是否成功
     */
    boolean saveServiceProvider(ServiceProvider entity);

    /**
     * 更新服务商信息
     *
     * @param entity 服务商实体
     * @return 是否成功
     */
    boolean updateServiceProvider(ServiceProvider entity);

    /**
     * 删除服务商
     *
     * @param ids 服务商ID集合
     * @return 是否成功
     */
    boolean removeByIds(List<Long> ids);
}
