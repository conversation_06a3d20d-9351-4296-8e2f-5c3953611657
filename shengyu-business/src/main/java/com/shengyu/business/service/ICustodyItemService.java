package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;

import java.util.List;

/**
 * 托管服务作业项服务层接口
 */
public interface ICustodyItemService extends IService<CustodyItem> {

    /**
     * 查询托管服务作业项列表
     *
     * @param custodyItem 托管服务作业项
     * @return 托管服务作业项集合
     */
    List<CustodyItemDto> selectList(CustodyItem custodyItem);

    /**
     * 获取指定ID的托管服务作业项对象
     *
     * @param id 托管服务作业项ID
     * @return 符合指定ID的托管服务作业项对象
     */
    CustodyItemDto selectById(Long id);

    /**
     * 新增托管服务作业项
     *
     * @param custodyItem 托管服务作业项
     * @return 新增结果
     */
    boolean saveCustodyItem(CustodyItem custodyItem);

    /**
     * 修改托管服务作业项
     *
     * @param custodyItem 托管服务作业项
     * @return 修改结果
     */
    boolean updateCustodyItem(CustodyItem custodyItem);

    /**
     * 根据ID列表删除托管服务作业项对象
     *
     * @param ids 托管服务作业项ID列表
     * @return 删除成功返回true，否则返回false
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 根据ID列表查询托管服务作业项对象
     *
     * @param ids 托管服务作业项ID列表
     * @return 符合指定ID的托管服务作业项对象集合
     */
    List<CustodyItemDto> selectList(List<Long> ids);
}