package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.mapper.BusinessMachineServeMapper;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class MachineServeServiceImpl extends ServiceImpl<BusinessMachineServeMapper, MachineServe>
        implements IMachineServeService {

    @Override
    public List<MachineServe> selectList(MachineServe machineServe) {
        return baseMapper.getList(machineServe);
    }

    @Override
    public MachineServe selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional
    public boolean saveMachineServe(MachineServe machineServe) {
        machineServe.setServeNo("MS" + SnowflakeIdUtils.getAsLong());
        return super.save(machineServe);
    }

    @Override
    @Transactional
    public boolean updateMachineServe(MachineServe machineServe) {
        MachineServe oldMachineServe = selectById(machineServe.getId());
        if (null == oldMachineServe) {
            throw new CustomException("农机服务不存在");
        }
        if (oldMachineServe.getStatus().equals(1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        return baseMapper.updateById(machineServe) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            MachineServe machineServe = this.getById(id);
            if (null == machineServe) {
                throw new CustomException("该操作项已不存在!");
            }
            if (machineServe.getStatus().equals(1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

}




