package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.mapper.CustodyServeMapper;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * 托管服务层实现
 */
@Service
public class CustodyServeServiceImpl
        extends ServiceImpl<CustodyServeMapper, CustodyServe> implements ICustodyServeService {

    @Autowired
    private ICustodyItemService custodyItemService;

    @Override
    public List<CustodyServeDto> selectList(CustodyServe custodyServe) {
        List<CustodyServeDto> list = baseMapper.selectCustodyFullList(custodyServe);
        List<CustodyServeDto> custodyServeDtos = new ArrayList<>();
        for (CustodyServe element : list) {
            custodyServeDtos.add(getCustodyServeDto(element));
        }
        return custodyServeDtos;
    }

    @Override
    public CustodyServeDto selectById(Long id) {
        CustodyServe custodyServe = this.getById(id);
        if (null == custodyServe) {
            throw new CustomException("该操作项已不存在!");
        }
        return getCustodyServeDto(custodyServe);
    }

    @Override
    @Transactional
    public boolean saveCustodyServe(CustodyServe custodyServe) {
        return baseMapper.insert(custodyServe) > 0;
    }

    @Override
    @Transactional
    public boolean updateCustodyServe(CustodyServe custodyServe) {
        CustodyServe oldCustodyServe = selectById(custodyServe.getId());
        if (oldCustodyServe.getStatus().equals(1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        return baseMapper.updateById(custodyServe) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            CustodyServe custodyServe = selectById(id);
            if (custodyServe.getStatus().equals(1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

    private CustodyServeDto getCustodyServeDto(CustodyServe custodyServe) {
        CustodyServeDto custodyServeDto = new CustodyServeDto();
        copyProperties(custodyServe, custodyServeDto);

        List<Long> itemsIds = Arrays.asList(Convert.toLongArray(custodyServe.getServiceItems()));
        List<CustodyItemDto> items = custodyItemService.selectList(itemsIds);
        custodyServeDto.setItems(items);
        return custodyServeDto;
    }

}