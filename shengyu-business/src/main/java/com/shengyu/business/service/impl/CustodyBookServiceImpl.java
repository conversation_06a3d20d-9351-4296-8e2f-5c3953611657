package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.mapper.CustodyBookMapper;
import com.shengyu.business.service.ICustodyBookService;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_custody_book(托管预定表)】的数据库操作Service实现
 * @createDate 2025-07-12 15:22:06
 */
@Service
public class CustodyBookServiceImpl extends ServiceImpl<CustodyBookMapper, CustodyBook>
        implements ICustodyBookService {

    @Autowired
    private ICustodyItemService custodyItemService;

    @Override
    public List<CustodyBookDto> selectList(CustodyBook custodyBook) {
        List<CustodyBookDto> list = baseMapper.selectCustodyBookList(custodyBook);
        List<CustodyBookDto> custodyBookDtos = new ArrayList<>();
        for (CustodyBookDto element : list) {
            custodyBookDtos.add(getCustodyBookDto(element));
        }
        return custodyBookDtos;
    }

    @Override
    public CustodyBookDto selectById(Long id) {
        return getCustodyBookDto(baseMapper.selectCustodyBookById(id));
    }

    @Override
    public CustodyBookDto selectByIdUserId(Long id, Long userId) {
        CustodyBookDto book = this.selectById(id);
        if (null == book || !userId.equals(book.getBookUserId())) {
            throw new CustomException("无权限操作");
        }
        return book;
    }


    @Override
    @Transactional
    public boolean saveCustodyBook(CustodyBook custodyBook) {
        custodyBook.setBookNo("SB" + SnowflakeIdUtils.getAsString());
        return baseMapper.insert(custodyBook) > 0;
    }


    @Override
    @Transactional
    public boolean updateByIdWithUserCheck(CustodyBook custodyBook, Long userId) {
        selectByIdUserId(custodyBook.getId(), userId);
        return this.updateById(custodyBook);
    }

    @Override
    @Transactional
    public boolean removeByIdWithUserCheck(Long id, Long userId) {
        selectByIdUserId(id, userId);
        return this.removeById(id);
    }

    private CustodyBookDto getCustodyBookDto(CustodyBookDto custodyBookDto) {
        List<Long> itemsIds = Arrays.asList(Convert.toLongArray(custodyBookDto.getItemId()));
        List<CustodyItemDto> items = custodyItemService.selectList(itemsIds);
        custodyBookDto.setItems(items);
        return custodyBookDto;
    }
}




