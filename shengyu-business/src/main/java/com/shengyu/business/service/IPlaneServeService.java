package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.PlaneServe;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_plane_serve(飞防服务表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IPlaneServeService extends IService<PlaneServe> {

    /**
     * 查询飞防服务列表
     *
     * @param planeServe 查询条件
     * @return 飞防服务列表
     */
    List<PlaneServe> selectList(PlaneServe planeServe);

    /**
     * 获取指定ID的飞防服务对象
     *
     * @param id 飞防服务ID
     * @return 符合指定ID的飞防服务对象
     */
    PlaneServe selectById(Long id);

    /**
     * 保存飞防服务
     *
     * @param planeServe 待保存的飞防服务对象
     * @return 是否保存成功
     */
    boolean savePlaneServe(PlaneServe planeServe);

    /**
     * 根据ID和用户校验后更新飞防服务
     *
     * @param planeServe 待更新的飞防服务对象
     * @return 是否更新成功
     */
    boolean updatePlaneServe(PlaneServe planeServe);

    /**
     * 根据ID批量删除飞防服务
     *
     * @param ids 待删除的ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
}
