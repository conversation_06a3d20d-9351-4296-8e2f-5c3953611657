package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Service
 * @createDate 2025-07-16 14:32:12
 */
public interface ISupplyDemandService extends IService<SupplyDemand> {

    /**
     * 查询供需信息列表
     *
     * @param supplyDemand 查询条件
     * @return 供需信息列表
     */
    List<SupplyDemandDto> selectList(SupplyDemand supplyDemand);

    /**
     * 根据ID查询供需信息
     *
     * @param id 供需信息ID
     * @return 供需信息详情
     */
    SupplyDemandDto selectOne(Long id);

    /**
     * 保存供需信息
     *
     * @param supplyDemand 待保存的供需信息对象
     * @return 是否保存成功
     */
    boolean saveSupplyDemand(SupplyDemand supplyDemand);

    /**
     * 根据ID和用户校验后更新供需信息
     *
     * @param supplyDemand 供需信息对象
     * @param userId       用户ID
     * @return 是否更新成功
     */
    boolean updateByIdUserId(SupplyDemand supplyDemand, Long userId);

    /**
     * app-根据ID和用户校验后删除供需信息
     *
     * @param id     供需信息ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByIdUserId(Long id, Long userId);



    /**
     * 根据ID查询供需信息
     *
     * @param id 供需信息ID
     * @return 供需信息详情
     */
    SupplyDemandDto selectValidById(Long id);

    /**
     * 根据ID和用户校验后查询供需信息
     * @param id 供需信息ID
     * @param userId 用户ID
     * @return 供需信息详情
     */
    SupplyDemandDto selectByIdUserId(Long id, Long userId);
}
