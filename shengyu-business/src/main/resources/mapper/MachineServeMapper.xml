<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shengyu.business.mapper.BusinessMachineServeMapper">

    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.MachineServe">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="serveNo" column="serve_no" jdbcType="VARCHAR"/>
        <result property="serveName" column="serve_name" jdbcType="VARCHAR"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="serveBanner" column="serve_banner" jdbcType="VARCHAR"/>
        <result property="serveDescription" column="serve_description" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ms.id,
        ms.serve_no,
        ms.serve_name,
        ms.serve_type,
        ms.status,
        ms.serve_banner,
        ms.serve_description,
        ms.delete_flag,
        ms.create_by,
        ms.create_time,
        ms.update_by,
        ms.update_time,
        ms.tenant_id,
        ms.remark
    </sql>

    <select id="getList" resultType="com.shengyu.business.domain.dto.MachineServeDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd.dict_label AS serveTypeName,
            dd2.dict_label AS statusName
        FROM t_business_machine_serve ms
            LEFT JOIN sys_dict_data dd
                ON ms.serve_type = dd.dict_value
                AND dd.dict_type = 'business_machine_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON ms.status = dd2.dict_value
                AND dd2.dict_type = 'business_shelf_status'
        WHERE ms.delete_flag = 0
            <if test="serveName != null and serveName != ''">
                AND ms.serve_name LIKE CONCAT('%', #{serveName}, '%')
            </if>
            <if test="serveType != null and serveType != ''">
                AND ms.serve_type = #{serveType}
            </if>
            <if test="status != null and status != ''">
                AND ms.status = #{status}
            </if>
            <if test="serveNo != null and serveNo != ''">
                AND ms.serve_no = #{serveNo}
            </if>
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.MachineServeDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd.dict_label AS serveTypeName,
            dd2.dict_label AS statusName
        FROM t_business_machine_serve ms
            LEFT JOIN sys_dict_data dd
                ON ms.serve_type = dd.dict_value
                AND dd.dict_type = 'business_machine_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON ms.status = dd2.dict_value
                AND dd2.dict_type = 'business_shelf_status'
        WHERE ms.id = #{id}
            AND ms.delete_flag = 0
    </select>

</mapper>