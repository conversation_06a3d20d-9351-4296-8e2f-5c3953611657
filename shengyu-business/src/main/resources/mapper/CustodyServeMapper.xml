<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.CustodyServeMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.CustodyServe">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="custodyCategory" column="custody_category" jdbcType="VARCHAR"/>
        <result property="serviceItems" column="service_items" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="subsidy" column="subsidy" jdbcType="DECIMAL"/>
        <result property="discount" column="discount" jdbcType="DECIMAL"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="cropType" column="crop_type" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="serviceProvider" column="service_provider" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        cs.id, cs.name, cs.custody_category, cs.service_items,
        cs.unit_price, cs.unit, cs.subsidy,
        cs.discount, cs.description, cs.tags,
        cs.crop_type, cs.image_url, cs.status,
        cs.service_provider, cs.province, cs.city,
        cs.county, cs.delete_flag, cs.create_by,
        cs.create_time, cs.update_by, cs.update_time,
        cs.tenant_id, cs.remark
    </sql>

    <select id="selectCustodyFullList" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        WHERE cs.delete_flag = 0
        <if test="status != null">
            AND cs.status = #{status}
        </if>
        <if test="cropType != null and cropType != ''">
            AND cs.crop_type = #{cropType}
        </if>
        <if test="province != null and province != ''">
            AND cs.province = #{province}
        </if>
        <if test="city != null and city != ''">
            AND cs.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND cs.county = #{county}
        </if>
        <if test="unitPrice != null">
            AND cs.unit_price = #{unitPrice}
        </if>
        <if test="name != null and name != ''">
            AND cs.name LIKE CONCAT(#{name}, '%')
        </if>
        ORDER BY cs.create_time DESC
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        WHERE cs.id = #{id}
        AND cs.delete_flag = 0
    </select>

    <select id="itemIdIsInServe" resultType="Integer">
        SELECT COUNT(1)
        FROM t_business_custody_serve
        WHERE delete_flag = 0
          AND FIND_IN_SET(#{singleId}, service_items) > 0
    </select>
</mapper>
