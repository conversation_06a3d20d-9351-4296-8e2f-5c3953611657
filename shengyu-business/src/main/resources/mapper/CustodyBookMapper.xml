<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.CustodyBookMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.CustodyBook">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="custodyCategory" column="custody_category" jdbcType="VARCHAR"/>
        <result property="custodyId" column="custody_id" jdbcType="BIGINT"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="addressRegion" column="address_region" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        cb.id,cb.book_no,cb.book_user_id,cb.custody_category,
        cb.custody_id,cb.item_id,cb.book_status,cb.contact_name,
        cb.phone,cb.id_card,cb.area,
        cb.address_region,cb.address_detail,cb.delete_flag,
        cb.create_time,cb.update_time,cb.tenant_id,
        cb.remark
    </sql>

    <select id="selectCustodyBookList" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>, cs.name as custodyName, dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        WHERE cb.delete_flag = 0
        <if test="bookNo != null and bookNo != ''">
            AND book_no LIKE CONCAT('%', #{bookNo}, '%')
        </if>
        <if test="custodyCategory != null and custodyCategory != ''">
            AND custody_category = #{custodyCategory}
        </if>
        <if test="bookStatus != null and bookStatus != ''">
            AND book_status = #{bookStatus}
        </if>
        <if test="contactName != null and contactName != ''">
            AND contact_name LIKE CONCAT('%', #{contactName}, '%')
        </if>
        <if test="phone != null and phone != ''">
            AND phone LIKE CONCAT('%', #{phone}, '%')
        </if>
        <if test="idCard != null and idCard != ''">
            AND id_card LIKE CONCAT('%', #{idCard}, '%')
        </if>
        <if test="addressRegion != null and addressRegion != ''">
            AND address_region LIKE CONCAT('%', #{addressRegion}, '%')
        </if>
        <if test="addressDetail != null and addressDetail != ''">
            AND address_detail LIKE CONCAT('%', #{addressDetail}, '%')
        </if>
    </select>

    <select id="selectCustodyBookById" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>, cs.name as custodyName, dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        WHERE cb.id = #{id}
    </select>
</mapper>
