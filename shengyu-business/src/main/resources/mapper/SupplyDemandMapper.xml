<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessSupplyDemandMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.SupplyDemand">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="infoTitle" column="info_title"/>
        <result property="infoType" column="info_type"/>
        <result property="cropType" column="crop_type"/>
        <result property="contactName" column="contact_name"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="idCard" column="id_card"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="description" column="description"/>
        <result property="publishTime" column="publish_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="reviewTime" column="review_time"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        sd.id,
        sd.user_id,
        sd.info_title,
        sd.info_type,
        sd.crop_type,
        sd.contact_name,
        sd.phone_number,
        sd.id_card,
        sd.province,
        sd.city,
        sd.district,
        sd.detailed_address,
        sd.description,
        sd.publish_time,
        sd.audit_status,
        sd.reviewer_id,
        sd.review_time,
        sd.tenant_id,
        sd.create_time,
        sd.update_time
    </sql>

    <select id="getList" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS infoTypeName,
        dd2.dict_label AS cropTypeName,
        dd3.dict_label AS auditStatusName
        FROM t_business_supply_demand sd
        LEFT JOIN sys_dict_data dd
        ON sd.info_type = dd.dict_value
        AND dd.dict_type = 'business_supply_demand_type'
        LEFT JOIN sys_dict_data dd2
        ON sd.crop_type = dd2.dict_value
        AND dd2.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd3
        ON sd.audit_status = dd3.dict_value
        AND dd3.dict_type = 'business_audit_status'
        WHERE 1 = 1
        <if test="infoTitle != null and infoTitle != ''">
            AND info_title LIKE CONCAT('%', #{infoTitle}, '%')
        </if>
        <if test="infoType != null and infoType != ''">
            AND info_type = #{infoType}
        </if>
        <if test="cropType != null and cropType != ''">
            AND crop_type = #{cropType}
        </if>
        <if test="contactName != null and contactName != ''">
            AND contact_name = #{contactName}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            AND phone_number = #{phoneNumber}
        </if>
    </select>

    <insert id="insert" parameterType="com.shengyu.business.domain.SupplyDemand">
        INSERT INTO t_business_supply_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="infoTitle != null">info_title,</if>
            <if test="infoType != null">info_type,</if>
            <if test="cropType != null">crop_type,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="idCard != null">id_card,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="detailedAddress != null">detailed_address,</if>
            <if test="description != null">description,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="reviewerId != null">reviewer_id,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="infoTitle != null">#{infoTitle},</if>
            <if test="infoType != null">#{infoType},</if>
            <if test="cropType != null">#{cropType},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="detailedAddress != null">#{detailedAddress},</if>
            <if test="description != null">#{description},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="reviewerId != null">#{reviewerId},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.shengyu.business.domain.SupplyDemand">
        UPDATE t_business_supply_demand
        <set>
            <if test="infoTitle != null">info_title = #{infoTitle},</if>
            <if test="infoType != null">info_type = #{infoType},</if>
            <if test="cropType != null">crop_type = #{cropType},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailedAddress != null">detailed_address = #{detailedAddress},</if>
            <if test="description != null">description = #{description},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <sql id="selectCommon">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS infoTypeName,
        dd2.dict_label AS cropTypeName,
        dd3.dict_label AS auditStatusName
        FROM t_business_supply_demand sd
        LEFT JOIN sys_dict_data dd
        ON sd.info_type = dd.dict_value
        AND dd.dict_type = 'business_supply_demand_type'
        LEFT JOIN sys_dict_data dd2
        ON sd.crop_type = dd2.dict_value
        AND dd2.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd3
        ON sd.audit_status = dd3.dict_value
        AND dd3.dict_type = 'business_audit_status'
        WHERE sd.id = #{id}
    </sql>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommon"/>
    </select>

    <select id="selectValidById" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommon"/>
        AND sd.delete_flag = 0 AND sd.audit_status = 'PASS'
    </select>
</mapper>
