<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.CustodyItemMapper">
    <!-- 结果映射 -->
    <resultMap id="CustodySingleResult" type="com.shengyu.business.domain.CustodyItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="subsidy" property="subsidy" jdbcType="DECIMAL"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"/>
        <result column="crop_type" property="cropType" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ci.id, ci.name, ci.content, ci.unit_price, ci.unit, ci.subsidy, ci.discount,
        ci.crop_type, ci.create_by, ci.delete_flag,
        ci.create_time, ci.update_time, ci.remark
    </sql>

    <select id="selectCustodyItemList" parameterType="Object"
            resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        left join sys_dict_data dd on ci.crop_type = dd.dict_value and dd.dict_type = 'business_crop_type'
        WHERE ci.delete_flag = 0
        <if test="name != null and name != ''">
            AND ci.name LIKE CONCAT(#{name}, '%')
        </if>
        <if test="cropType != null and cropType != ''">
            AND ci.crop_type = #{cropType}
        </if>
    </select>

    <select id="selectById" parameterType="Long" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        WHERE ci.id = #{id} AND ci.delete_flag = 0
    </select>

    <select id="selectList" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
    SELECT
    <if test="list !=null and list.size > 0">
        FIELD(ci.id,
        <foreach collection="list" item="id" separator=",">
            #{id}
        </foreach>
        ) AS `index`,
    </if>
    <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
    FROM t_business_custody_item ci
    LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
    WHERE ci.id IN
    <foreach collection="list" item="id" separator="," open="(" close=")">
        #{id}
    </foreach>
    ORDER BY `index`
</select>
</mapper>