<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.ServiceProviderMapper">
    <resultMap type="com.shengyu.business.domain.ServiceProvider" id="ServiceProviderResult">
        <result property="id" column="id"/>
        <result property="providerNo" column="provider_no"/>
        <result property="userId" column="user_id"/>
        <result property="providerName" column="provider_name"/>
        <result property="businessLicense" column="business_license"/>
        <result property="businessLicenseImage" column="business_license_image"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="legalPersonIdCard" column="legal_person_id_card"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="contactEmail" column="contact_email"/>
        <result property="businessAddress" column="business_address"/>
        <result property="registeredAddress" column="registered_address"/>
        <result property="businessScope" column="business_scope"/>
        <result property="providerType" column="provider_type"/>
        <result property="providerLevel" column="provider_level"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="approvalOpinion" column="approval_opinion"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalBy" column="approval_by"/>
        <result property="serviceArea" column="service_area"/>
        <result property="serviceCategory" column="service_category"/>
        <result property="introduction" column="introduction"/>
        <result property="logo" column="logo"/>
        <result property="score" column="score"/>
        <result property="serviceCount" column="service_count"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        sp.id,
        sp.provider_no, sp.user_id, sp.provider_name, sp.business_license, sp.business_license_image,
        sp.legal_person, sp.legal_person_id_card, sp.contact_person, sp.contact_phone, sp.contact_email,
        sp.business_address, sp.registered_address, sp.business_scope, sp.provider_type, sp.provider_level,
        sp.audit_status, sp.approval_opinion, sp.approval_time, sp.approval_by, sp.service_area, sp.service_category,
        sp.introduction, sp.logo, sp.score, sp.service_count, sp.delete_flag, sp.create_by, sp.create_time,
        sp.update_by, sp.update_time, sp.remark, sp.tenant_id
    </sql>

    <select id="selectServiceProviderList" parameterType="com.shengyu.business.domain.ServiceProvider"
            resultType="com.shengyu.business.domain.dto.ServiceProviderDto">
        select
        <include refid="Base_Column_List"/>
        , dd.dict_label as providerTypeName
        , dd2.dict_label as serviceCategoryName
        , dd3.dict_label as providerLevelName
        , dd4.dict_label as auditStatusName
        from t_mall_service_provider sp
        left join sys_dict_data dd on sp.provider_type = dd.dict_value and dd.dict_type = 'mall_service_provider_type'
        left join sys_dict_data dd2 on sp.service_category = dd2.dict_value and dd2.dict_type =
        'mall_service_category'
        left join sys_dict_data dd3 on sp.provider_level = dd3.dict_value and dd3.dict_type =
        'mall_service_provider_level'
        left join sys_dict_data dd4 on sp.audit_status = dd4.dict_value and dd4.dict_type = 'business_audit_status'
        where sp.delete_flag = '0'
        <if test="providerNo != null  and providerNo != ''">
            and sp.provider_no = #{providerNo}
        </if>
        <if test="providerName != null  and providerName != ''">
            and sp.provider_name like concat('%', #{providerName}, '%')
        </if>
        <if test="legalPerson != null  and legalPerson != ''">
            and sp.legal_person = #{legalPerson}
        </if>
        <if test="contactPerson != null  and contactPerson != ''">
            and sp.contact_person = #{contactPerson}
        </if>
        <if test="contactPhone != null  and contactPhone != ''">
            and sp.contact_phone = #{contactPhone}
        </if>
        <if test="contactEmail != null  and contactEmail != ''">
            and sp.contact_email = #{contactEmail}
        </if>
        <if test="businessAddress != null  and businessAddress != ''">
            and sp.business_address = #{businessAddress}
        </if>
        <if test="registeredAddress != null  and registeredAddress != ''">
            and sp.registered_address = #{registeredAddress}
        </if>
        <if test="businessScope != null  and businessScope != ''">
            and sp.business_scope = #{businessScope}
        </if>
        <if test="providerType != null  and providerType != ''">
            and sp.provider_type = #{providerType}
        </if>
        <if test="auditStatus != null  and auditStatus != ''">
            and sp.audit_status = #{auditStatus}
        </if>
        <if test="approvalBy != null  and approvalBy != ''">
            and sp.approval_by = #{approvalBy}
        </if>
        <if test="serviceArea != null  and serviceArea != ''">
            and sp.service_area = #{serviceArea}
        </if>
        <if test="serviceCategory != null  and serviceCategory != ''">
            and sp.service_category = #{serviceCategory}
        </if>
        <if test="introduction != null  and introduction != ''">
            and sp.introduction = #{introduction}
        </if>
    </select>

    <select id="selectServiceProviderById" parameterType="Long"
            resultType="com.shengyu.business.domain.dto.ServiceProviderDto">
        select
        <include refid="Base_Column_List"/>
        , dd.dict_label as providerTypeName
        , dd2.dict_label as serviceCategoryName
        , dd3.dict_label as providerLevelName
        , dd4.dict_label as auditStatusName
        from t_mall_service_provider sp
        left join sys_dict_data dd on sp.provider_type = dd.dict_value and dd.dict_type = 'mall_service_provider_type'
        left join sys_dict_data dd2 on sp.service_category = dd2.dict_value and dd2.dict_type = 'mall_service_category'
        left join sys_dict_data dd3 on sp.provider_level = dd3.dict_value and dd3.dict_type =
        'mall_service_provider_level'
        left join sys_dict_data dd4 on sp.audit_status = dd4.dict_value and dd4.dict_type = 'business_audit_status'
        where sp.id = #{id} and sp.delete_flag = '0'
    </select>
</mapper>