<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessMachineBookMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.MachineBook" autoMapping="true">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="serveId" column="serve_id" jdbcType="BIGINT"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="plotCount" column="plot_count" jdbcType="TINYINT"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="addressRegion" column="address_region" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        mb.id,
        mb.book_no,
        mb.book_user_id,
        mb.serve_type,
        mb.serve_id,
        mb.book_status,
        mb.contact_name,
        mb.phone,
        mb.id_card,
        mb.plot_count,
        mb.area,
        mb.address_region,
        mb.address_detail,
        mb.delete_flag,
        mb.create_time,
        mb.update_time,
        mb.tenant_id,
        mb.remark
    </sql>

    <select id="getList" resultType="com.shengyu.business.domain.dto.MachineBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        type_dict.dict_label AS serveTypeName,
        status_dict.dict_label AS bookStatusName
        FROM t_business_machine_book mb
        LEFT JOIN sys_dict_data type_dict
        ON mb.serve_type = type_dict.dict_value
        AND type_dict.dict_type = 'business_machine_serve_type'
        LEFT JOIN sys_dict_data status_dict
        ON mb.book_status = status_dict.dict_value
        AND status_dict.dict_type = 'business_book_status'
        WHERE mb.delete_flag = 0
        <if test="bookNo != null and bookNo != ''">
            AND mb.book_no = #{bookNo}
        </if>
        <!-- 其他条件保持不变 -->
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.MachineBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        type_dict.dict_label AS serveTypeName,
        status_dict.dict_label AS bookStatusName
        FROM t_business_machine_book mb
        LEFT JOIN sys_dict_data type_dict
        ON mb.serve_type = type_dict.dict_value
        AND type_dict.dict_type = 'business_machine_serve_type'
        LEFT JOIN sys_dict_data status_dict
        ON mb.book_status = status_dict.dict_value
        AND status_dict.dict_type = 'business_book_status'
        WHERE mb.id = #{id}
    </select>
</mapper>
