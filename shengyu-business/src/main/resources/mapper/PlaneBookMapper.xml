<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shengyu.business.mapper.BusinessPlaneBookMapper">

    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.PlaneBook">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="serveId" column="serve_id" jdbcType="BIGINT"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="plotCount" column="plot_count" jdbcType="TINYINT"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="addressRegion" column="address_region" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        pb.id,
        pb.book_no,
        pb.book_user_id,
        pb.serve_type,
        pb.serve_id,
        pb.book_status,
        pb.contact_name,
        pb.phone,
        pb.id_card,
        pb.plot_count,
        pb.area,
        pb.address_region,
        pb.address_detail,
        pb.delete_flag,
        pb.create_time,
        pb.update_time,
        pb.tenant_id,
        pb.remark
    </sql>

    <select id="getList" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd1.dict_label AS serveTypeName,
            dd2.dict_label AS bookStatusName
        FROM t_business_plane_book pb
            LEFT JOIN sys_dict_data dd1
                ON pb.serve_type = dd1.dict_value
                AND dd1.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON pb.book_status = dd2.dict_value
                AND dd2.dict_type = 'business_book_status'
        WHERE 1 = 1
            <if test="serveType != null and serveType != ''">
                AND pb.serve_type = #{serveType}
            </if>
            <if test="bookStatus != null and bookStatus != ''">
                AND pb.book_status = #{bookStatus}
            </if>
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd1.dict_label AS serveTypeName,
            dd2.dict_label AS bookStatusName
        FROM t_business_plane_book pb
            LEFT JOIN sys_dict_data dd1
                ON pb.serve_type = dd1.dict_value
                AND dd1.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON pb.book_status = dd2.dict_value
                AND dd2.dict_type = 'business_book_status'
        WHERE pb.id = #{id}
    </select>

</mapper>