package com.shengyu.api.controller.system;

import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.entity.SysMenu;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.system.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "登录-APP")
@RestController
public class ApiLoginController {
    @Autowired
    private ISysMenuService menuService;

    @ApiOperation(value = "获取菜单列表")
    @PostMapping("/app/getRouters")
    public AjaxResult<Map<String, Object>> getRouters() {
        List<SysMenu> menuList = menuService.getAppMenuByUser(SecurityUtils.getUsername());
        Map<String, Object> data = new HashMap<>();
        data.put("menu", menuList);
        return AjaxResult.success(data);
    }

}
