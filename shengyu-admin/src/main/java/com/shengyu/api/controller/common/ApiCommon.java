package com.shengyu.api.controller.common;

import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysAppInfo;
import com.shengyu.system.domain.vo.AreaTreeVo;
import com.shengyu.system.service.ISysAppInfoService;
import com.shengyu.system.service.ISysDeptService;
import com.shengyu.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: SS
 * @Date: 2023/02/14/17:17
 * @Description:
 */
@Api(tags = "公共接口调用")
@RestController
public class ApiCommon {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysAppInfoService sysAppInfoService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysDictDataService dictDataService;

    @ApiOperation(value = "省市县-行政区划tree")
    @PostMapping("/getStandardAreaTree")
    public AjaxResult<List<AreaTreeVo>> getStandardAreaTree() {
        List<AreaTreeVo> areaTreeVoList = redisCache.getCacheObject(Constants.AREA);
        return AjaxResult.success(areaTreeVoList);
    }

    @ApiOperation(value = "获取当前租户系统自定义名称")
    @PostMapping("/getSystemName")
    public AjaxResult<String> getSystemName(HttpServletRequest request) {
        String tenantId = SecurityUtils.getLoginUser().getTenantId();
        String redisKey = Constants.SYS_CONFIG_KEY + "system.name_"
                + (StringUtils.isEmpty(tenantId) ? "" : tenantId);
        String name = redisCache.getCacheObject(redisKey);
        if (StringUtils.isEmpty(name)) {
            name = "农事服务后台管理系统";
        }
        return AjaxResult.success(name);
    }


    @ApiOperation(value = "获取当前最新app")
    @PostMapping("/getNewApp")
    public AjaxResult<SysAppInfo> getNewApp() {
        return AjaxResult.success(sysAppInfoService.getLastApp());
    }

    @ApiOperation(value = "乡镇列表")
    @PostMapping("/townList")
    public AjaxResult<List<SysDept>> townList() {
        List<SysDept> tList = sysDeptService.getTownList();
        return AjaxResult.success(tList);
    }

    @ApiOperation(value = "获取当前系统配置-值")
    @PostMapping("/getConfigValue")
    public AjaxResult<String> getConfigValue(String code) {
        Object val = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + code + "_" + ConfigUtils.getTenantId());
        if (StringUtils.isNull(val) && code.equals("sys.ledger.level")) {
            val = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + code + "_0");
        }
        if (val != null) {
            return AjaxResult.success("suc", val.toString());
        }
        return AjaxResult.success("suc", null);//未找到配置
    }

}
