package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.poi.ExcelUtil;
import com.shengyu.framework.web.service.TokenService;
import com.shengyu.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 组织架构信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
@Api(tags = "组织架构")
public class SysDeptController extends BaseController {
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 获取组织架构列表
     */
    @ApiOperation(value = "获取组织架构列表")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @PostMapping("/list")
    public TableDataInfo<SysDept> list(SysDept dept) {
        startPage();
//        if (StringUtils.isEmpty(dept.getTenantId())) {
//            dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
//        }
        List<SysDept> depts = deptService.selectDeptList(dept);
        return getDataTable(depts);
    }

    /**
     * 查询组织架构列表（排除节点）
     */
    @ApiOperation(value = "查询组织架构列表（排除节点）")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @PostMapping("/list/exclude")
    public AjaxResult excludeChild(@RequestParam Long deptId) {
        SysDept dept = new SysDept();
//        dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        List<SysDept> depts = deptService.selectDeptList(dept);
        Iterator<SysDept> it = depts.iterator();
        while (it.hasNext()) {
            SysDept d = it.next();
            if (d.getDeptId().intValue() == deptId
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + "")) {
                it.remove();
            }
        }
        return AjaxResult.success(depts);
    }

    @ApiOperation(value = "组织架构管理导入")
    @Log(title = "组织架构管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:dept:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<SysDept> util = new ExcelUtil<>(SysDept.class);
        List<SysDept> list = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = deptService.importDept(list, operName);
        return AjaxResult.success(message);
    }

    /**
     * 根据组织架构编号获取详细信息
     */
    @ApiOperation(value = "根据组织架构编号获取详细信息")
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<SysDept> getInfo(@RequestParam Long deptId) {
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取组织架构下拉树列表
     */
    @ApiOperation(value = "获取组织架构下拉树列表-接口（如无特殊说明，统一调取该接口作为组织架构树）")
    @PostMapping("/treeSelect")
    public AjaxResult<List<TreeSelect>> treeSelect(SysDept dept) {
        List<TreeSelect> list = redisCache.getCacheObject("temp_depts_" + SecurityUtils.getLoginUser().getUsername());
        if (StringUtils.isNotEmpty(list)) {
            return AjaxResult.success(list);
        }
//        if (StringUtils.isEmpty(dept.getTenantId())) {
//            dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
//        }
        dept.setStatus("1");//正常状态
        String deptNature = redisCache.getCacheObject(
                Constants.SYS_CONFIG_KEY + "deptNature_" + SecurityUtils.getLoginUser().getTenantId());
        if (StringUtils.isNotEmpty(deptNature)) {
            dept.getParams().put("deptNature", deptNature);
        }
        List<SysDept> depts = deptService.selectDeptList(dept);
        List<TreeSelect> treeList = deptService.buildDeptTreeSelect(depts);
        redisCache.setCacheObject("temp_depts_" + SecurityUtils.getLoginUser().getUsername(), treeList, 4, TimeUnit.HOURS);
        return AjaxResult.success(treeList);
    }

    @ApiOperation(value = "获取组织架构下拉树列表-(该接口树，包含当前节点含有的最大子节点层数)")
    @PostMapping("/treeSelect2")
    public AjaxResult<Map<String, Object>> treeSelect2(SysDept dept) {
//        if (StringUtils.isEmpty(dept.getTenantId())) {
//            dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
//        }
        dept.setStatus("1");//正常状态
        List<SysDept> depts = deptService.selectDeptList(dept);
        Map<String, Object> data = new HashMap<>();
        List<TreeSelect> list = deptService.buildDeptTreeSelect(depts);
        data.put("tree", list);
        if (StringUtils.isNotEmpty(list)) {
            list.forEach(t -> {
                if (StringUtils.isNotEmpty(t.getChildren())) {
                    Set<Integer> sets = new HashSet<>();
                    getMaxLevel(t.getChildren(), sets);
                    data.put("allLevel", sets.stream().max(Comparator.comparingInt(Integer::shortValue)).get() - t.getLevel() + 1);
                } else {
                    data.put("allLevel", 1);
                }
            });
        } else {
            data.put("allLevel", 0);
        }
        return AjaxResult.success(data);
    }

    /**
     * 获取组织架构下拉树列表
     */
    @ApiOperation(value = "获取组织架构下拉树列表-(该接口树为全组织架构树，无特殊要求，请不要调用该接口)")
    @PostMapping("/completeTree")
    public AjaxResult<List<TreeSelect>> completeTree(SysDept dept) {
//        if (StringUtils.isEmpty(dept.getTenantId())) {
//            dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
//        }
        List<TreeSelect> list = redisCache.getCacheObject("temp_complete_depts_" + SecurityUtils.getLoginUser().getUsername());
        if (StringUtils.isNotEmpty(list)) {
            return AjaxResult.success(list);
        }
        dept.setStatus("1");//正常状态
        List<SysDept> depts = deptService.selectDeptList(dept);
        List<SysDept> treeDepts = deptService.buildDeptTree(depts);
        if (StringUtils.isNotEmpty(depts)) {
            Map<String, SysDept> maps = new HashMap<>();
            for (SysDept d : treeDepts) {
                if (d.getLevel() > 1 && StringUtils.isNull(maps.get(String.valueOf(d.getParentId())))) {
                    SysDept pD = deptService.selectDeptById(d.getParentId());
                    if (StringUtils.isNotNull(pD)) {
                        maps.put(String.valueOf(d.getParentId()), pD);
                        if (d.getLevel() > 2) {
                            SysDept pD_ = deptService.selectDeptById(pD.getParentId());
                            if (StringUtils.isNotNull(pD_)) {
                                maps.put(String.valueOf(pD.getParentId()), pD_);
                                if (d.getLevel() > 3) {
                                    SysDept pD2_ = deptService.selectDeptById(pD_.getParentId());
                                    if (StringUtils.isNotNull(pD2_)) {
                                        maps.put(String.valueOf(pD_.getParentId()), pD2_);
                                        if (d.getLevel() > 4) {
                                            SysDept pD3_ = deptService.selectDeptById(pD2_.getParentId());
                                            if (StringUtils.isNotNull(pD3_)) {
                                                maps.put(String.valueOf(pD2_.getParentId()), pD3_);
                                                if (d.getLevel() > 5) {
                                                    SysDept pD4_ = deptService.selectDeptById(pD3_.getParentId());
                                                    if (StringUtils.isNotNull(pD4_)) {
                                                        maps.put(String.valueOf(pD3_.getParentId()), pD4_);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            for (String s : maps.keySet()) {
                depts.add(maps.get(s));
            }
        }
        List<TreeSelect> treeList = deptService.buildDeptTreeSelect(depts);
        redisCache.setCacheObject("temp_complete_depts_" + SecurityUtils.getLoginUser().getUsername(), treeList, 4, TimeUnit.HOURS);
        return AjaxResult.success(treeList);
    }

    private void getMaxLevel(List<TreeSelect> c, Set<Integer> sets) {
        for (TreeSelect t : c) {
            sets.add(t.getLevel());
            if (StringUtils.isNotEmpty(t.getChildren())) {
                getMaxLevel(t.getChildren(), sets);
            }
        }
    }

    @ApiOperation(value = "获取组织架构下拉树列表")
    @PostMapping("/treeByDeptId")
    public AjaxResult<List<TreeSelect>> treeByDeptId(@RequestParam Long deptId) {
        List<SysDept> deptS = deptService.selectChildrenDeptById(deptId);
        return AjaxResult.success(deptService.buildDeptTreeSelect(deptS));
    }

    /**
     * 加载对应角色组织架构列表树
     */
    @ApiOperation(value = "加载对应角色组织架构列表树")
    @PostMapping(value = "/roleDeptTreeSelect")
    public MapResult roleDeptTreeSelect(@RequestParam Long roleId) {
        SysDept dept = new SysDept();
        dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        List<SysDept> depts = deptService.selectDeptList(dept);
        MapResult ajax = MapResult.success();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.buildDeptTreeSelect(depts));
        return ajax;
    }

    /**
     * 新增组织架构
     */
    @ApiOperation(value = "新增组织架构")
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "组织架构管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptCodeUnique(dept))) {
            return AjaxResult.error("新增组织架构'" + dept.getDeptName() + "'失败，组织架构编码已存在");
        }
        dept.setCreateBy(SecurityUtils.getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改组织架构
     */
    @ApiOperation(value = "修改组织架构")
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "组织架构管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptCodeUnique(dept))) {
            return AjaxResult.error("修改组织架构'" + dept.getDeptName() + "'失败，组织架构编码已存在");
        } else if (dept.getParentId().equals(dept.getDeptId())) {
            return AjaxResult.error("修改组织架构'" + dept.getDeptName() + "'失败，上级组织架构不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())
                && deptService.selectNormalChildrenDeptById(dept.getDeptId()) > 0) {
            return AjaxResult.error("该组织架构包含未停用的子组织架构！");
        }
        dept.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除组织架构
     */
    @ApiOperation(value = "删除组织架构")
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "组织架构管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return AjaxResult.error("存在下级组织架构,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return AjaxResult.error("组织架构存在用户,不允许删除");
        }
        return toAjax(deptService.deleteDeptById(deptId));
    }

    @ApiOperation(value = "统计组织架构业务组织数量")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @PostMapping("/getBusinessMap")
    public AjaxResult<Map<String, Object>> getBusinessMap(SysDept dept) {
        Map<String, Object> res = new HashMap<>();
        res.put("town", 0);
        res.put("village", 0);
        res.put("bsTown", 0);
        res.put("bsVillage", 0);
        res.put("bsGroup", 0);
        res.put("codeCounts", 0);
        res.put("codeGroup", 0);
        res.put("codeVillage", 0);
        res.put("codeTown", 0);
        List<SysDept> list = deptService.selectDeptList(dept);
        if (StringUtils.isNotEmpty(list)) {
            res.put("town", list.stream().filter(d -> d.getLevel() == 4 && !"1".equals(d.getIsBusiness())).count());
            res.put("village", list.stream().filter(d -> d.getLevel() == 5 && !"1".equals(d.getIsBusiness())).count());
            res.put("bsTown", list.stream().filter(d -> "1".equals(d.getNature()) && "1".equals(d.getIsBusiness())).count());
            res.put("bsVillage", list.stream().filter(d -> "2".equals(d.getNature()) && "1".equals(d.getIsBusiness())).count());
            res.put("bsGroup", list.stream().filter(d -> "3".equals(d.getNature()) && "1".equals(d.getIsBusiness())).count());
            res.put("codeCounts", list.stream().filter(d -> StringUtils.isNotNull(d.getCodeId()) && "1".equals(d.getIsBusiness())).count());
            res.put("codeGroup", list.stream().filter(d -> StringUtils.isNotEmpty(d.getCreditCode())
                    && "1".equals(d.getIsBusiness()) && "3".equals(d.getNature())).count());
            res.put("codeVillage", list.stream().filter(d -> StringUtils.isNotEmpty(d.getCreditCode())
                    && "1".equals(d.getIsBusiness()) && "2".equals(d.getNature())).count());
            res.put("codeTown", list.stream().filter(d -> StringUtils.isNotEmpty(d.getCreditCode())
                    && "1".equals(d.getIsBusiness()) && "1".equals(d.getNature())).count());
        }
        return AjaxResult.success(res);
    }
}
