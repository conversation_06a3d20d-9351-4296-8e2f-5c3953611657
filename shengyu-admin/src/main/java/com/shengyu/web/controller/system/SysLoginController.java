package com.shengyu.web.controller.system;

import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.HttpStatus;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.core.domain.entity.SysMenu;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.domain.model.LoginBody;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.framework.web.service.SysLoginService;
import com.shengyu.framework.web.service.SysPermissionService;
import com.shengyu.framework.web.service.TokenService;
import com.shengyu.system.domain.vo.MetaVo;
import com.shengyu.system.domain.vo.RouterVo;
import com.shengyu.system.service.ISysDeptService;
import com.shengyu.system.service.ISysMenuService;
import com.xxl.sso.core.login.SsoTokenLoginHelper;
import com.xxl.sso.core.user.XxlSsoUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "登录")
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysDeptService deptService;


    /**
     * 登录方法
     *
     * @return 结果
     */
    @ApiOperation(value = "系统管理后台登录")
    @PostMapping("/login")
    public MapResult login(@RequestBody LoginBody loginBody, HttpServletRequest request, HttpServletResponse response) {
        MapResult ajax = MapResult.success();
        String token = loginService.login(loginBody);
        ajax.put(Constants.TOKEN, token);
        LoginUser loginUser = tokenService.getLoginUser(token);
        SysUser user = loginUser.getUser();
        int needUpdDays = Convert.toInt(redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "system.updatePassword" +
                ".times"), 90);
        if (!user.isAdmin()) {
            if (StringUtils.isNull(user.getLastUpdPwdTime())
                    || DateUtils.getSubDay(new Date(), user.getLastUpdPwdTime()) >= needUpdDays) {
                ajax.put("isUpdPwd", true);
            } else {
                ajax.put("isUpdPwd", false);
            }
        }
        return ajax;
    }

    @ApiOperation(value = "系统管理后台登录")
    @PostMapping("/appLogin")
    public MapResult loginApp(HttpServletRequest request) {
        MapResult ajax = MapResult.success();
        XxlSsoUser xxlUser = SsoTokenLoginHelper.loginCheck(request);
        if (StringUtils.isNull(xxlUser)) {
            throw new CustomException("令牌已过期", HttpStatus.UNAUTHORIZED);
        }
        String idCard = xxlUser.getUserid();
        //重新登录后 删除临时组织机构缓存
        redisCache.deleteObject("temp_depts_" + xxlUser.getUsername());
        redisCache.deleteObject("temp_complete_depts_" + xxlUser.getUsername());
        String token = loginService.login(idCard);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @PostMapping("getInfo")
    public MapResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        MapResult ajax = MapResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @PostMapping("getRouters")
    public AjaxResult<List<RouterVo>> getRouters(HttpServletRequest request) {
        String key = request.getHeader("systemKey");
        if (StringUtils.isEmpty(key)) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 用户信息
            SysUser user = loginUser.getUser();
            List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
            return AjaxResult.success(menuService.buildMenus(menus));
        }
        List<SysMenu> menus = menuService.selectMenuTreeByKey(key);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @PostMapping("getIndexRouters")
    public AjaxResult<List<RouterVo>> getIndexRouters(HttpServletRequest request) {
        String key = request.getHeader("systemKey");
        if (StringUtils.isEmpty(key)) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 用户信息
            SysUser user = loginUser.getUser();
            List<SysMenu> menus = menuService.selectMenuTreeByUserIdNoTree(user.getUserId());
            if (StringUtils.isEmpty(menus)) {
                return AjaxResult.success();
            }
            return AjaxResult.success(menuService.buildMenus(getIndexList(menus)));
        }
        List<SysMenu> menus = menuService.selectMenuTreeByKeyNoTree(key);
        if (StringUtils.isEmpty(menus)) {
            return AjaxResult.success();
        }
        return AjaxResult.success(menuService.buildMenus(getIndexList(menus)));
    }

    private List<SysMenu> getIndexList(List<SysMenu> menus) {
        List<MetaVo> pList = new ArrayList<>();
        SysMenu tmpMenu = menus.get(0);
        tmpMenu.setParentId(0L);
        tmpMenu.setMenuType("M");
        List<SysMenu> finalList = new ArrayList<>();
        for (MetaVo v : pList) {
            SysMenu pM = new SysMenu();
            BeanUtils.copyProperties(tmpMenu, pM);
            pM.setMenuName(v.getTitle());
            finalList.add(pM);
        }
        return finalList;
    }
}
