package com.shengyu.web.controller.business;

import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 农机服务
 */
@Api(tags = "农机服务")
@RestController
@RequestMapping("/machine/serve")
public class BusinessMachineServeController extends BaseController {

    @Resource
    private IMachineServeService machineServeService;

    /**
     * 农机服务
     */
    @ApiOperation(value = "农机服务--列表")
    @PreAuthorize("@ss.hasPermi('machine:serve:list')")
    @PostMapping("/list")
    public TableDataInfo<MachineServe> list(MachineServe machineServe) {
        startPage();
        List<MachineServe> list = machineServeService.selectList(machineServe);
        return getDataTable(list);
    }

    /**
     * 农机服务--详情
     */
    @ApiOperation(value = "农机服务--详情")
    @PreAuthorize("@ss.hasPermi('machine:serve:query')")
    @PostMapping("/query")
    public AjaxResult<MachineServe> query(@RequestParam Long id) {
        return AjaxResult.success(machineServeService.selectById(id));
    }

    /**
     * 农机服务--详情
     */
    @ApiOperation(value = "农机服务--新增")
    @PreAuthorize("@ss.hasPermi('machine:serve:add')")
    @Log(title = "农机服务", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult<String> add(@RequestBody MachineServe machineServe) {
        return toAjax(machineServeService.saveMachineServe(machineServe));
    }

    /**
     * 农机服务--详情
     */
    @ApiOperation(value = "农机服务--编辑")
    @PreAuthorize("@ss.hasPermi('machine:serve:edit')")
    @Log(title = "农机服务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<String> edit(@RequestBody MachineServe machineServe) {
        return toAjax(machineServeService.updateMachineServe(machineServe));
    }

    /**
     * 农机服务--上架、下架
     */
    @ApiOperation(value = "农机服务--上架、下架")
    @PreAuthorize("@ss.hasPermi('machine:serve:status')")
    @Log(title = "农机服务", businessType = BusinessType.UPDATE)
    @PostMapping("/editStatus")
    public AjaxResult<String> editStatus(@RequestBody MachineServe machineServe) {
        return toAjax(machineServeService.updateById(machineServe));
    }

    /**
     * 农机服务--详情
     */
    @ApiOperation(value = "农机服务--删除")
    @PreAuthorize("@ss.hasPermi('machine:serve:remove')")
    @Log(title = "农机服务", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult<String> remove(Long[] ids) {
        return toAjax(machineServeService.removeByIds(Arrays.asList(ids)));
    }
}
