package com.shengyu.web.controller.business;

import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.shengyu.business.service.ICustodyBookService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 托管预定
 */
@Api(tags = "托管预定")
@RestController
@RequestMapping("/custody/book")
public class BusinessCustodyBookController extends BaseController {

    @Resource
    private ICustodyBookService custodyBookService;

    /**
     * 托管预定
     */
    @ApiOperation(value = "托管预定--列表")
    @PreAuthorize("@ss.hasPermi('custody:book:list')")
    @PostMapping("/list")
    public TableDataInfo<CustodyBookDto> list(CustodyBook custodyBook) {
        startPage();
        List<CustodyBookDto> list = custodyBookService.selectList(custodyBook);
        return getDataTable(list);
    }

    /**
     * 托管预定--详情
     */
    @ApiOperation(value = "托管预定--详情")
    @PreAuthorize("@ss.hasPermi('custody:book:query')")
    @PostMapping("/query")
    public AjaxResult<CustodyBookDto> query(@RequestParam Long id) {
        return AjaxResult.success(custodyBookService.selectById(id));
    }

    /**
     * 托管预定--处理
     */
    @ApiOperation(value = "托管预定--处理")
    @PreAuthorize("@ss.hasPermi('custody:book:deal')")
    @Log(title = "托管预定--处理", businessType = BusinessType.UPDATE)
    @PostMapping("/deal")
    public AjaxResult<String> deal(@RequestBody CustodyBook custodyBook) {
        return toAjax(custodyBookService.updateById(custodyBook));
    }

}
