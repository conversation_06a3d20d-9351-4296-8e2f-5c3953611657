package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.HelpDocument;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.system.service.IHelpDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 帮助文档Controller
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@RestController
@RequestMapping("/sys/document")
@Api(tags = "帮助文档")
public class HelpDocumentController extends BaseController {

    @Autowired
    private IHelpDocumentService helpDocumentService;

    /**
     * 查询帮助文档列表
     */
    @ApiOperation(value = "查询帮助文档列表")
    @PreAuthorize("@ss.hasPermi('sys:document:list')")
    @PostMapping("/list")
    public AjaxResult<List<HelpDocument>> list(HelpDocument helpDocument) {
        List<HelpDocument> list = helpDocumentService.selectHelpDocumentList(helpDocument);
        return AjaxResult.success(list);
    }


    /**
     * 获取帮助文档详细信息
     */
    @ApiOperation(value = "获取帮助文档详细信息")
    @PreAuthorize("@ss.hasPermi('sys:document:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult getInfo(@RequestParam Long id) {
        return AjaxResult.success(helpDocumentService.selectHelpDocumentById(id));
    }

    /**
     * 新增帮助文档
     */
    @ApiOperation(value = "新增帮助文档")
    @PreAuthorize("@ss.hasPermi('sys:document:add')")
    @Log(title = "帮助文档", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HelpDocument helpDocument) {
        return toAjax(helpDocumentService.insertHelpDocument(helpDocument));
    }

    /**
     * 修改帮助文档
     */
    @ApiOperation(value = "修改帮助文档")
    @PreAuthorize("@ss.hasPermi('sys:document:edit')")
    @Log(title = "帮助文档", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HelpDocument helpDocument) {
        return toAjax(helpDocumentService.updateHelpDocument(helpDocument));
    }

    /**
     * 删除帮助文档
     */
    @ApiOperation(value = "删除帮助文档")
    @PreAuthorize("@ss.hasPermi('sys:document:remove')")
    @Log(title = "帮助文档", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long id) {
        if (helpDocumentService.hasChildById(id)) {
            return AjaxResult.error("有子节点数据，不可删除!");
        }
        return toAjax(helpDocumentService.deleteHelpDocumentById(id));
    }

    @ApiOperation(value = "获取组织架构下拉树列表-接口（如无特殊说明，统一调取该接口作为组织架构树）")
    @PostMapping("/treeSelect")
    public AjaxResult<List<TreeSelect>> treeSelect(HelpDocument document) {
        List<HelpDocument> ds = helpDocumentService.selectHelpDocumentList(document);
        return AjaxResult.success(helpDocumentService.buildTreeSelect(ds));
    }
}
