package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.system.domain.SysArea;
import com.shengyu.system.service.ISysAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 省市县管理Controller
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
@Api(tags = "行政区划")
@RestController
@RequestMapping("/system/area")
public class SysAreaController extends BaseController {
    @Autowired
    private ISysAreaService sysAreaService;

    /**
     * 查询省市县管理列表
     */
    @ApiOperation(value = "省市县级联管理--省市县列表")
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @PostMapping("/list")
    public TableDataInfo<SysArea> list(SysArea sysArea) {
        startPage();
        List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
        return getDataTable(list);
    }

    /**
     * 获取省市县管理详细信息
     */
    @ApiOperation(value = "省市县级联管理--详细信息")
    @PreAuthorize("@ss.hasPermi('system:area:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<SysArea> getInfo(@RequestParam Long id) {
        return AjaxResult.success(sysAreaService.selectSysAreaById(id));
    }

    /**
     * 新增省市县管理
     */
    @ApiOperation(value = "省市县级联管理--新增")
    @PreAuthorize("@ss.hasPermi('system:area:add')")
    @Log(title = "省市县管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysArea sysArea) {
        return toAjax(sysAreaService.insertSysArea(sysArea));
    }

    /**
     * 修改省市县管理
     */
    @ApiOperation(value = "省市县级联管理--修改")
    @PreAuthorize("@ss.hasPermi('system:area:edit')")
    @Log(title = "省市县管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysArea sysArea) {
        return toAjax(sysAreaService.updateSysArea(sysArea));
    }

    /**
     * 删除省市县管理
     */
    @ApiOperation(value = "省市县级联管理--删除")
    @PreAuthorize("@ss.hasPermi('system:area:remove')")
    @Log(title = "省市县管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] ids) {
        return toAjax(sysAreaService.deleteSysAreaByIds(ids));
    }
}
