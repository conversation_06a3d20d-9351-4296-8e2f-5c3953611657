package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysAppInfo;
import com.shengyu.system.service.ISysAppInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app管理Controller
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Api(tags = "app管理")
@RestController
@RequestMapping("/system/appInfo")
public class SysAppInfoController extends BaseController {
    @Autowired
    private ISysAppInfoService sysAppInfoService;

    /**
     * 查询app管理列表
     */
    @ApiOperation("查询app管理列表")
    @PreAuthorize("@ss.hasPermi('system:appInfo:list')")
    @PostMapping("/list")
    public TableDataInfo<SysAppInfo> list(SysAppInfo sysAppInfo) {
        startPage();
        if (StringUtils.isEmpty(sysAppInfo.getTenantId()) && !SecurityUtils.getLoginUser().getUser().isAdmin()) {
            sysAppInfo.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        List<SysAppInfo> list = sysAppInfoService.selectSysAppInfoList(sysAppInfo);
        return getDataTable(list);
    }

    @ApiOperation("app版本list")
    @PostMapping("/allList")
    public AjaxResult<List<SysAppInfo>> allList(SysAppInfo sysAppInfo) {
        if (StringUtils.isEmpty(sysAppInfo.getTenantId()) && !SecurityUtils.getLoginUser().getUser().isAdmin()) {
            sysAppInfo.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        List<SysAppInfo> list = sysAppInfoService.selectSysAppInfoList(sysAppInfo);
        return AjaxResult.success(list);
    }

    /**
     * 获取app管理详细信息
     */
    @ApiOperation("获取app管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:appInfo:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<SysAppInfo> getInfo(@RequestParam("id") Long id) {
        return AjaxResult.success(sysAppInfoService.selectSysAppInfoById(id));
    }

    /**
     * 新增app管理
     */
    @ApiOperation("新增app管理")
    @PreAuthorize("@ss.hasPermi('system:appInfo:add')")
    @Log(title = "app管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult saveApp(@RequestBody SysAppInfo sysAppInfo) {
        return toAjax(sysAppInfoService.insertSysAppInfo(sysAppInfo));
    }

    /**
     * 修改app管理
     */
    @ApiOperation("修改app管理")
    @PreAuthorize("@ss.hasPermi('system:appInfo:edit')")
    @Log(title = "app管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateApp(@RequestBody SysAppInfo sysAppInfo) {
        return toAjax(sysAppInfoService.updateSysAppInfo(sysAppInfo));
    }

    /**
     * 删除app管理
     */
    @ApiOperation("删除app管理")
    @PreAuthorize("@ss.hasPermi('system:appInfo:remove')")
    @Log(title = "app管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] ids) {
        return toAjax(sysAppInfoService.deleteSysAppInfoByIds(ids));
    }
}
