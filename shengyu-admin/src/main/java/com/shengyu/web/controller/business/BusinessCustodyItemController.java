package com.shengyu.web.controller.business;

import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 托管作业项
 */
@Api(tags = "托管作业项")
@RestController
@RequestMapping("/custody/item")
public class BusinessCustodyItemController extends BaseController {

    @Resource
    private ICustodyItemService custodyItemService;

    /**
     * 查询托管作业项列表
     */
    @ApiOperation(value = "托管作业项--列表")
    @PreAuthorize("@ss.hasPermi('custody:single:list')")
    @PostMapping("/list")
    public TableDataInfo<CustodyItemDto> list(CustodyItem custodyItem) {
        startPage();
        List<CustodyItemDto> list = custodyItemService.selectList(custodyItem);
        return getDataTable(list);
    }

    /**
     * 获取托管作业项详细信息
     */
    @ApiOperation(value = "托管作业项--详情")
    @PreAuthorize("@ss.hasPermi('custody:single:query')")
    @PostMapping("/query")
    public AjaxResult<CustodyItemDto> query(@RequestParam Long id) {
        return AjaxResult.success(custodyItemService.selectById(id));
    }

    /**
     * 新增托管作业项
     */
    @ApiOperation(value = "托管作业项--新增")
    @PreAuthorize("@ss.hasPermi('custody:single:add')")
    @Log(title = "托管作业项--新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult add(@RequestBody CustodyItem custodyItem) {
        return toAjax(custodyItemService.saveCustodyItem(custodyItem));
    }

    /**
     * 修改托管作业项
     */
    @ApiOperation(value = "托管作业项--修改")
    @PreAuthorize("@ss.hasPermi('custody:single:edit')")
    @Log(title = "托管作业项--修改", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody CustodyItem custodyItem) {
        return toAjax(custodyItemService.updateCustodyItem(custodyItem));
    }

    /**
     * 删除托管作业项
     */
    @ApiOperation(value = "托管作业项--删除")
    @PreAuthorize("@ss.hasPermi('custody:single:remove')")
    @Log(title = "托管作业项--删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam(value = "ids") Long[] ids) {
        return toAjax(custodyItemService.removeByIds(Arrays.asList(ids)));
    }

}
