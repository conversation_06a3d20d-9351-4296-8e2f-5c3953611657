package com.shengyu.web.controller.business;

import com.shengyu.business.domain.ServiceProvider;
import com.shengyu.business.domain.dto.ServiceProviderDto;
import com.shengyu.business.service.IServiceProviderService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 商城服务商信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/business/serviceProvider")
@Api(tags = "商城服务商信息")
public class BusinessServiceProviderController extends BaseController {
    @Resource
    private IServiceProviderService serviceProviderService;

    /**
     * 查询商城服务商信息列表
     */
    @ApiOperation(value = "查询商城服务商信息列表")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:list')")
    @PostMapping("/list")
    public TableDataInfo<ServiceProviderDto> list(ServiceProvider serviceProvider) {
        startPage();
        List<ServiceProviderDto> list = serviceProviderService.selectList(serviceProvider);
        return getDataTable(list);
    }

    /**
     * 获取商城服务商信息详细信息
     */
    @ApiOperation(value = "获取商城服务商信息详细信息")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<ServiceProviderDto> getInfo(@RequestParam Long id) {
        return AjaxResult.success(serviceProviderService.selectOne(id));
    }

    /**
     * 新增商城服务商信息
     */
    @ApiOperation(value = "新增商城服务商信息")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:add')")
    @Log(title = "商城服务商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<String> add(@RequestBody ServiceProvider serviceProvider) {
        return toAjax(serviceProviderService.saveServiceProvider(serviceProvider));
    }

    /**
     * 修改商城服务商信息
     */
    @ApiOperation(value = "修改商城服务商信息")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:edit')")
    @Log(title = "商城服务商信息", businessType = BusinessType.UPDATE)
    @PostMapping
    public AjaxResult<String> edit(@RequestBody ServiceProvider serviceProvider) {
        return toAjax(serviceProviderService.updateServiceProvider(serviceProvider));
    }

    /**
     * 删除商城服务商信息
     */
    @ApiOperation(value = "删除商城服务商信息")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:remove')")
    @Log(title = "商城服务商信息", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult<String> remove(@RequestParam Long[] ids) {
        return toAjax(serviceProviderService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 审核商城服务商信息
     */
    @ApiOperation(value = "审核商城服务商信息")
    @PreAuthorize("@ss.hasPermi('mall:serviceProvider:audit')")
    @Log(title = "商城服务商信息", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult<String> audit(@RequestBody ServiceProvider serviceProvider) {
        serviceProvider.setApprovalBy(SecurityUtils.getLoginUser().getUsername());
        serviceProvider.setApprovalTime(DateUtils.getNowDate());
        return toAjax(serviceProviderService.updateServiceProvider(serviceProvider));
    }
}
