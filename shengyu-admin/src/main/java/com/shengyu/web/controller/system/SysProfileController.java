package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.config.CapitalConfig;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.file.ObsUtil;
import com.shengyu.framework.web.service.TokenService;
import com.shengyu.system.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {
    @Autowired
    private ISysUserService userService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 个人信息
     */
    @GetMapping
    public MapResult profile() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        MapResult ajax = MapResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));

        return ajax;
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user) {
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        if (userService.updateUserProfile(user, true) > 0) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 更新缓存用户信息
            loginUser.getUser().setNickName(user.getNickName());
            loginUser.getUser().setPhonenumber(user.getPhonenumber());
            loginUser.getUser().setEmail(user.getEmail());
            loginUser.getUser().setSex(user.getSex());
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return AjaxResult.error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return AjaxResult.error("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(userName, SecurityUtils.encryptPassword(newPassword)) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(SecurityUtils.encryptPassword(newPassword));
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public MapResult avatar(HttpServletRequest request) {
        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            List<MultipartFile> fileList = multipartRequest.getFiles("file");
            if (StringUtils.isNotEmpty(fileList)) {
                MultipartFile file = fileList.get(0);
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                String savePath = ObsUtil.upload(file, CapitalConfig.getAvatarPath());
                String avatar = ObsUtil.viewPublicFile(savePath);
                if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                    MapResult ajax = MapResult.success();
                    ajax.put("imgUrl", avatar);
                    // 更新缓存用户头像
                    loginUser.getUser().setAvatar(avatar);
                    tokenService.setLoginUser(loginUser);
                    return ajax;
                }
            }
        } catch (IOException e) {
            logger.error("用户头像图片上传失败: {}", e.getMessage(), e);
        }
        return MapResult.error("上传图片异常，请联系管理员");
    }

    @ApiOperation(value = "修改手机号码")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updatePhone")
    public AjaxResult updatePhone(@RequestBody Map<String, String> params) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        userService.checkUserAllowed(user);
        String phone = params.get("phone");
        String code = params.get("code");
        String uuid = params.get("uuid");
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null || !captcha.equals(code)) {
            return AjaxResult.error("验证码错误!");
        }
        user.setPhonenumber(phone);
        user.setUpdateBy(SecurityUtils.getUsername());
        if (userService.updateUserPhone(SecurityUtils.getUsername(), phone)) {
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改电话异常，请联系管理员");
    }

    @ApiOperation(value = "修改密码-手机端")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwdForMobile")
    public AjaxResult updatePwdForMobile(String code, String uuid, String newPassword) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null || !captcha.equals(code)) {
            return AjaxResult.error("验证码错误!");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return AjaxResult.error("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(userName, SecurityUtils.encryptPassword(newPassword)) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(SecurityUtils.encryptPassword(newPassword));
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改密码异常，请联系管理员");
    }
}
