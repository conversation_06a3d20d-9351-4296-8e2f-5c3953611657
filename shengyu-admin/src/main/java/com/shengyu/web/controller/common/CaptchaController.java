package com.shengyu.web.controller.common;

import com.alibaba.fastjson.JSONObject;
import com.google.code.kaptcha.Producer;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.MessageUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.VerifyCodeUtils;
import com.shengyu.common.utils.sign.Base64;
import com.shengyu.common.utils.sms.SmsEnum;
import com.shengyu.common.utils.sms.SmsUtil;
import com.shengyu.common.utils.uuid.IdUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@Api(tags = "通用接口")
@RestController
public class CaptchaController {
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisCache redisCache;

    /**
     * 验证码类型
     */
    @Value("${shengyu.captchaType}")
    private String captchaType;

    /**
     * 生成验证码
     */
    @ApiOperation(value = "获取web随机验证码图片")
    @PostMapping("/captchaImage")
    public MapResult getCode() {
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String capStr = null, code = null;
        BufferedImage image = null;
        // 生成验证码
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }
        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return MapResult.error(e.getMessage());
        }

        MapResult ajax = MapResult.success();
        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    @ApiOperation(value = "获取手机登录验证码")
    @PostMapping("/getPhoneCode")
    public MapResult getPhoneCode(@RequestBody Map<String, String> paramsMap) throws Exception {
        String phone = paramsMap.get("phone");
        String imageCode = paramsMap.get("imageCode");
        String imageUuid = paramsMap.get("imageUuid");
        String platForm = paramsMap.get("platForm");
        if ((StringUtils.isEmpty(platForm) || !"2".equals(platForm)) &&
                (StringUtils.isEmpty(phone) || StringUtils.isEmpty(imageCode) || StringUtils.isEmpty(imageUuid))) {
            return MapResult.error(MessageUtils.message("not.null", "手机号、图片验证码、短信验证码"));
        }
        if ("2".equals(platForm) && StringUtils.isEmpty(phone)) {
            return MapResult.error(MessageUtils.message("not.null", "手机号"));
        }
        if (!"2".equals(platForm)) {
            //对比图片验证码，验证
            String imageVerifyKey = Constants.CAPTCHA_CODE_KEY + imageUuid;
            String captcha = redisCache.getCacheObject(imageVerifyKey);
            redisCache.deleteObject(imageVerifyKey);
            if (captcha == null) {
                return MapResult.error(MessageUtils.message("user.jcaptcha.expire"));
            }
            if (!imageCode.equalsIgnoreCase(captcha)) {
                return MapResult.error(MessageUtils.message("user.jcaptcha.error"));
            }
        }
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String code = VerifyCodeUtils.generateVerifyCode(4, "0123456789");
        JSONObject json = new JSONObject();
        json.put("code", code);
        boolean sendResult = SmsUtil.sendSms(phone, json, SmsEnum.LOGIN_TEMPLATE_CODE);
        if (sendResult) {
            redisCache.setCacheObject(verifyKey, code, Constants.PHONE_CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
            MapResult ajax = MapResult.success();
            ajax.put("uuid", uuid);
            return ajax;
        } else {
            return MapResult.error(MessageUtils.message("user.jcaptcha.sendError"));
        }
    }
}
