package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.entity.SysRole;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysNotice;
import com.shengyu.system.domain.model.NoticeAndDeptModel;
import com.shengyu.system.domain.model.SysNoticeDto;
import com.shengyu.system.service.ISysNoticeDeptService;
import com.shengyu.system.service.ISysNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */
@Api(tags = "公告信息")
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController {
    @Autowired
    private ISysNoticeService noticeService;
    @Autowired
    private ISysNoticeDeptService noticeDeptService;

    /**
     * 获取通知公告列表
     */
    @ApiOperation(value = "获取通知公告列表")
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @PostMapping("/list")
    public TableDataInfo<SysNotice> list(SysNotice notice) {
        startPage();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        if (StringUtils.isNotEmpty(roles) && roles.stream().anyMatch(r -> r.getRoleId() == 2)) {//如果是教师 则按个人数据权限处理
            notice.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        }
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    @ApiOperation(value = "获取通知公告列表")
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @PostMapping("/ownList")
    public TableDataInfo ownList(SysNoticeDto notice) {
        startPage();
        List<SysNotice> list = noticeService.selectOwnNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @ApiOperation(value = "根据通知公告编号获取详细信息")
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<NoticeAndDeptModel> getInfo(@RequestParam Long noticeId) {
        NoticeAndDeptModel model = new NoticeAndDeptModel();
        SysNotice notice = noticeService.selectNoticeById(noticeId);
        BeanUtils.copyProperties(notice, model);
        Long[] deptIds = noticeDeptService.selectSysNoticeDeptByNoticeId(noticeId);
        model.setDeptIds(deptIds);
        return AjaxResult.success(model);
    }

    /**
     * 新增通知公告
     */
    @ApiOperation(value = "新增通知公告")
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody NoticeAndDeptModel notice) {
        notice.setCreateBy(SecurityUtils.getUsername());
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @ApiOperation(value = "修改通知公告")
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody NoticeAndDeptModel notice) {
        notice.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     */
    @ApiOperation(value = "删除通知公告")
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }
}
