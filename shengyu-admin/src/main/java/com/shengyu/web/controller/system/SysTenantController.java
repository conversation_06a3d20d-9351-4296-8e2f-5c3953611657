package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.system.domain.SysTenant;
import com.shengyu.system.domain.model.TenantModel;
import com.shengyu.system.service.ISysTenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户Controller
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Api(tags = "租户管理")
@RestController
@RequestMapping("/sys/tenant")
public class SysTenantController extends BaseController {
    @Autowired
    private ISysTenantService sysTenantService;

    /**
     * 查询租户列表
     */
    @ApiOperation(value = "查询租户列表")
    @PreAuthorize("@ss.hasRole('admin')")
    @PostMapping("/list")
    public TableDataInfo list(SysTenant sysTenant) {
        startPage();
        List<SysTenant> list = sysTenantService.selectSysTenantList(sysTenant);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询租户列表")
    @PreAuthorize("@ss.hasRole('admin')")
    @PostMapping("/allList")
    public AjaxResult<List<SysTenant>> allList() {
        List<SysTenant> list = sysTenantService.selectSysTenantList(new SysTenant());
        return AjaxResult.success(list);
    }

    /**
     * 获取租户详细信息
     */
    @ApiOperation(value = "获取租户详细信息")
    @PreAuthorize("@ss.hasRole('admin')")
    @PostMapping(value = "/getInfo")
    public AjaxResult getInfo(@RequestParam Integer id) {
        return AjaxResult.success(sysTenantService.selectSysTenantById(id));
    }

    /**
     * 新增租户
     */
    @ApiOperation(value = "新增租户")
    @PreAuthorize("@ss.hasRole('admin')")
    @Log(title = "租户", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addTenant(@RequestBody TenantModel sysTenant) {
        return toAjax(sysTenantService.insertSysTenant(sysTenant));
    }

    /**
     * 修改租户
     */
    @ApiOperation(value = "修改租户")
    @PreAuthorize("@ss.hasRole('admin')")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult editTenant(@RequestBody SysTenant sysTenant) {
        return toAjax(sysTenantService.updateSysTenant(sysTenant));
    }

    /**
     * 删除租户
     */
    @ApiOperation(value = "删除租户")
    @PreAuthorize("@ss.hasRole('admin')")
    @Log(title = "租户", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult<String> remove(@RequestParam Integer[] ids) {
        return toAjax(sysTenantService.deleteSysTenantByIds(ids));
    }
}
