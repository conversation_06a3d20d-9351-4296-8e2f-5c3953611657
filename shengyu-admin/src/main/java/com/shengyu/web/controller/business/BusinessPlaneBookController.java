package com.shengyu.web.controller.business;

import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.service.IPlaneBookService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 飞防服务预定
 */
@Api(tags = "飞防服务预定")
@RestController
@RequestMapping("/plane/book")
public class BusinessPlaneBookController extends BaseController {

    @Resource
    private IPlaneBookService planeBookService;

    /**
     * 托管订单
     */
    @ApiOperation(value = "飞防服务预定--列表")
    @PreAuthorize("@ss.hasPermi('plane:book:list')")
    @PostMapping("/list")
    public TableDataInfo<PlaneBook> list(PlaneBook custodyBook) {
        startPage();
        List<PlaneBook> list = planeBookService.selectList(custodyBook);
        return getDataTable(list);
    }

    /**
     * 托管订单--详情
     */
    @ApiOperation(value = "飞防服务预定--详情")
    @PreAuthorize("@ss.hasPermi('plane:book:query')")
    @PostMapping("/query")
    public AjaxResult<PlaneBook> query(@RequestParam Long id) {
        return success(planeBookService.selectById(id));
    }

    /**
     * 飞防服务预定--处理
     */
    @ApiOperation(value = "飞防服务预定--处理")
    @PreAuthorize("@ss.hasPermi('plane:book:deal')")
    @Log(title = "飞防服务预定--处理", businessType = BusinessType.UPDATE)
    @PostMapping("/deal")
    public AjaxResult<String> deal(@RequestBody PlaneBook planeBook) {
        return toAjax(planeBookService.updateById(planeBook));
    }
}
