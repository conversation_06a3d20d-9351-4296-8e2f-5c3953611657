package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysRole;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.service.ISysDeptService;
import com.shengyu.system.service.ISysPostService;
import com.shengyu.system.service.ISysRoleService;
import com.shengyu.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysRoleService roleService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private ISysPostService postService;

    /**
     * 获取用户列表
     */
    @ApiOperation(value = "获取用户列表")
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @PostMapping("/list")
    public TableDataInfo<SysUser> list(SysUser user) {
        startPage();
        if (!SecurityUtils.getLoginUser().getUser().isAdmin() && StringUtils.isEmpty(user.getTenantId())) {
            user.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation(value = "根据用户编号获取详细信息")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @PostMapping(value = "/getInfo")
    public MapResult getInfo(@RequestParam Long userId) {
        MapResult ajax = MapResult.success();
        List<SysRole> roles = roleService.selectRoleAll(null);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin())
                .collect(Collectors.toList()));
        if (StringUtils.isNotNull(userId)) {
            SysUser user = userService.selectUserById(userId);
            ajax.put("data", user);
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
            ajax.put("postIds", postService.selectPostListByUserId(userId));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @ApiOperation(value = "新增用户")
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkIdCardUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，身份证号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user, true));
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改用户")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getIdCard())
                && UserConstants.NOT_UNIQUE.equals(userService.checkIdCardUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，身份证号已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUser(user, true));
    }

    /**
     * 删除用户
     */
    @ApiOperation(value = "删除用户")
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] userIds) {
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置密码")
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.resetPwd(user));
    }

    @ApiOperation(value = "重置密码")
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchResetPwd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "userid(Long)", dataType = "Long"),
            @ApiImplicitParam(name = "password", value = "密码(为空时重置为系统默认密码)(String)", dataType = "String")
    })
    public AjaxResult batchResetPwd(@RequestParam Long[] ids, @RequestParam(required = false) String password) {
        if (StringUtils.isEmpty(password)) {
            password = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "sys.user.initPassword");
        }
        password = SecurityUtils.encryptPassword(password);
        return toAjax(userService.batchResetPwd(ids, password));
    }

    /**
     * 状态修改
     */
    @ApiOperation(value = "状态修改")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @ApiOperation(value = "根据用户编号获取授权角色")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @PostMapping("/getRoleByUser")
    public MapResult getRoleByUser(@RequestParam Long userId) {
        MapResult ajax = MapResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @ApiOperation(value = "用户授权角色")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PostMapping("/authRole")
    public AjaxResult insertAuthRole(@RequestParam Long userId, @RequestParam Long[] roleIds) {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 查询用户下拉选
     */
    @ApiOperation(value = "用户管理-获取下拉选", notes = "keyWord可以是名称也可以是手机号")
    @PostMapping("/getUserByKeyWord")
    public AjaxResult getUserByKeyWord(@RequestParam(required = false) String keyWord, @RequestParam(required = false) Long deptId) {
        SysUser user = new SysUser();
        if (StringUtils.isEmpty(keyWord) && StringUtils.isNull(deptId)) {
            throw new CustomException("请至少传入关键词或组织机构");
        }
        user.setSearchValue(keyWord);
        user.setTenantId(ConfigUtils.getTenantId());
        user.setDeptId(deptId);
        List<SysUser> userList = userService.selectByKeyWord(user);
        return AjaxResult.success(userList);
    }

    @ApiOperation(value = "用户管理-设置管理业务组织", notes = "设置管理业务组织")
    @PostMapping("/setManageDept")
    public AjaxResult setManageDept(@RequestParam Long deptId) {
        SysDept sysDept = deptService.selectDeptById(deptId);
        if (!UserConstants.NORMAL.equals(sysDept.getIsBusiness())) {
            throw new CustomException("请选择业务组织进行切换!");
        }
        String manageKey = "temp_manage_dept_id_" + SecurityUtils.getUsername();
        String tenantKey = "temp_manage_tenant_id_" + SecurityUtils.getUsername();
        String deptCodeKey = "temp_manage_dept_code_" + SecurityUtils.getUsername();
        redisCache.setCacheObject(manageKey, deptId);
        redisCache.setCacheObject(tenantKey, sysDept.getTenantId());
        redisCache.setCacheObject(deptCodeKey, sysDept.getDeptCode());
        return AjaxResult.success(sysDept);
    }

    @ApiOperation(value = "授权秘钥")
    @PreAuthorize("@ss.hasPermi('system:user:setSec')")
    @PostMapping("/setSec")
    public AjaxResult<String> setSec(@RequestParam("id") Long id, @RequestParam("secStr") String secStr) {
        return toAjax(userService.authorize(id, secStr));
    }

}
