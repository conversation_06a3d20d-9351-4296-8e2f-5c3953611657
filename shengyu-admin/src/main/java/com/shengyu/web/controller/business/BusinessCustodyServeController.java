package com.shengyu.web.controller.business;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 托管服务
 */
@Api(tags = "托管服务")
@RestController
@RequestMapping("/custody/serve")
public class BusinessCustodyServeController extends BaseController {

    @Resource
    private ICustodyServeService custodyServeService;

    /**
     * 查询托管服务列表
     */
    @ApiOperation(value = "托管服务--列表")
    @PreAuthorize("@ss.hasPermi('custody:serve:list')")
    @PostMapping("/list")
    public TableDataInfo<CustodyServeDto> list(CustodyServe custodyServe) {
        startPage();
        List<CustodyServeDto> list = custodyServeService.selectList(custodyServe);
        return getDataTable(list);
    }

    /**
     * 获取托管服务详细信息
     */
    @ApiOperation(value = "托管服务--详情")
    @PreAuthorize("@ss.hasPermi('custody:serve:query')")
    @PostMapping("/query")
    public AjaxResult<CustodyServeDto> query(@RequestParam Long id) {
        return AjaxResult.success(custodyServeService.selectById(id));
    }

    /**
     * 新增托管服务
     */
    @ApiOperation(value = "托管服务--新增")
    @PreAuthorize("@ss.hasPermi('custody:serve:add')")
    @Log(title = "托管服务", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult add(@RequestBody CustodyServe custodyServe) {
        logger.debug("add CustodyFull:{}", JSONObject.toJSONString(custodyServe));
        return toAjax(custodyServeService.saveCustodyServe(custodyServe));
    }

    /**
     * 修改托管服务
     */
    @ApiOperation(value = "托管服务--修改")
    @PreAuthorize("@ss.hasPermi('custody:serve:edit')")
    @Log(title = "托管服务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody CustodyServe custodyServe) {
        return toAjax(custodyServeService.updateCustodyServe(custodyServe));
    }

    /**
     * 上架、下架托管服务
     */
    @ApiOperation(value = "托管服务--上架、下架")
    @PreAuthorize("@ss.hasPermi('custody:serve:editStatus')")
    @Log(title = "托管服务--状态变更", businessType = BusinessType.UPDATE)
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody CustodyServe custodyServe) {
        return toAjax(custodyServeService.updateById(custodyServe));
    }

    /**
     * 删除托管服务
     */
    @ApiOperation(value = "托管服务--删除")
    @PreAuthorize("@ss.hasPermi('custody:serve:remove')")
    @Log(title = "托管服务", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] ids) {
        return toAjax(custodyServeService.removeByIds(Arrays.asList(ids)));
    }

}
