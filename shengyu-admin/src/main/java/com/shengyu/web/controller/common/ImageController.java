package com.shengyu.web.controller.common;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.config.CapitalConfig;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.file.ObsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@Api(tags = "图片上传接口")
@RestController
public class ImageController extends BaseController {
    /**
     * 上传banner图片
     * 支持单个图片文件上传
     *
     * @param file 上传的图片文件
     * @return 包含图片URL的MapResult对象
     */
    @Log(title = "上传banner", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "上传banner")
    @PostMapping("/banner")
    public MapResult uploadBanner(@RequestParam("file") MultipartFile file) {
        try {
            // 1. 基础校验
            if (file == null || file.isEmpty()) {
                return MapResult.error("请选择要上传的图片文件");
            }

            // 2. 文件扩展名校验
            String filename = file.getOriginalFilename();
            if (!validateImageFile(filename)) {
                return MapResult.error("仅支持JPG/PNG/GIF/WEBP格式的图片");
            }

            // 3. 文件大小校验（5MB限制）
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (file.getSize() > maxSize) {
                return MapResult.error("图片大小不能超过5MB");
            }

            // 4. 执行上传
            String filePath = ObsUtil.upload(file, CapitalConfig.getBusinessPath());
            String fileUrl = ObsUtil.viewPublicFile(filePath);

            // 5. 返回结果
            MapResult result = MapResult.success();
            result.put("imgUrl", fileUrl);
            return result;
        } catch (Exception e) {
            logger.error("Banner图片上传失败: {}", e.getMessage(), e);
            return MapResult.error("图片上传失败，请稍后重试");
        }
    }

    /**
     * 校验图片文件扩展名
     *
     * @param filename 文件名
     * @return 是否有效
     */
    private boolean validateImageFile(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return false;
        }

        String lowerName = filename.toLowerCase();
        return lowerName.endsWith(".jpg") ||
                lowerName.endsWith(".jpeg") ||
                lowerName.endsWith(".png") ||
                lowerName.endsWith(".gif") ||
                lowerName.endsWith(".webp");
    }
}
