package com.shengyu.web.controller.system;

import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.shengyu.system.domain.SysNoticeStatus;
import com.shengyu.system.service.ISysNoticeStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息状态Controller
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Api(tags = "消息状态")
@RestController
@RequestMapping("/system/noticeStatus")
public class SysNoticeStatusController extends BaseController {
    @Autowired
    private ISysNoticeStatusService sysNoticeStatusService;

    /**
     * 查询消息状态列表
     */
    @ApiOperation("查询消息状态列表")
    @PreAuthorize("@ss.hasPermi('system:noticeStatus:list')")
    @PostMapping("/list")
    public TableDataInfo<SysNoticeStatus> list(SysNoticeStatus sysNoticeStatus) {
        startPage();
        List<SysNoticeStatus> list = sysNoticeStatusService.selectSysNoticeStatusList(sysNoticeStatus);
        return getDataTable(list);
    }

    /**
     * 获取消息状态详细信息
     */
    @ApiOperation("获取消息状态详细信息")
    @PreAuthorize("@ss.hasPermi('system:noticeStatus:query')")
    @PostMapping(value = "/getInfo")
    public AjaxResult<SysNoticeStatus> getInfo(@RequestParam("id") Long id) {
        return AjaxResult.success(sysNoticeStatusService.selectSysNoticeStatusById(id));
    }

    /**
     * 新增消息状态
     */
    @ApiOperation("新增消息状态")
    @PreAuthorize("@ss.hasPermi('system:noticeStatus:add')")
    @Log(title = "消息状态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysNoticeStatus sysNoticeStatus) {
        return toAjax(sysNoticeStatusService.insertSysNoticeStatus(sysNoticeStatus));
    }

    /**
     * 修改消息状态
     */
    @ApiOperation("修改消息状态")
    @PreAuthorize("@ss.hasPermi('system:noticeStatus:edit')")
    @Log(title = "消息状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysNoticeStatus sysNoticeStatus) {
        return toAjax(sysNoticeStatusService.updateSysNoticeStatus(sysNoticeStatus));
    }

    /**
     * 删除消息状态
     */
    @ApiOperation("删除消息状态")
    @PreAuthorize("@ss.hasPermi('system:noticeStatus:remove')")
    @Log(title = "消息状态", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestParam Long[] ids) {
        return toAjax(sysNoticeStatusService.deleteSysNoticeStatusByIds(ids));
    }
}
