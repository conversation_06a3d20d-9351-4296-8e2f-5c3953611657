package com.shengyu.web.controller.business;

import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 飞防服务
 */
@Api(tags = "飞防服务")
@RestController
@RequestMapping("/plane/serve")
public class BusinessPlaneServeController extends BaseController {

    @Resource
    private IPlaneServeService planeServeService;

    /**
     * 飞防服务
     */
    @ApiOperation(value = "飞防服务--列表")
    @PreAuthorize("@ss.hasPermi('plane:serve:list')")
    @PostMapping("/list")
    public TableDataInfo<PlaneServe> list(PlaneServe planeServe) {
        startPage();
        List<PlaneServe> list = planeServeService.selectList(planeServe);
        return getDataTable(list);
    }

    /**
     * 飞防服务--详情
     */
    @ApiOperation(value = "飞防服务--详情")
    @PreAuthorize("@ss.hasPermi('plane:serve:query')")
    @PostMapping("/query")
    public AjaxResult<PlaneServe> query(@RequestParam Long id) {
        return AjaxResult.success(planeServeService.selectById(id));
    }

    /**
     * 飞防服务--详情
     */
    @ApiOperation(value = "飞防服务--新增")
    @PreAuthorize("@ss.hasPermi('plane:serve:add')")
    @Log(title = "飞防服务", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult<String> add(@RequestBody PlaneServe planeServe) {
        return toAjax(planeServeService.savePlaneServe(planeServe));
    }

    /**
     * 飞防服务--编辑
     */
    @ApiOperation(value = "飞防服务--编辑")
    @PreAuthorize("@ss.hasPermi('plane:serve:edit')")
    @Log(title = "飞防服务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<String> edit(@RequestBody PlaneServe planeServe) {
        return toAjax(planeServeService.updatePlaneServe(planeServe));
    }

    /**
     * 飞防服务--上架、下架
     */
    @ApiOperation(value = "飞防服务--上架、下架")
    @PreAuthorize("@ss.hasPermi('plane:serve:editStatus')")
    @Log(title = "飞防服务--状态变更", businessType = BusinessType.UPDATE)
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody PlaneServe planeServe) {
        return toAjax(planeServeService.updateById(planeServe));
    }

    /**
     * 飞防服务--删除
     */
    @ApiOperation(value = "飞防服务--删除")
    @PreAuthorize("@ss.hasPermi('plane:serve:remove')")
    @Log(title = "飞防服务", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult<String> remove(Long[] ids) {
        return toAjax(planeServeService.removeByIds(Arrays.asList(ids)));
    }

}
