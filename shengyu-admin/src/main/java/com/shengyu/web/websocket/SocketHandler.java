package com.shengyu.web.websocket;

import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.core.redis.RedisListener;
import com.shengyu.common.utils.JsonUtils;
import com.shengyu.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 监听消息(采用redis发布订阅方式发送消息)
 */
@Slf4j
@Component
public class SocketHandler implements RedisListener {

    @Autowired
    private WebSocket webSocket;

    @Override
    public void onMessage(SocketMsg msg) {
        String userId = msg.getUserId();
        String message = JsonUtils.getJsonString(msg);
        if (StringUtils.isNotNull(userId)) {
            webSocket.pushMessage(msg);
        } else {
            webSocket.pushMessage(message);
        }
    }
}