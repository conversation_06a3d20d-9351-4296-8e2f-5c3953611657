package com.shengyu.app.controller;

import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.service.IPlaneBookService;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "app--飞防服务")
@RestController
@RequestMapping("/app/planServe")
public class PlanServeController extends BaseAppController {

    @Autowired
    private IPlaneServeService planServeService;

    @Autowired
    private IPlaneBookService planeBookService;


//    @ApiOperation(value = "飞防服务--列表")
//    @PostMapping("/serve/list")
//    public TableDataInfo<PlaneServe> serveList(@RequestBody PlaneServe planServe) {
//        startPage();
//        planServe.setStatus(1);
//        List<PlaneServe> list = planServeService.selectList(planServe);
//        return getDataTable(list);
//    }

    @ApiOperation(value = "飞防服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<PlaneServe> queryServe(@RequestParam String serveType) {
        PlaneServe planServe = new PlaneServe();
        planServe.setServeType(serveType);
        planServe.setStatus(1);
        return AjaxResult.success(planServeService.selectList(planServe).get(0));
    }


    @ApiOperation(value = "飞防服务预定--列表")
    @PostMapping("/book/list")
    public AjaxResult<List<PlaneBook>> bookList(HttpServletRequest request) {
        PlaneBook query = new PlaneBook();
        query.setBookUserId(getLoginUser().getUserId());
        List<PlaneBook> planeBookList = planeBookService.selectList(query);
        return AjaxResult.success(planeBookList);
    }

    @ApiOperation(value = "飞防服务预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<PlaneBook> bookQuery(@RequestParam Long id) {
        return AjaxResult.success(planeBookService.selectByIdUserId(id, getLoginUser().getUserId()));
    }

    @ApiOperation(value = "飞防服务预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody PlaneBook planeBook) {
        try {
            planeBook.setBookUserId(getLoginUser().getUserId());
            return AjaxResult.success(planeBookService.savePlaneBook(planeBook));
        } catch (Exception e) {
            logger.error("新增飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "飞防服务预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody PlaneBook planeBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(planeBookService.updateByIdWithUserCheck(planeBook, loginUserId));
        } catch (Exception e) {
            logger.error("修改飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "飞防服务预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(planeBookService.removeByIdWithUserCheck(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
