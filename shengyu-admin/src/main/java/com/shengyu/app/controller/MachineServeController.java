package com.shengyu.app.controller;

import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.service.IMachineBookService;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "app--农机服务")
@RestController
@RequestMapping("/app/machineServe")
public class MachineServeController extends BaseAppController {

    @Autowired
    private IMachineServeService machineServeService;

    @Autowired
    private IMachineBookService machineBookService;

//    @ApiOperation(value = "农机服务--列表")
//    @PostMapping("/serve/list")
//    public TableDataInfo<MachineServe> serveList(@RequestBody  MachineServe machineServe) {
//        startPage();
//        machineServe.setStatus(1);
//        List<MachineServe> list = machineServeService.selectList(machineServe);
//        return getDataTable(list);
//    }

    @ApiOperation(value = "农机服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<MachineServe> queryServe(@RequestParam String serveType) {
        MachineServe machineServe = new MachineServe();
        machineServe.setServeType(serveType);
        machineServe.setStatus(1);
        return AjaxResult.success(machineServeService.selectList(machineServe).get(0));
    }

    @ApiOperation(value = "农机服务预定--列表")
    @PostMapping("/book/list")
    public AjaxResult<List<MachineBook>> bookList(HttpServletRequest request) {
        MachineBook query = new MachineBook();
        query.setBookUserId(getLoginUser().getUserId());
        List<MachineBook> machineBookList = machineBookService.selectMachineBookList(query);
        return AjaxResult.success(machineBookList);
    }

    @ApiOperation(value = "农机服务预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<MachineBook> bookQuery(@RequestParam Long id) {
        return AjaxResult.success(machineBookService.selectByIdUserId(id, getLoginUser().getUserId()));
    }

    @ApiOperation(value = "农机服务预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody MachineBook machineBook) {
        try {
            machineBook.setBookUserId(getLoginUser().getUserId());
            return AjaxResult.success(machineBookService.saveMachineBook(machineBook));
        } catch (Exception e) {
            logger.error("新增农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "农机服务预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody MachineBook machineBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(machineBookService.updateByIdWithUserCheck(machineBook, loginUserId));
        } catch (Exception e) {
            logger.error("修改农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "农机服务预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(machineBookService.removeByIdWithUserCheck(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
