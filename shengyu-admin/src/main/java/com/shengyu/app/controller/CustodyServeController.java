package com.shengyu.app.controller;

import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.service.ICustodyBookService;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "app--托管服务")
@RestController
@RequestMapping("/app/custody")
public class CustodyServeController extends BaseAppController {

    @Autowired
    private ICustodyServeService custodyFullService;

    @Autowired
    private ICustodyBookService custodyBookService;

    @ApiOperation(value = "托管服务--列表")
    @PostMapping("/serve/list")
    public TableDataInfo<CustodyServeDto> serveList(@RequestBody CustodyServe custodyServe) {
        startPage();
        custodyServe.setStatus(1);
        List<CustodyServeDto> list = custodyFullService.selectList(custodyServe);
        return getDataTable(list);
    }

    @ApiOperation(value = "托管服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<CustodyServeDto> serveQuery(@RequestParam Long id) {
        CustodyServeDto dto = custodyFullService.selectById(id);
        return AjaxResult.success(dto);
    }

    @ApiOperation(value = "托管预定--托管列表")
    @PostMapping("/book/list")
    public AjaxResult<List<CustodyBookDto>> bookList(HttpServletRequest request) {
        CustodyBook query = new CustodyBook();
        query.setBookUserId(getLoginUser().getUserId());
        List<CustodyBookDto> custodyBookList = custodyBookService.selectList(query);
        return AjaxResult.success(custodyBookList);
    }

    @ApiOperation(value = "托管预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<CustodyBookDto> bookQuery(@RequestParam Long id) {
        CustodyBookDto custodyBookDto = custodyBookService.selectByIdUserId(id, getLoginUser().getUserId());
        return AjaxResult.success(custodyBookDto);
    }

    @ApiOperation(value = "托管预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody CustodyBook custodyBook) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            custodyBook.setBookUserId(loginUser.getUser().getUserId());
            return AjaxResult.success(custodyBookService.saveCustodyBook(custodyBook));
        } catch (CustomException e) {
            logger.error("新增托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "托管预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody CustodyBook custodyBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(custodyBookService.updateByIdWithUserCheck(custodyBook, loginUserId));
        } catch (CustomException e) {
            logger.error("修改托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "托管预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(custodyBookService.removeByIdWithUserCheck(id, loginUserId));
        } catch (CustomException e) {
            logger.error("删除托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
