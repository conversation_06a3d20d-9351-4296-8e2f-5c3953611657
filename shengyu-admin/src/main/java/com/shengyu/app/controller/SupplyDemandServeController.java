package com.shengyu.app.controller;

import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import com.shengyu.business.enums.AuditStatusEnum;
import com.shengyu.business.service.ISupplyDemandService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "app--供需服务")
@RestController
@RequestMapping("/app/supplyDemandServe")
public class SupplyDemandServeController extends BaseAppController {

    @Autowired
    private ISupplyDemandService supplyDemandService;

    @ApiOperation(value = "供需服务--列表")
    @PostMapping("/list")
    public TableDataInfo<SupplyDemandDto> list(@RequestBody SupplyDemand supplyDemand) {
        startPage();
        supplyDemand.setUserId(null);
        supplyDemand.setAuditStatus(AuditStatusEnum.PASS);
        List<SupplyDemandDto> list = supplyDemandService.selectList(supplyDemand);
        return getDataTable(list);
    }

    @ApiOperation(value = "供需服务--详情")
    @PostMapping("/query")
    public AjaxResult<SupplyDemand> query(@RequestParam Long id) {
        return AjaxResult.success(supplyDemandService.selectValidById(id));
    }

    @ApiOperation(value = "供需服务--我的供需列表")
    @PostMapping("/personal/list")
    public TableDataInfo<SupplyDemandDto> myList(@RequestBody SupplyDemand supplyDemand) {
        startPage();
        Long loginUserId = getLoginUser().getUserId();
        supplyDemand.setUserId(loginUserId);
        List<SupplyDemandDto> list = supplyDemandService.selectList(supplyDemand);
        return getDataTable(list);
    }

    @ApiOperation(value = "供需服务--详情")
    @PostMapping("/personal/query")
    public AjaxResult<SupplyDemand> myQuery(@RequestParam Long id) {
        Long userId = getLoginUser().getUserId();
        return AjaxResult.success(supplyDemandService.selectByIdUserId(id, userId));
    }

    @ApiOperation(value = "供需服务--发布")
    @PostMapping("/personal/publish")
    public AjaxResult<Boolean> publish(@RequestBody SupplyDemand supplyDemand) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            supplyDemand.setUserId(loginUserId);
            return AjaxResult.success(supplyDemandService.saveSupplyDemand(supplyDemand));
        } catch (Exception e) {
            logger.error("新增供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "供需服务--修改")
    @PostMapping("/personal/edit")
    public AjaxResult<Boolean> update(@RequestBody SupplyDemand supplyDemand) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(supplyDemandService.updateByIdUserId(supplyDemand, loginUserId));
        } catch (Exception e) {
            logger.error("修改供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "供需服务--删除")
    @DeleteMapping("/personal/delete")
    public AjaxResult<Boolean> delete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(supplyDemandService.removeByIdUserId(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
