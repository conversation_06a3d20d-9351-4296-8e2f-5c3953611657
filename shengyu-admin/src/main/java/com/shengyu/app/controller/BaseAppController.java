package com.shengyu.app.controller;

import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * app层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseAppController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 获取登录用户
     *
     * @return
     */
    protected SysUser getLoginUser() {
        return SecurityUtils.getLoginUser().getUser();
    }


}
