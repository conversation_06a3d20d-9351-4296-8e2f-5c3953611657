package com.shengyu.common.utils.excel;


import com.shengyu.common.utils.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFFormulaEvaluator;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class ExcelTemplate {
    private String path;

    private Workbook workbook;

    private Sheet[] sheets;

    private Sheet sheet;

    private Throwable ex;

    private List<Cell> cellList = null;

    private CellStyle style;

    private Pattern doublePattern = Pattern.compile("^[0-9]+[.]{0,1}[0-9]*[dD]{0,1}$");

    public ExcelTemplate(String path) {
        this.path = path;
        init();
    }

    public ExcelTemplate(InputStream is) {
        init(is);
    }

    public ExcelTemplate(File file) {
        init(file);
    }

    private void init() {
        File file = new File(this.path);
        if (file.exists() && (this.path == null || (
                !this.path.endsWith(".xlsx") && !this.path.endsWith(".xls")))) {
            this.ex = new IOException("错误的文件格式");
        } else {
            try {
                InputStream is = new FileInputStream(file);
                this.workbook = WorkbookFactory.create(is);
                this.style = this.workbook.createCellStyle();
                style.setBorderBottom(BorderStyle.THIN); //下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                style.setWrapText(true);
                this.sheets = new Sheet[this.workbook.getNumberOfSheets()];
                for (int i = 0; i < this.sheets.length; i++)
                    this.sheets[i] = this.workbook.getSheetAt(i);
                if (this.sheets.length > 0)
                    this.sheet = this.sheets[0];
                this.sheet.setForceFormulaRecalculation(true);
            } catch (Exception e) {
                e.printStackTrace();
                this.ex = e;
            }
        }
    }

    private void init(InputStream is) {
        try {
            this.workbook = WorkbookFactory.create(is);
            this.style = this.workbook.createCellStyle();
            style.setBorderBottom(BorderStyle.THIN); //下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderRight(BorderStyle.THIN);//右边框
            style.setWrapText(true);
            this.sheets = new Sheet[this.workbook.getNumberOfSheets()];
            for (int i = 0; i < this.sheets.length; i++)
                this.sheets[i] = this.workbook.getSheetAt(i);
            if (this.sheets.length > 0)
                this.sheet = this.sheets[0];
            this.sheet.setForceFormulaRecalculation(true);
        } catch (Exception e) {
            this.ex = e;
        }
    }

    private void init(File file) {
        try {
            InputStream is = new FileInputStream(file);
            this.workbook = WorkbookFactory.create(is);
            this.style = this.workbook.createCellStyle();
            style.setBorderBottom(BorderStyle.THIN); //下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderRight(BorderStyle.THIN);//右边框
            style.setWrapText(true);
            this.sheets = new Sheet[this.workbook.getNumberOfSheets()];
            for (int i = 0; i < this.sheets.length; i++)
                this.sheets[i] = this.workbook.getSheetAt(i);
            if (this.sheets.length > 0)
                this.sheet = this.sheets[0];
            this.sheet.setForceFormulaRecalculation(true);
        } catch (Exception e) {
            e.printStackTrace();
            this.ex = e;
        }
    }

    private boolean initSheet(int sheetNo) {
        if (!examine() || sheetNo < 0 || sheetNo > this.workbook.getNumberOfSheets() - 1)
            return false;
        int sheetNum = this.workbook.getNumberOfSheets();
        this.sheets = new Sheet[sheetNum];
        for (int i = 0; i < sheetNum; i++) {
            if (i == sheetNo)
                this.sheet = this.workbook.getSheetAt(i);
            this.sheets[i] = this.workbook.getSheetAt(i);
        }
        this.sheet = this.workbook.getSheetAt(sheetNo);
        this.sheet.setForceFormulaRecalculation(true);
        return true;
    }

    public boolean examine() {
        if (this.ex == null && this.workbook != null)
            return true;
        return false;
    }

    private boolean examineSheetRow(int index) {
        if (index < 0 || index > this.sheet.getLastRowNum())
            return false;
        return true;
    }

    public void setSheetName(String sheetName) {
        this.workbook.setSheetName(0, sheetName);
    }


    public void addRows(int sheetNo, int fromRowStartIndex, List<List<String>> areaValues, List<Integer> sumList,
                        Map<Integer, String> formulas, boolean needSum, String fileName) throws IOException {
        exception();
        if (!examine() ||
                !initSheet(sheetNo) ||
                !examineSheetRow(fromRowStartIndex)) {
            throw new IOException();
        }
        Row sumRow = null;
        if (needSum) {
            sumRow = this.sheet.createRow(fromRowStartIndex + 1);
        }
        if (areaValues != null) {
            CellStyle firstCellStyle = null;
            Row existRow = this.sheet.getRow(1000);
            boolean existsCell = false;
            if (existRow != null && existRow.getCell(0) != null) {
                firstCellStyle = existRow.getCell(0).getCellStyle();
                existsCell = true;
            }
            for (int i = 0; i < areaValues.size(); i++) {
                Row row;
                if (existsCell) {
                    row = this.sheet.getRow(fromRowStartIndex + i + 2);
                } else {
                    row = this.sheet.createRow(fromRowStartIndex + i + 2);
                }
                List<String> curList = areaValues.get(i);
                if (curList != null) {
                    int cellIndex = 0;
                    for (String str : curList) {
                        Cell cell = row.getCell(cellIndex);
                        if (formulas != null && formulas.get(cellIndex) != null) {
                            if (StringUtils.isNull(cell)) {
                                cell = row.createCell(cellIndex, CellType.FORMULA);
                            }
                            String formulaStr = formulas.get(cellIndex);
                            String[] curArr = formulaStr.split("\\$");
                            String realFormula = "";
                            for (int curIndex = 0; curIndex < curArr.length; curIndex++) {
                                if (curIndex % 2 == 0) {
                                    char charIndex = (char) (Integer.parseInt(curArr[curIndex]) + 65);
                                    realFormula += charIndex + "" + (fromRowStartIndex + i + 3);
                                } else {
                                    realFormula += curArr[curIndex];
                                }
                            }
                            cell.setCellFormula(realFormula);
                        } else {
                            if (sumList != null && sumList.contains(cellIndex)) {
                                if (StringUtils.isNull(cell)) {
                                    cell = row.createCell(cellIndex, CellType.NUMERIC);
                                }
                                str = (StringUtils.isEmpty(str)) ? "0" : str;
                                cell.setCellValue(new BigDecimal(str).doubleValue());
                            } else {
                                if (StringUtils.isNull(cell)) {
                                    cell = row.createCell(cellIndex, CellType.STRING);
                                }
                                cell.setCellValue(str);
                            }
                        }
                        if (!existsCell) {
                            cell.setCellStyle(style);
                        }
                        if (firstCellStyle != null && cellIndex == 0) {
                            cell.setCellStyle(firstCellStyle);
                        }
                        cellIndex++;
                    }
                }
            }
            if (needSum && sumList != null && sumRow != null) {
                List<String> curList = areaValues.get(0);
                for (int celIndex = 0; celIndex < curList.size(); celIndex++) {
                    if (sumList.contains(celIndex)) {
                        char excelIndex = (char) (celIndex + 65);
                        String formula = "SUM(" + excelIndex + (fromRowStartIndex + 3)
                                + ":" + excelIndex + (fromRowStartIndex + 2 + areaValues.size()) + ")";
                        Cell cell = sumRow.createCell(celIndex, CellType.FORMULA);
                        cell.setCellStyle(style);
                        cell.setCellFormula(formula);
                        continue;
                    }
                    Cell cell = sumRow.createCell(celIndex, CellType.STRING);
                    cell.setCellStyle(style);
                    if (celIndex == 0) {
                        if (fileName.contains("农用地")) {
                            cell.setCellValue("农用地小计");
                        } else if (fileName.contains("建设用地")) {
                            cell.setCellValue("建设用地小计");
                        } else {
                            cell.setCellValue("合计");
                        }
                    } else {
                        cell.setCellValue("");
                    }
                }
            }
            HSSFFormulaEvaluator.evaluateAllFormulaCells(this.workbook);
        }
    }

    public void fillCellByIndex(int sheetNo, Map<String, String> fillValue) throws IOException {
        exception();
        if (!examine() || !initSheet(sheetNo)) {
            throw new IOException();
        }
        if (fillValue != null) {
            Iterator it = fillValue.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry<String, String>) it.next();
                String index[] = entry.getKey().split(",");
                Row row = this.sheet.getRow(Integer.valueOf(index[0]));
                Cell cell = row.getCell(Integer.valueOf(index[1]));
                if (cell.getCellType() == CellType.NUMERIC) {
                    if (entry.getValue() == null || "".equals(entry.getValue())) {
                        cell.setCellValue(0);
                    } else {
                        cell.setCellValue(new BigDecimal(entry.getValue()).doubleValue());
                    }
                } else {
                    cell.setCellValue(getIfEmpty(entry.getValue()));
                }
            }
            HSSFFormulaEvaluator.evaluateAllFormulaCells(this.workbook);
        }
    }

    private static String getIfEmpty(Object val) {
        if (val == null) {
            return "";
        }
        return val.toString();
    }

    private boolean examineRange(CellRangeAddress address) {
        if (address == null || !examine())
            return false;
        int firstRowNum = address.getFirstRow();
        int lastRowNum = address.getLastRow();
        int firstColumnNum = address.getFirstColumn();
        int lastColumnNum = address.getLastColumn();
        if (firstRowNum == lastRowNum && firstColumnNum == lastColumnNum)
            return false;
        return true;
    }

    private void exception() throws EncryptedDocumentException, IOException {
        if (this.ex != null) {
            if (this.ex instanceof EncryptedDocumentException)
                throw new EncryptedDocumentException("无法读取的加密文件");
            if (this.ex instanceof IOException)
                throw new IOException();
            return;
        }
    }

    private void settingColumnWidth(int sourceSheetNo, int sheetNo) {
        if (sourceSheetNo < 0 || sourceSheetNo > this.workbook.getNumberOfSheets() || sheetNo < 0 || sheetNo > this.workbook
                .getNumberOfSheets())
            return;
        List<Row> rows = new ArrayList<Row>();
        for (int i = this.sheet.getFirstRowNum(); i <= this.sheet.getLastRowNum(); i++) {
            Row row = this.sheet.getRow(i);
            if (row != null)
                rows.add(row);
        }
        Row maxColumnRow = null;
        for (Row r : rows) {
            if (r != null) {
                if (maxColumnRow == null) {
                    maxColumnRow = r;
                } else {
                    if (r.getLastCellNum() > maxColumnRow.getLastCellNum()) {
                        maxColumnRow = r;
                    }
                }
            }
        }
        if (maxColumnRow != null) {
            int maxColumn = maxColumnRow.getLastCellNum();
            for (int j = 0; j < maxColumn; j++)
                this.workbook.getSheetAt(sheetNo).setColumnWidth(j, this.workbook.getSheetAt(sourceSheetNo).getColumnWidth(j));
        }
    }

    public synchronized boolean clearSheet(int sheetNo) {
        if (!examine())
            return false;
        int sheetNum;
        if (sheetNo < 0 || sheetNo > (sheetNum = this.workbook.getNumberOfSheets()))
            return false;
        for (int i = 0; i < sheetNum; i++) {
            if (i == sheetNo) {
                String sheetName = this.workbook.getSheetName(i);
                this.workbook.removeSheetAt(i);
                this.workbook.createSheet(sheetName);
            }
            if (i > sheetNo) {
                int offset = i - sheetNo;
                String sheetName = this.workbook.getSheetName(i - offset);
                Sheet newSheet = this.workbook.cloneSheet(i - offset);
                this.workbook.removeSheetAt(i - offset);
                this.workbook.setSheetName(this.workbook.getSheetIndex(newSheet), sheetName);
            }
        }
        if (!initSheet(sheetNo))
            return false;
        return true;
    }

    public byte[] getBytes() {
        if (!examine())
            return null;
        try {
            ByteArrayOutputStream ops = new ByteArrayOutputStream();
            this.workbook.write(ops);
            return ops.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean equals(Object o) {
        if (o == null)
            return false;
        if (o == this)
            return true;
        if (!(o instanceof ExcelTemplate))
            return false;
        if ((examine() ^ ((ExcelTemplate) o).examine()))
            return false;
        return this.path.equals(((ExcelTemplate) o).path);
    }

    public int hashCode() {
        int hash = this.path.hashCode();
        return hash >>> 16 ^ hash;
    }

    public String toString() {
        return "ExcelTemplate from " + this.path + " is " + (
                examine() ? "effective" : "invalid");
    }
}
