package com.shengyu.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.concurrent.TimeUnit;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYYMMDDHHMMSS_S = "yyyyMMddHHmmssSS";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String HHMMSS = "HHmmss";
    public static final String HHMMSS_S = "HHmmssSS";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy年MM月dd", "yyyy年MM月dd日", "yyyy年MM", "yyyy年MM月",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static final String dateToStr(final Date date) {
        if (null != date) {
            return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(date);
        } else {
            return null;
        }
    }

    public static int getWeek(Date date) {
        if (null != date) {
            GregorianCalendar g = new GregorianCalendar();
            g.setTime(date);
            return g.get(Calendar.WEEK_OF_YEAR); //获得周数
        }
        return 0;
    }

    public static long getDaysPoor(String t1, String t2) {
        long diff = parseDate(t2).getTime() - parseDate(t1).getTime();
        long nd = 1000 * 24 * 60 * 60;
        return diff / nd + 1;
    }

    public static long getWeeksPoor(String t1, String t2) {
        long diff = parseDate(t2).getTime() - parseDate(t1).getTime();
        long nd = 1000 * 24 * 60 * 60 * 7;
        return diff / nd + (diff % nd == 0 ? 0 : 1);
    }

    public static long getMinutesPoor(String t1) {
        long diff = DateUtils.getNowDate().getTime() - dateTime("yyyyMMddHHmmss", t1).getTime();
        long nd = 1000 * 60;
        return diff / nd + (diff % nd == 0 ? 0 : 1);
    }

    public static long getMinutesPoor(Long t2) {
        long diff = DateUtils.getNowDate().getTime() - t2;
        long nd = 1000 * 60;
        return diff / nd + (diff % nd == 0 ? 0 : 1);
    }

    public static String getDateStr(String time, int addTimes, String unitType) {
        Calendar c = Calendar.getInstance();
        c.setTime(parseDate(time));
        c.add("2".equals(unitType) ? Calendar.WEEK_OF_YEAR : Calendar.DATE, addTimes);
        return "2".equals(unitType) ? String.valueOf(c.get(Calendar.WEEK_OF_YEAR)) : dateTime(c.getTime());
    }

    public static int getAgeByBirth(Date birthday) {

        //获取当前时间
        Calendar cal = Calendar.getInstance();

        //获取出生日期的Calendar对象
        Calendar bir = Calendar.getInstance();
        bir.setTime(birthday);
        //如果出生日期大于当前日期，则返回0
        if (cal.before(birthday)) {
            return 0;
        }
        //取出当前年月日
        int nowYear = cal.get(Calendar.YEAR);
        int nowMonth = cal.get(Calendar.MONTH);
        int nowDay = cal.get(Calendar.DAY_OF_MONTH);

        //取出出生日期的年月日
        int birthYear = bir.get(Calendar.YEAR);
        int birthMonth = bir.get(Calendar.MONTH);
        int birthDay = bir.get(Calendar.DAY_OF_MONTH);

        //计算年份
        int age = nowYear - birthYear;

        //计算月份和日，看看是否大于当前月日，如果小于则减去一岁
        if (nowMonth < birthMonth || (nowMonth == birthMonth && nowDay < birthDay)) {
            age--;
        }
        return age;
    }

    public static String getCurrentDate(String patten) {
        SimpleDateFormat sdf = new SimpleDateFormat(patten);
        return sdf.format(new Date());
    }


    public static Date instance() {
        return new Date();
    }

    public static Long getSubDay(Date date1, Date date2) {
        Long sub = date1.getTime() - date2.getTime();
        return TimeUnit.MILLISECONDS.toDays(sub);
    }

    public static int getSubMon(Date date1, Date date2) {
        LocalDate d1 = LocalDateTime.ofInstant(date1.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate d2 = LocalDateTime.ofInstant(date2.toInstant(), ZoneId.systemDefault()).toLocalDate();
        Period period = Period.between(d1, d2);
        int dayMon = period.getDays() > 0 ? 1 : 0;
        return period.getYears() * 12 + period.getMonths() + dayMon;
    }

    public static int getSubYear(Date date1, Date date2) {
        LocalDate d1 = LocalDateTime.ofInstant(date1.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate d2 = LocalDateTime.ofInstant(date2.toInstant(), ZoneId.systemDefault()).toLocalDate();
        Period period = Period.between(d1, d2);
        int t1 = period.getMonths() > 0 ? 1 : 0;
        int t2 = period.getDays() > 0 ? 1 : 0;
        return period.getYears() + ((t1+t2) > 0 ? 1 : 0);
    }

    public static String getYesterday() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
        return sdf.format(c.getTime());
    }

    public static String getSubDay(int days) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, days);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
        return sdf.format(c.getTime());
    }

    public static String getSubMon(int mon, String date) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, Integer.parseInt(date.substring(0, 4)));
        c.set(Calendar.MONTH, Integer.parseInt(date.substring(5)) - 1);
        c.add(Calendar.MONTH, mon);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM);
        return sdf.format(c.getTime());
    }

    public static long getMonPoor(String date1, String date2) {
        LocalDate d1 = LocalDateTime.ofInstant(parseDate(date1).toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate d2 = LocalDateTime.ofInstant(parseDate(date2).toInstant(), ZoneId.systemDefault()).toLocalDate();
        Period period = Period.between(d1, d2);
        int dayMon = period.getDays() > 0 ? 1 : 0;
        return period.getYears() * 12 + period.getMonths() + dayMon;
    }

    public static String getQuarter(String date) {
        int curMonth = Integer.parseInt(date.substring(5));
        String year = date.substring(0, 4);
        if (curMonth >= 1 && curMonth < 4) {
            return year + "-01";
        }
        if (curMonth >= 4 && curMonth < 7) {
            return year + "-04";
        }
        if (curMonth >= 7 && curMonth < 10) {
            return year + "-07";
        }
        return year + "-10";
    }

    public static Date getLastMonthDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    public static String getLastMonthDay(String dateStr) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, Integer.parseInt(dateStr.substring(0, 4)));
        c.set(Calendar.MONTH, Integer.parseInt(dateStr.substring(5)) - 1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return DateUtils.dateTime(c.getTime());
    }

    public static Date getAddDate(Date date,int times,String payType,int tt) {
        int addMonth = times;
        if("3".equals(payType)){
            addMonth = times * 3;
        }else if("4".equals(payType)){
            addMonth = times * 6;
        }else if("5".equals(payType)){
            addMonth = times * 12;
        }else if("6".equals(payType)){
            addMonth = times * tt;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, addMonth);
        return c.getTime();
    }
}
