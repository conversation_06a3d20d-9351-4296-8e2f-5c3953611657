package com.shengyu.common.utils;

import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.spring.SpringUtils;

/**
 * @Author: SS
 * @Date: 2023/08/09/16:29
 * @Description:
 */
public class ConfigUtils {

    public static String getTenantId() {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String tenantKey = "temp_manage_tenant_id_" + SecurityUtils.getUsername();
        String tenantId = redisCache.getCacheObject(tenantKey);
        if (StringUtils.isEmpty(tenantId)) {
            tenantId = SecurityUtils.getLoginUser().getUser().getTenantId();
            redisCache.setCacheObject(tenantKey, tenantId);
        }
        return tenantId;
    }

    public static Long getDeptId() {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Long deptId = redisCache.getCacheObject("temp_manage_dept_id_" + SecurityUtils.getUsername());
        if (StringUtils.isNull(deptId)) {
            deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        }
        return deptId;
    }

}
