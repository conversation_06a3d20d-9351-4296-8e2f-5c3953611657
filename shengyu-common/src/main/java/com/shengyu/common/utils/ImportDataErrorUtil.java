package com.shengyu.common.utils;

import com.shengyu.common.constant.RedisConstants;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.bean.ImportDataErrorInfo;
import com.shengyu.common.utils.spring.SpringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * description: importDataErrorUtil <br>
 * date: 2021/7/29 18:09 <br>
 *
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public class ImportDataErrorUtil {
    private static RedisCache redisCache = SpringUtils.getBean("redisCache");

    public static String setError(ImportDataErrorInfo errorInfo) {
        Map<String, Object> errorData = new HashMap();
        errorData.put("list", errorInfo.getErrorList());
        errorData.put("errorMap", errorInfo.getErrorMap());
        String redisKey = errorInfo.getRedisKey();
        redisCache.setCacheMap(redisKey, errorData);
        redisCache.expire("excelError", RedisConstants.ERROR_DATA_EXPIRATION, TimeUnit.SECONDS);
        String failureMsg = "很抱歉，导入失败！共 " + errorInfo.getTotalNum() + "条数据,导入成功:"
                + (errorInfo.getTotalNum() - errorInfo.getFailureNum()) + "条;" + "更新数据:" + errorInfo.getUpdateNum()
                + "条,插入数据:" + errorInfo.getInsertNum() + "条";
        return failureMsg;
    }


}
