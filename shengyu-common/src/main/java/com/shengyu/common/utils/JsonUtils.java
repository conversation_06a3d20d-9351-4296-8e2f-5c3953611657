package com.shengyu.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * @Author: SS
 * @Date: 2022/03/24/11:19
 * @Description: json工具类
 */
public class JsonUtils {

    public static JSONObject getJsonInstance() {
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    public static JSONObject getCommonAppIdJson(String appId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyId", appId);
        return jsonObject;
    }

    public static JSONObject getCommonKeyJson(String appId, String type) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyId", appId);
        jsonObject.put("type", type);
        return jsonObject;
    }

    public static JSONObject toJson(String jsonStr) {
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        return jsonObject;
    }

    public static JSONObject toJson(Object obj) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", JSONObject.toJSONString(obj));
        return jsonObject;
    }

    public static JSONObject toStandardJson(Object obj) {
        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.put(Number.class, ToStringSerializer.instance);
        serializeConfig.put(Long.class, ToStringSerializer.instance);
        serializeConfig.put(Long.TYPE, ToStringSerializer.instance);
        String jsonStr = JSONObject.toJSONString(obj, serializeConfig, SerializerFeature.WriteDateUseDateFormat);
        return JSONObject.parseObject(jsonStr);
    }

    public static Object getValue(String jsonStr, String key) {
        JSONObject jsonObject = toJson(jsonStr);
        return jsonObject.get(key);
    }

    public static String getJsonString(Object obj) {
        return JSONObject.toJSONString(obj);
    }

    public static boolean isSuccess(JSONObject json) {
        return "200".equals(json.getString("ret"));
    }
}
