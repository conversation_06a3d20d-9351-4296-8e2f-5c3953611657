package com.shengyu.common.utils;

public class CommonUtils {

    /**
     * 判断文件名是否带盘符，重新处理
     *
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName) {
        //判断是否带有盘符信息
        // Check for Unix-style path
        int unixSep = fileName.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = fileName.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1) {
            // Any sort of path separator found...
            fileName = fileName.substring(pos + 1);
        }
        String fileType = "";
        if (fileName.lastIndexOf(".") > -1) {
            fileType = fileName.substring(fileName.lastIndexOf("."));
            fileName = fileName.substring(0, fileName.lastIndexOf("."));
        }
        //替换上传文件名字的特殊字符
        fileName = fileName.replace("=", "").replace(",", "")
                .replace("&", "").replace("#", "").replace(".", "");
        //替换上传文件名字中的空格
        fileName = fileName.replaceAll("\\s", "");
        fileName += fileType;
        return fileName;
    }
}