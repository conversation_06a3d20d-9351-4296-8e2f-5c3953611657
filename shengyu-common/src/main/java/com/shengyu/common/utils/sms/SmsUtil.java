package com.shengyu.common.utils.sms;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.shengyu.common.config.CapitalConfig;

/**
 * description: SmsUtil <br>
 * date: 2021/7/27 10:08 <br>
 *
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public class SmsUtil {
    /**
     * 产品名称:云通信短信API产品,开发者无需替换
     */
    static final String product = "Dysmsapi";
    /**
     * 产品域名,开发者无需替换
     */
    static final String domain = "dysmsapi.aliyuncs.com";

    /**
     * TODO 此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
     */
    static String accessKeyId = "LTAI5tB5Vge4uk4Zm7czp3ap";
    static String accessKeySecret = "******************************";

    public static boolean sendSms(String phone, JSONObject templateParamJson, SmsEnum smsEnum) throws Exception {
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        // Client代理参数设置
        Config config = new Config();
        // 从环境变量获取RAM用户的AccessKey ID
        config.setAccessKeyId(accessKeyId);
        // 从环境变量获取RAM用户的AccessKey Secret
        config.setAccessKeySecret(accessKeySecret);
        // 地域ID
        config.setRegionId("cn-hangzhou");
        if ("none".equals(CapitalConfig.getSmsProxyUrl())) {//无需代理跳转
            // 配置 Endpoint
            config.endpoint = domain;
        } else {
            // Client代理参数设置
            config.setHttpProxy(CapitalConfig.getSmsProxyUrl());
            config.setHttpsProxy(CapitalConfig.getSmsProxyUrl());
            config.setNoProxy("127.0.0.1,localhost");
        }
        Client client = new Client(config);

//        //初始化acsClient,暂不支持region化
//        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
//        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
//        IAcsClient acsClient = new DefaultAcsClient(profile);

        //验证json参数
        validateParam(templateParamJson, smsEnum);

        //组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        //必填:待发送手机号
        request.setPhoneNumbers(phone);
        //必填:短信签名-可在短信控制台中找到
        request.setSignName(smsEnum.getSignName());
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(smsEnum.getTemplateCode());
        //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
        request.setTemplateParam(templateParamJson.toJSONString());
        boolean result = false;
        // 获取响应对象
        RuntimeOptions runtime = new RuntimeOptions();
        SendSmsResponse sendSmsResponse = client.sendSmsWithOptions(request, runtime);
        if ("OK".equals(sendSmsResponse.getBody().getCode())) {
            result = true;
        }
        return result;
    }

    private static void validateParam(JSONObject templateParamJson, SmsEnum smsEnum) {
        String keys = smsEnum.getKeys();
        String[] keyArr = keys.split(",");
        for (String item : keyArr) {
            if (!templateParamJson.containsKey(item)) {
                throw new RuntimeException("模板缺少参数：" + item);
            }
        }
    }
}
