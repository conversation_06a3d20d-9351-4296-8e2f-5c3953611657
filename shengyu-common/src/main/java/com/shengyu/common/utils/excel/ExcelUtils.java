package com.shengyu.common.utils.excel;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @Author: SS
 * @Date: 2022/05/12/9:31
 * @Description: excel导出工具类
 */
public class ExcelUtils {

    //生成excel实体
    public static ExcelEntity export(String destPath, String fileName, Map<String, String> fillVal, List<List<String>> rows,
                                     int rowStart, List<Integer> sumArrList, Map<Integer, String> formulas,
                                     boolean needSum) throws Exception {
        ExcelEntity entity = new ExcelEntity();
        try {
            ExcelTemplate excel = new ExcelTemplate(destPath);
            if (fillVal != null) {
                excel.fillCellByIndex(0, fillVal);
            }
            if (rows != null) {
                excel.addRows(0, rowStart - 1, rows, sumArrList, formulas, needSum, fileName);
            }
            entity.setData(excel.getBytes());
            entity.setName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("生成excel失败");
        }
        return entity;
    }

    //生成excel实体
    public static ExcelEntity export(String destPath, String fileName, List<List<String>> rows,
                                     int rowStart, String sheetName) throws Exception {
        ExcelEntity entity = new ExcelEntity();
        try {
            ExcelTemplate excel = new ExcelTemplate(destPath);
            excel.addRows(0, rowStart - 1, rows, null, null, false, fileName);
            excel.setSheetName(sheetName);
            entity.setData(excel.getBytes());
            entity.setName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("生成excel失败");
        }
        return entity;
    }

    //生成excel实体
    public static ExcelEntity export(File file, String fileName, Map<String, String> fillVal, List<List<String>> rows,
                                     int rowStart, List<Integer> sumArrList, Map<Integer, String> formulas,
                                     boolean needSum) throws Exception {
        ExcelEntity entity = new ExcelEntity();
        try {
            ExcelTemplate excel = new ExcelTemplate(file);
            if (fillVal != null) {
                excel.fillCellByIndex(0, fillVal);
            }
            if (rows != null) {
                excel.addRows(0, rowStart - 1, rows, sumArrList, formulas, needSum, fileName);
            }
            entity.setData(excel.getBytes());
            entity.setName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("生成excel失败");
        }
        return entity;
    }

    //生成excel实体
    public static ExcelEntity export(InputStream ins, String fileName, Map<String, String> fillVal,
                                     List<List<String>> rows,
                                     int rowStart, List<Integer> sumArrList, Map<Integer, String> formulas,
                                     boolean needSum) throws Exception {
        ExcelEntity entity = new ExcelEntity();
        try {
            ExcelTemplate excel = new ExcelTemplate(ins);
            if (fillVal != null) {
                excel.fillCellByIndex(0, fillVal);
            }
            if (rows != null) {
                excel.addRows(0, rowStart - 2, rows, sumArrList, formulas, needSum, fileName);
            }
            entity.setData(excel.getBytes());
            entity.setName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("生成excel失败");
        }
        return entity;
    }

}
