package com.shengyu.common.utils;

/**
 * @Author: SS
 * @Date: 2023/10/12/11:25
 * @Description:
 */
public class SerialNumUtils {

    private static final String[] CHINESE_NUMBER = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    public static String convertNo(int index, int level) {
        if (index >= 27) {
            return String.valueOf(index);
        }
        if (level <= 2) {
            if (index < 10) {
                return convertVal(CHINESE_NUMBER[index], level);
            } else if (index == 10) {
                return convertVal("十", level);
            } else if (index > 10 && index < 20) {
                return convertVal("十" + CHINESE_NUMBER[index - 10], level);
            } else {
                String strVal = String.valueOf(index);
                String cVal = "";
                for (int i = 0; i < 2; i++) {
                    if (i == 0) {
                        cVal += CHINESE_NUMBER[Integer.parseInt(String.valueOf(strVal.charAt(i)))] + "十";
                    } else {
                        cVal += CHINESE_NUMBER[Integer.parseInt(String.valueOf(strVal.charAt(i)))];
                    }
                }
                return convertVal(cVal, level);
            }
        } else if (level <= 4) {
            return convertVal(String.valueOf(index), level);
        } else if (level <= 6) {
            char c = (char) (index + 64);
            return convertVal(String.valueOf(c), level);
        }
        char c = (char) (index + 95);
        return convertVal(String.valueOf(c), level);
    }

    private static String convertVal(String val, int level) {
        if (level % 2 == 0) {
            return "(" + val + ")";
        }
        return val;
    }
}
