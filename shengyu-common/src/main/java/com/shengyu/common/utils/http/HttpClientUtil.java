package com.shengyu.common.utils.http;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.utils.JsonUtils;
import com.shengyu.common.utils.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

/**
 * @Author: SS
 * @Date: 2022/03/24/11:02
 * @Description: http请求工具
 */
public class HttpClientUtil {

    /***
     * @Description: http请求
     * @Param: [url, param, charset, contentType]
     * @return: byte[]
     * @Author: SS
     * @Date: 2022/3/24
     */
    public static byte[] httpSendPost(String url, String param, String charset, ContentType contentType,
                                      boolean isSSL,String cookie) throws Exception {
        byte[] b = null;
        // 创建httpclient对象
        CloseableHttpClient httpClient;
        if (isSSL) {
            httpClient = HttpClients.custom().setSSLSocketFactory(new SSLConnectionSocketFactory(SSLContexts.custom()
                    .loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(), NoopHostnameVerifier.INSTANCE)).build();
        } else {
            httpClient = HttpClients.createDefault();
        }

        // 创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        // 设置参数到请求对象中
        StringEntity stringEntity = new StringEntity(param, contentType);
        stringEntity.setContentType(contentType.toString());
        stringEntity.setContentEncoding(charset);
        httpPost.setEntity(stringEntity);
        //超时设置
        RequestConfig requestConfig;
        if(StringUtils.isNotEmpty(cookie)){
            requestConfig = RequestConfig.custom().setConnectionRequestTimeout(5000)
                    .setSocketTimeout(12000).setConnectTimeout(12000).setCookieSpec(cookie).build();
        }else{
            requestConfig = RequestConfig.custom().setConnectionRequestTimeout(5000)
                    .setSocketTimeout(12000).setConnectTimeout(12000).build();
        }
        httpPost.setConfig(requestConfig);
        // 执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = httpClient.execute(httpPost);
        // 获取结果实体
        // 判断网络连接状态码是否正常(0--200都数正常)
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            b = EntityUtils.toByteArray(response.getEntity());
        }
        // 释放链接
        response.close();
        return b;
    }

    public static String httpSendPostStr(String url, String param, String charset, ContentType contentType) throws Exception {
        byte[] b = httpSendPost(url, param, charset, contentType, false,null);
        if (b != null) {
            return new String(b,charset);
        }
        return null;
    }

    public static String httpSendPostForSSL(String url, String param, String charset, ContentType contentType) throws Exception {
        byte[] b = httpSendPost(url, param, charset, contentType, true,null);
        if (b != null) {
            return new String(b,charset);
        }
        return null;
    }

    public static String httpSendPostForCook(String url, String param, String charset, ContentType contentType,
                                             String cookie) throws Exception {
        byte[] b = httpSendPost(url, param, charset, contentType, true,cookie);
        if (b != null) {
            return new String(b,charset);
        }
        return null;
    }

    /**
     * @Description: 校验请求是否成功
     * @Param: [responseStr]
     * @return: boolean
     * @Author: SS
     * @Date: 2022/3/24
     */
    public static boolean isSuccess(String responseStr) {
        JSONObject json = JsonUtils.toJson(responseStr);
        if (json != null && "00".equals(json.getString("BK_STATUS"))) {
            return true;
        }
        return false;
    }

    /***
     * @Description: 处理返回的json数据
     * @Param: [responseStr]
     * @return: java.lang.Object
     * @Author: SS
     * @Date: 2022/3/24
     */
    public static Object getData(String responseStr) {
        JSONObject json = JsonUtils.toJson(responseStr);
        Object result = null;
        if (json != null && "00".equals(json.getString("BK_STATUS"))) {
            result = json.get("DATA");
        }
        return result;
    }
}
