package com.shengyu.common.utils;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import javax.servlet.ServletOutputStream;
import java.io.*;

/**
 * @Author:siler
 * @Date:2022/2/16 16:06
 */
public class FtpUtils {

    private String encoding = System.getProperty("file.encoding");
    public FTPClient ftpClient = null;
    private String url;
    private String port;
    private String username;
    private String password;

    /**
     * @Date: 2022/2/16 16:08
     * @Description: 默认为21端口
     **/
    public FtpUtils(String url, String username, String password) {
        this.url = url;
        this.port = "21";
        this.username = username;
        this.password = password;
    }

    public FtpUtils(String url, String port, String username, String password) {
        this.url = url;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    public FTPClient getFTPClient() {
        int reply;
        if (this.ftpClient == null || !this.ftpClient.isConnected()) {
            this.ftpClient = new FTPClient();
            try {
                this.ftpClient.connect(url, Integer.parseInt(port));
                this.ftpClient.login(username, password);
                this.ftpClient.enterLocalPassiveMode();
                // 设置文件传输类型为二进制传输
                this.ftpClient.setFileType(ftpClient.BINARY_FILE_TYPE);
                this.ftpClient.setControlEncoding(encoding);
                reply = ftpClient.getReplyCode();
                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftpClient.disconnect();
                    return null;
                }
                return this.ftpClient;
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return this.ftpClient;
        }
    }

    /**
     * @param remotePath ftp目录
     * @param fileName   指定下载的文件
     * @param localPath  本地目录
     * @Date: 2022/2/16 16:26
     * @Description: 指定ftp文件名下载到本地
     **/

    public boolean downFile(String remotePath, String fileName, String localPath) {
        getFTPClient();
        if (!isCreateFtpClient(this.ftpClient)) {
            return false;
        }
        try {
            this.ftpClient.changeWorkingDirectory(remotePath);
            FTPFile[] remoteFiles = this.ftpClient.listFiles();
            for (FTPFile rf : remoteFiles) {
                if (rf.getName().equals(fileName)) {
                    // 创建本地存储路径
                    File lf = new File(localPath + File.separator + rf.getName());
                    if (!lf.exists()) {
                        lf.createNewFile();
                    }
                    FileOutputStream lfos = new FileOutputStream(lf);
                    // 通过文件检索系统,将文件写入流
                    this.ftpClient.retrieveFile(rf.getName(), lfos);
                    System.out.println("下载完毕");
                    lfos.close();
                }
            }
            this.ftpClient.logout();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        }
        return true;
    }

    //将文件下载进入流
    public boolean downFile(String remotePath, String fileName, ServletOutputStream outputStream, Boolean delete) {
        getFTPClient();
        if (!isCreateFtpClient(this.ftpClient)) {
            return false;
        }
        try {
            this.ftpClient.changeWorkingDirectory(remotePath);
            FTPFile[] remoteFiles = this.ftpClient.listFiles();
            for (FTPFile rf : remoteFiles) {
                if (rf.getName().equals(fileName)) {
                    // 通过文件检索系统,将文件写入流
                    this.ftpClient.retrieveFile(rf.getName(), outputStream);
                    if (delete) {
                        ftpClient.deleteFile(rf.getName());
                    }
                    break;
                }
            }
            this.ftpClient.logout();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        }
        return true;
    }


    /**
     * @param remotePath ftp目录
     * @param localPath  本地目录
     * @param prefix     前缀
     * @param suffix     后缀
     * @Date: 2022/2/16 16:42
     * @Description: 下载所有文件到本地目录, 不包含目录
     **/
    public boolean downAllPathFile(String remotePath, String localPath, String prefix, String suffix) {
        boolean result = false;
        if (prefix == null || prefix.trim().equals("")) {
            //return this.downAllPathFileSuffix(remotePath, localPath, suffix);
            return result;
        } else if (suffix == null || suffix.trim().equals("")) {
            //return this.downAllPathFilePrefix(remotePath, localPath, prefix);
            return result;
        } else {
            getFTPClient();
            if (!isCreateFtpClient(this.ftpClient)) {
                return false;
            }
            try {
                this.ftpClient.changeWorkingDirectory(remotePath);
                FTPFile[] remoteFiles = this.ftpClient.listFiles();
                for (FTPFile rf : remoteFiles) {
                    if (rf.isFile() && rf.getName().contains(prefix) && rf.getName().endsWith(suffix)) {
                        File lf = new File(localPath + "/" + rf.getName());
                        FileOutputStream lfos = new FileOutputStream(lf);
                        // 通过文件检索系统,将文件写入流
                        this.ftpClient.retrieveFile(rf.getName(), lfos);

                        lfos.close();
                    }
                }
                this.ftpClient.logout();
                result = true;
            } catch (IOException e) {
                e.printStackTrace();
                return result;
            } finally {
                if (ftpClient.isConnected()) {
                    try {
                        ftpClient.disconnect();
                    } catch (Exception ee) {
                        ee.printStackTrace();
                    }
                }
            }
            return result;
        }
    }

    public boolean downAllPathFileAndDelete(String remotePath, String localPath) {
        boolean result = false;
            getFTPClient();
            if (!isCreateFtpClient(this.ftpClient)) {
                return false;
            }
            try {
                this.ftpClient.changeWorkingDirectory(remotePath);
                FTPFile[] remoteFiles = this.ftpClient.listFiles();
                for (FTPFile rf : remoteFiles) {
                    if (rf.isFile()) {
                        File lf = new File(localPath + "/" + rf.getName());
                        FileOutputStream lfos = new FileOutputStream(lf);
                        // 通过文件检索系统,将文件写入流
                        this.ftpClient.retrieveFile(rf.getName(), lfos);
                        ftpClient.deleteFile(rf.getName());
                        lfos.close();
                    }
                }
                this.ftpClient.logout();
                result = true;
            } catch (IOException e) {
                e.printStackTrace();
                return result;
            } finally {
                if (ftpClient.isConnected()) {
                    try {
                        ftpClient.disconnect();
                    } catch (Exception ee) {
                        ee.printStackTrace();
                    }
                }
            }
            return result;
    }


    /**
     * @Date: 2022/2/16 16:51
     * @Description: 上传文件
     **/
    public boolean uploadFile(String remotePath, String filename, InputStream lin) {
        boolean result = false;
        getFTPClient();
        if (!this.isCreateFtpClient(this.ftpClient)) {
            return false;
        }
        try {
            boolean change = ftpClient.changeWorkingDirectory(remotePath);
            if (!change) {
                String[] dirTree = remotePath.split("/");
                String baseDir = "/"+ (StringUtils.isEmpty(dirTree[0]) ? dirTree[1] : dirTree[0]);
                for(String curDir : dirTree){
                    if(StringUtils.isEmpty(curDir)){
                        continue;
                    }
                    if(!("/"+curDir).equals(baseDir)){
                        baseDir += "/"+curDir;
                    }
                    change = ftpClient.changeWorkingDirectory(baseDir);
                    if(!change){
                        if (!ftpClient.makeDirectory(curDir)) { //创建目录
                            return false;
                        }
                    }
                    ftpClient.changeWorkingDirectory(baseDir);
                }
            }
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            result = ftpClient.storeFile(filename, lin);
            lin.close();
            ftpClient.logout();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * @param remotePath
     * @param localFilePath 完整的本地路径
     * @Date: 2022/2/16 16:56
     * @Description: FTP文件上传
     **/
    public boolean uploadFile(String remotePath, String localFilePath) {
        File lf = new File(localFilePath);
        try {
            FileInputStream lfis = new FileInputStream(lf);
            return uploadFile(remotePath, lf.getName(), lfis);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @Date: 2022/2/16 17:01
     * @Description: 删除远程文件信息
     **/
    public boolean deleteFile(String filePath, String fileName) {
        getFTPClient();
        boolean result = false;
        if (isCreateFtpClient(this.ftpClient)) {
            try {
                ftpClient.changeWorkingDirectory(filePath);
                result = ftpClient.deleteFile(fileName);
            } catch (IOException e) {
                e.printStackTrace();
                return result;
            } finally {
                if (ftpClient.isConnected()) {
                    try {
                        ftpClient.disconnect();
                    } catch (Exception ee) {
                        ee.printStackTrace();
                    }
                }
            }
        }
        return result;
    }


    /*
     * @Date: 2022/2/16 16:30
     * @Description: 是否创建ftp客户端
     **/
    public boolean isCreateFtpClient(FTPClient c) {
        if (c == null || !c.isConnected()) {
            return false;
        } else {
            return true;
        }

    }
}

