package com.shengyu.common.utils;

/**
 * 常量
 */
public class Constant {

    //任务状态
    public static enum PATROL_TASK_STATUS {
        UNPUBLISHI(1, "未发布"), PUBLISHI(2, "已发布"), CANCEL(3, "已撤销"),
        ONGOING(4, "进行中"), CHECKING(5, "检查中"), CHECK_FINISH(6, "已检查"),
        REFORMING(7, "整改中"), RECHECKING(8, "复检中"), CHECK_PASS(9, "检查通过"),
        RECHECK_PASS(10, "复检通过"), RECHECK_NO_PASS(11, "复检不通过");
        private Integer value;
        private String description;

        private PATROL_TASK_STATUS(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }

        public static String getDescByValue(Integer value) {
            PATROL_TASK_STATUS[] taskStatuses = PATROL_TASK_STATUS.values();
            for (PATROL_TASK_STATUS taskStatus : taskStatuses) {
                if (taskStatus.getValue().equals(value)) {
                    return taskStatus.getDescription();
                }
            }
            return "";
        }
    }

    //巡检记录房间状态
    public static enum RECORD_ROOM_STATUS {
        WAIT_CHECK(1, "待检查"), CHECKING(2, "检查中"),
        WAIT_CONFIRM_RECHECK(3, "待确认复检"),
        WAIT_RECEIVE_RECHECK(4, "待接收复检"), RECEIVE_RECHECK(5, "已接收复检"),
        CHECK_PASS(6, "检查通过"), WAIT_RECEIVE_ISSUE(7, "待接收下发"),
        WAIT_RECEIVE_REFORM(8, "待接收整改"), RECEIVE_REFORM(9, "接收整改"),
        WAIT_CONFIRM_REFORM(10, "待确认整改"), CONFIRM_REFORM(11, "确认整改"),
        RECHECK_PASS(12, "复检通过"), RECHECK_NO_PASS(13, "复检不通过"),
        WAIT_CONFIRM_RECHECK_FINISH(14, "待确认复检完成");
        private Integer value;
        private String description;

        private RECORD_ROOM_STATUS(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }

        public static String getDescByValue(Integer value) {
            RECORD_ROOM_STATUS[] roomStatuses = RECORD_ROOM_STATUS.values();
            for (RECORD_ROOM_STATUS status : roomStatuses) {
                if (status.getValue().equals(value)) {
                    return status.getDescription();
                }
            }
            return "";
        }
    }

    //接收状态
    public static enum ACCEPT_STATUS {
        ACCEPT(1, "已接收"), NOACCEPT(0, "未接收");
        private Integer value;
        private String description;

        private ACCEPT_STATUS(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }

        public static String getDescByValue(Integer value) {
            ACCEPT_STATUS[] acceptStatuses = ACCEPT_STATUS.values();
            for (ACCEPT_STATUS acceptStatus : acceptStatuses) {
                if (acceptStatus.getValue().equals(value)) {
                    return acceptStatus.getDescription();
                }
            }
            return "";
        }
    }

    //房间检查项状态
    public static enum ITEM_CHECK_STATUS {
        PASS(1, "通过"), NO_PASS(0, "检查不通过");
        private Integer value;
        private String description;

        private ITEM_CHECK_STATUS(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }
    }

    //组成员校色
    public static enum GROUP_ROLE {
        LEADER(1, "组长"), DEPUTY_LEADER(2, "副组长"), MEMBERT(3, "成员");
        private Integer value;
        private String description;

        private GROUP_ROLE(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }

        public static String getDescByValue(Integer value) {
            GROUP_ROLE[] groupRoles = GROUP_ROLE.values();
            for (GROUP_ROLE groupRole : groupRoles) {
                if (groupRole.getValue().equals(value)) {
                    return groupRole.getDescription();
                }
            }
            return "";
        }
    }

    //确认状态
    public static enum IS_CONFIRM_STATUS {
        YES(1, "已接收"), NO(0, "未接收");
        private Integer value;
        private String description;

        private IS_CONFIRM_STATUS(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }
    }

    //当前登录用户在某一个任务或者检查记录中角色
    public static enum TASK_USER_ROLE {
        PATROL_LEADER(1, "检查组组长"), PATROL_MEMBER(2, "检查组成员"),
        LAB_LEADER(3, "实验室负责人"), ROOM_LEADER(4, "房间负责人");
        private Integer value;
        private String description;

        private TASK_USER_ROLE(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }
    }

    //用户操作任务类型 1为接收检查，2为发布检查结果，3接收下发，4，接收整改，5，确认整改，6 确认复检，7，接收复检 8 复检完成 9、复检结果下发',
    public static enum USER_OPERATE_TYPE {
        ACCEPT_CHECK(1, "接收检查"), PUBLISH_PATROL_RECORD(2, "发布检查结果"), ACCEPT_ISSUE(3, "接收下发"),
        ACCEPT_REFORM(4, "接收整改"), CONFIRM_REFORM(5, "确认整改"), CONFIRM_RECHECK(6, "确认复检"),
        ACCEPT_RECHCK(7, "接收复检"), RECHECK_FINISH(8, "复检完成"), RECHECK_RESULT_ISSUE(9, "复检结果下发");
        private Integer value;
        private String description;

        private USER_OPERATE_TYPE(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }
    }

    //复检结果下发状态
    public static enum RECHECK_RESULT_ISSUE {
        YES(1, "是"), NO(0, "否");
        private Integer value;
        private String description;

        private RECHECK_RESULT_ISSUE(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getDescription() {
            return this.description;
        }
    }

}
