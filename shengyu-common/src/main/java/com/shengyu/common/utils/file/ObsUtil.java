package com.shengyu.common.utils.file;

import com.obs.services.ObsClient;
import com.obs.services.model.HttpMethodEnum;
import com.obs.services.model.TemporarySignatureRequest;
import com.obs.services.model.TemporarySignatureResponse;
import com.shengyu.common.utils.CommonUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.uuid.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * 华为云OBS工具类
 */
public final class ObsUtil {
    private static final Logger logger = LoggerFactory.getLogger(ObsUtil.class);
    private static final long EXPIRE_SECONDS = 3600L;
    private static final String PUBLIC_URL_PREFIX = "https://farmaffairs.obs.cn-north-9.myhuaweicloud.com/";

    // 使用volatile保证多线程可见性
    private static volatile String bucketName;
    private static volatile ObsClient obsClient;

    private ObsUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static void setBucketName(String bucketName) {
        ObsUtil.bucketName = Objects.requireNonNull(bucketName, "Bucket name cannot be null");
    }

    public static void setObsClient(ObsClient obsClient) {
        ObsUtil.obsClient = Objects.requireNonNull(obsClient, "ObsClient cannot be null");
    }

    /**
     * 上传MultipartFile到OBS
     */
    public static String upload(MultipartFile file, String fileDir) throws IOException {
        Objects.requireNonNull(file, "File cannot be null");
        Objects.requireNonNull(fileDir, "File directory cannot be null");

        String orgName = StringUtils.defaultIfEmpty(file.getOriginalFilename(), file.getName());
        orgName = CommonUtils.getFileName(orgName);

        String fileName = buildFileName(orgName);
        String fullPath = buildFullPath(fileDir, fileName);

        try (InputStream inputStream = file.getInputStream()) {
            obsClient.putObject(bucketName, fullPath, inputStream);
            return fullPath;
        } catch (IOException e) {
            logger.error("Failed to upload file to OBS", e);
            throw e;
        }
    }

    /**
     * 上传InputStream到OBS
     */
    public static String upload(InputStream fileIs, String fileDir, String fileType) {
        Objects.requireNonNull(fileIs, "File input stream cannot be null");
        Objects.requireNonNull(fileDir, "File directory cannot be null");
        Objects.requireNonNull(fileType, "File type cannot be null");

        String fileName = UUID.fastUUID().toString() + System.currentTimeMillis() + "." + fileType;
        String fullPath = buildFullPath(fileDir, fileName);

        obsClient.putObject(bucketName, fullPath, fileIs);
        return fullPath;
    }

    /**
     * 生成下载或预览URL
     */
    public static String downloadOrView(String fileName) {
        Objects.requireNonNull(fileName, "File name cannot be null");

        TemporarySignatureRequest request = new TemporarySignatureRequest(
                HttpMethodEnum.GET, EXPIRE_SECONDS);
        request.setBucketName(bucketName);
        request.setObjectKey(fileName);

        TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
        return response.getSignedUrl();
    }

    /**
     * 获取公开文件URL
     */
    public static String viewPublicFile(String fileName) {
        return PUBLIC_URL_PREFIX + Objects.requireNonNull(fileName);
    }

    private static String buildFileName(String originalName) {
        if (!originalName.contains(".")) {
            return originalName + UUID.fastUUID() + System.currentTimeMillis();
        }

        int dotIndex = originalName.lastIndexOf(".");
        return originalName.substring(0, dotIndex) + "_" +
                System.currentTimeMillis() +
                originalName.substring(dotIndex);
    }

    private static String buildFullPath(String fileDir, String fileName) {
        String normalizedDir = fileDir.endsWith("/") ? fileDir : fileDir + "/";
        normalizedDir = StrAttackFilter.filter(normalizedDir);
        return normalizedDir + fileName;
    }
}
