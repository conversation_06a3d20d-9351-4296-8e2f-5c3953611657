package com.shengyu.common.utils;

import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.core.domain.entity.SysUser;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: SS
 * @Date: 2023/06/14/17:34
 * @Description:
 */
public class SocketMsgUtil {

    public static SocketMsg createMsg(String type, SysUser user, String msg) {
        Map<String, Object> data = new HashMap<>();
        data.put("msgText", msg);
        SocketMsg socketMsg = new SocketMsg(type, String.valueOf(user.getUserId()), data);
        return socketMsg;
    }

    public static SocketMsg createMsg(String type, String userId, String msg) {
        Map<String, Object> data = new HashMap<>();
        data.put("msgText", msg);
        SocketMsg socketMsg = new SocketMsg(type, userId, data);
        return socketMsg;
    }

    public static SocketMsg createMsg(String type, SysUser user, String msg, String id, String status) {
        Map<String, Object> data = new HashMap<>();
        if ("currentProgress".equals(type)) {
            data.put("msgText", "当前进度：" + msg);
        } else {
            data.put("msgText", msg);
        }
        data.put("id", id);
        data.put("status", status);
        SocketMsg socketMsg = new SocketMsg(type, String.valueOf(user.getUserId()), data);
        return socketMsg;
    }

    public static SocketMsg createMsg(String type, SysUser user, String msg, String url) {
        Map<String, Object> data = new HashMap<>();
        data.put("msgText", msg);
        data.put("fileUrl", url);
        SocketMsg socketMsg = new SocketMsg(type, String.valueOf(user.getUserId()), data);
        return socketMsg;
    }
}
