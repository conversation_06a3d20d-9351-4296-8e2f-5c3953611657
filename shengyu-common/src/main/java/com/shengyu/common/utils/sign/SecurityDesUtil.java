package com.shengyu.common.utils.sign;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.DESedeKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2019-2-27 16:22
 */
public class SecurityDesUtil {

    private static final String localKey = "HNsy2024xxjsyxgs";

    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用RSA生成一对钥匙
     */
    public static KeyPair getKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 生成私钥
     */
    public static PrivateKey getPrivateKey(KeyPair key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        PrivateKey private1 = key.getPrivate();
        byte[] encoded = private1.getEncoded();
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        KeyFactory factory = KeyFactory.getInstance("RSA");
        return factory.generatePrivate(keySpec);
    }

    /**
     * 获取公钥
     */
    public static PublicKey getPublicKey(KeyPair keyPair) throws NoSuchAlgorithmException, InvalidKeySpecException {
        PublicKey public1 = keyPair.getPublic();
        byte[] encoded = public1.getEncoded();
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        KeyFactory factory = KeyFactory.getInstance("RSA");
        return factory.generatePublic(keySpec);
    }

    /**
     * 获取RSA密钥对
     *
     * @return base64编码的密钥对
     */
    public static Map<String, String> generateRsaKeys() {
        try {
            KeyPair keyPair = getKeyPair();
            //获取公钥
            PublicKey publicKey = getPublicKey(keyPair);
            //获取私钥
            PrivateKey privateKey = getPrivateKey(keyPair);
            String strpk = Base64.encode(publicKey.getEncoded());
            String strprivk = Base64.encode(privateKey.getEncoded());
            Map<String, String> result = new HashMap<>();
            result.put("public_key", strpk);
            result.put("private_key", strprivk);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }


    /**
     * 数字签名
     *
     * @param str    待签名的字符串
     * @param keyStr rsa密钥Base64字符
     * @return base64编码的签名信息
     */
    public static String signMethod(String str, String keyStr) throws NoSuchProviderException, NoSuchAlgorithmException,
            InvalidKeySpecException, SignatureException, InvalidKeyException {
        PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(Base64.decode(keyStr));
        KeyFactory keyf = KeyFactory.getInstance("RSA", "BC");
        PrivateKey privkey2 = keyf.generatePrivate(priPKCS8);
        return Base64.encode(signMethod(str, privkey2));
    }


    /**
     * 数字签名校验
     *
     * @param str    报文字节流
     * @param sign   签名字节流
     * @param pubKey 签名方RSA公钥(Base64编码)
     */
    public static boolean verifyMethod(byte[] str, byte[] sign, String pubKey) throws NoSuchAlgorithmException, InvalidKeyException,
            SignatureException, InvalidKeySpecException, NoSuchProviderException {
        X509EncodedKeySpec pubX509 = new X509EncodedKeySpec(Base64.decode(pubKey));
        KeyFactory keyf = KeyFactory.getInstance("RSA", "BC");
        PublicKey pubkey2 = keyf.generatePublic(pubX509);
        return verifyMethod(str, sign, pubkey2);
    }

    /**
     * 3DES加密
     *
     * @param content  待加密报文
     * @param password DES对称密钥（base64编码）
     * @return base64编码的密文
     */
    public static String tripleDESEncrypt(String content, String password) {
        byte[] passwords = Base64.decode(password);
        byte[] encryptBytes = tripleDESEncrypt(content.getBytes(StandardCharsets.UTF_8), passwords);
        return Base64.encode(encryptBytes);
    }

    /**
     * 3DES解密
     *
     * @param content  待解密报文字节流
     * @param password DES对称密钥（base64编码）
     * @return
     */
    public static byte[] tripleDESDecrypt(byte[] content, String password) {
        byte[] passwords = Base64.decode(password);
        return tripleDESDecrypt(content, passwords);
    }

    /**
     * 3DES加密
     *
     * @param content  待加密报文字节流
     * @param password DES对称密钥
     */
    public static byte[] tripleDESEncrypt(byte[] content, byte[] password) {
        try {
            DESedeKeySpec deskeyspec = new DESedeKeySpec(password);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("DESede");
            SecretKey secretKey = keyfactory.generateSecret(deskeyspec);
            Cipher cipher = Cipher.getInstance("DESede");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            return cipher.doFinal(content);
        } catch (Exception e) {
            throw new RuntimeException("DES加密发生异常", e);
        }
    }

    public static String localEncode(String base64String) {
        if (StringUtils.isEmpty(base64String)) {
            return null;
        }
        byte[] content = Base64.decode(base64String);
        byte[] encoded = desEncrypt(content, localKey.getBytes(StandardCharsets.UTF_8));
        return Base64.encode(encoded);
    }

    public static String localDecode(String base64String) {
        if (StringUtils.isEmpty(base64String)) {
            return null;
        }
        byte[] content = Base64.decode(base64String);
        byte[] decoded = desDecrypt(content, localKey.getBytes(StandardCharsets.UTF_8));
        return Base64.encode(decoded);
    }

    public static void main(String[] args) {


        String a = "ccb123456";
    }

    /**
     * 3DES解密
     *
     * @param content  待解密报文字节流
     * @param password DES对称密钥
     * @return
     */
    public static byte[] tripleDESDecrypt(byte[] content, byte[] password) {
        try {
            DESedeKeySpec deskeyspec = new DESedeKeySpec(password);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("DESede");
            SecretKey secretKey = keyfactory.generateSecret(deskeyspec);
            Cipher cipher = Cipher.getInstance("DESede");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            return cipher.doFinal(content);
        } catch (Exception e) {
            throw new RuntimeException("DES解密发生异常", e);
        }
    }

    /**
     * DES加密
     *
     * @param content  待加密报文字节流
     * @param password DES对称密钥
     * @return
     */
    public static byte[] desEncrypt(byte[] content, byte[] password) {
        try {
            DESKeySpec deskeyspec = new DESKeySpec(password);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = keyfactory.generateSecret(deskeyspec);
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            return cipher.doFinal(content);
        } catch (Exception e) {
            throw new RuntimeException("DES加密发生异常", e);
        }
    }

    /**
     * DES解密
     *
     * @param content  待解密报文字节流
     * @param password DES对称密钥
     */
    public static byte[] desDecrypt(byte[] content, byte[] password) {
        try {
            DESKeySpec deskeyspec = new DESKeySpec(password);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = keyfactory.generateSecret(deskeyspec);
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            return cipher.doFinal(content);
        } catch (Exception e) {
            throw new RuntimeException("DES解密发生异常", e);
        }
    }

    private static boolean verifyMethod(byte[] str, byte[] sign, PublicKey key) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(key);
        signature.update(str);
        return signature.verify(sign);
    }


    private static byte[] signMethod(String str, PrivateKey key) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(key);
        signature.update(str.getBytes(StandardCharsets.UTF_8));
        return signature.sign();
    }


    public static byte[] asc2bin(String hexString) {
        byte[] hexbyte = hexString.getBytes(StandardCharsets.UTF_8); // 得到系统默认编码的字节数组
        byte[] bitmap = new byte[hexbyte.length / 2];
        for (int i = 0; i < bitmap.length; i++) {
            hexbyte[i * 2] -= hexbyte[i * 2] > '9' ? 7 : 0;
            hexbyte[i * 2 + 1] -= hexbyte[i * 2 + 1] > '9' ? 7 : 0;
            bitmap[i] = (byte) ((hexbyte[i * 2] << 4 & 0xf0) | (hexbyte[i * 2 + 1] & 0x0f));
        }
        return bitmap;
    }


    public static Integer binarysToInteger(byte[] bytes) {
        StringBuffer a = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            a.append((int) bytes[i]);
        }
        return Integer.valueOf(a.toString());
    }

}
