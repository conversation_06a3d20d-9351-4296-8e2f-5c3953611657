package com.shengyu.common.utils.poi;

import com.shengyu.common.config.CapitalConfig;
import fr.opensagres.poi.xwpf.converter.xhtml.Base64EmbedImgManager;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.Picture;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jsoup.Jsoup;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;

/**
 * description: WordUtil <br>
 * date: 2021/9/7 15:50 <br>
 *
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public class WordUtil {
    private final static String LEARNING_HTML_PATH = CapitalConfig.getLearningHtmlPath();

    public static String doc2Html(MultipartFile file) throws IOException, ParserConfigurationException, TransformerException {
        String content = null;
        //判断是doc还是docx
        if (file.getOriginalFilename().endsWith(".docx")) {
            XWPFDocument document = new XWPFDocument(file.getInputStream());
            content = docxToHtml(document);
        } else {
            HWPFDocument wordDocument = new HWPFDocument(file.getInputStream());
            content = docToHtml(wordDocument);
        }
        org.jsoup.nodes.Document doc = Jsoup.parse(content);
        content = doc.html();
        return content;
      /*  try {
            File file1=new File( shengyuConfig.getLearningHtmlPath()+File.separator+path);
            if(!file1.getParentFile().exists()){
                file1.mkdirs();
            }
            String fileName=file.getOriginalFilename();
            fileName=fileName.substring(0,fileName.lastIndexOf(".")-1);
            File file2=new File(shengyuConfig.getLearningHtmlPath()+File.separator+path+File.separator+fileName+".html");
            file2.createNewFile();
            fos=new FileOutputStream(file2);
            bw=new BufferedWriter(new OutputStreamWriter(fos,Constants.UTF8));
            bw.write(content);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if(bw!=null){
                    bw.close();
                }
                if(fos!=null){
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }*/


    }

    /**
     * doc格式转html
     *
     * @param wordDocument
     * @return
     */
    public static String docToHtml(HWPFDocument wordDocument) {
        String content = null;
        ByteArrayOutputStream baos = null;
        try {
            WordToHtmlConverter wordToHtmlConverter = new ImageConverter(
                    DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument()
            );
            wordToHtmlConverter.processDocument(wordDocument);
            Document htmlDocument = wordToHtmlConverter.getDocument();
            DOMSource domSource = new DOMSource(htmlDocument);
            baos = new ByteArrayOutputStream();
            StreamResult streamResult = new StreamResult(baos);

            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer serializer = tf.newTransformer();
            serializer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
            serializer.setOutputProperty(OutputKeys.INDENT, "yes");
            serializer.setOutputProperty(OutputKeys.METHOD, "html");
            serializer.transform(domSource, streamResult);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    content = new String(baos.toByteArray(), "utf-8");
                    baos.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return content;
    }

    public static String docxToHtml(XWPFDocument document) {
        String value = "";
        final String picturesPath = "D:/ruoyi/test/image";
        try {
            XHTMLOptions options = XHTMLOptions.create();
            //图片转base64
            options.setImageManager(new Base64EmbedImgManager());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            XHTMLConverter.getInstance().convert(document, outputStream, options);
            value = new String(outputStream.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    static class ImageConverter extends WordToHtmlConverter {

        public ImageConverter(Document document) {
            super(document);
        }

        @Override
        protected void processImageWithoutPicturesManager(Element currentBlock, boolean inlined, Picture picture) {
            Element imgNode = currentBlock.getOwnerDocument().createElement("img");
            StringBuffer sb = new StringBuffer();
            sb.append(Base64.getMimeEncoder().encodeToString(picture.getRawContent()));
            sb.insert(0, "data:" + picture.getMimeType() + ";base64,");
            imgNode.setAttribute("src", sb.toString());
            currentBlock.appendChild(imgNode);
        }
    }


    public static void main(String[] args) {


        try {
            File file1 = new File("D:/ruoyi/test/ss.text");
            file1.createNewFile();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
