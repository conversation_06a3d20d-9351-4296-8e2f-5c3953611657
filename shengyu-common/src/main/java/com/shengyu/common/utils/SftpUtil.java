package com.shengyu.common.utils;

import com.jcraft.jsch.*;
import com.shengyu.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

/**
 * @Author: SS
 * @Date: 2024/08/06/9:08
 * @Description:
 */
@Slf4j
public class SftpUtil {

    private String server;
    private String port;
    private String loginName;
    private String loginPassword;

    public SftpUtil(String server, String port, String loginName, String loginPassword) {
        this.server = server;
        this.port = port;
        this.loginName = loginName;
        this.loginPassword = loginPassword;
    }

    /**
     * 连接登陆远程服务器
     *
     * @return
     */
    public ChannelSftp connect() {
        JSch jSch = new JSch();
        Session session = null;
        ChannelSftp sftp = null;
        try {
            session = jSch.getSession(loginName, server, Integer.parseInt(port));
            session.setPassword(loginPassword);
            session.setConfig(this.getSshConfig());
            session.connect();
            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
        } catch (Exception e) {
            log.error("SSH方式连接FTP服务器时有JSchException异常!", e);
            return null;
        }
        return sftp;
    }

    /**
     * 获取服务配置
     *
     * @return
     */
    private Properties getSshConfig() {
        Properties sshConfig = new Properties();
        sshConfig.put("StrictHostKeyChecking", "no");
        return sshConfig;
    }


    /**
     * 关闭连接
     *
     * @param sftp
     */
    public void disconnect(ChannelSftp sftp) {
        try {
            if (sftp != null) {
                if (sftp.getSession().isConnected()) {
                    sftp.getSession().disconnect();
                }
            }
        } catch (Exception e) {
            log.error("关闭与sftp服务器会话连接异常", e);
        }
    }

    /**
     * 下载远程sftp服务器文件
     *
     * @return
     */
    public void downFile(String remotePath, String fileName, ServletOutputStream outputStream, Boolean delete) {
        ChannelSftp sftp = null;
        try {
            sftp = connect();
            if (sftp == null) {
                return;
            }
            sftp.cd(remotePath);
            sftp.get(fileName, outputStream);
            if(delete){
                this.deleteFile(remotePath,fileName);
            }
        } catch (Exception e) {
            log.error("接收文件异常!", e);
        } finally {
            // 关闭连接
            disconnect(sftp);
        }
    }


    /**
     * 读取远程sftp服务器文件
     *
     * @return
     */
    public void readFile() {
        InputStream in = null;
        ArrayList<String> strings = new ArrayList<>();
        ChannelSftp sftp = null;
        try {
            sftp = connect();
            if (sftp == null) {
                return;
            }
            String remotePath = "/test1/";
            String remoteFilename = "测试1.txt";
            sftp.cd(remotePath);
            if (!listFiles(remotePath).contains(remoteFilename)) {
                log.error("no such file");
                return;
            }
            in = sftp.get(remoteFilename);
            if (in != null) {
                BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
                String str = null;
                while ((str = br.readLine()) != null) {
                    System.out.println(str);
                }
            } else {
                log.error("in为空，不能读取。");
            }
        } catch (Exception e) {
            log.error("接收文件时异常!", e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                // 关闭连接
                disconnect(sftp);
            } catch (Exception e) {
                log.error("关闭文件流时出现异常!", e);
            }
        }
    }


    /**
     * 写文件至远程sftp服务器
     *
     * @return
     */
    public void writeFile() {
        ChannelSftp sftp = null;
        ByteArrayInputStream input = null;
        try {
            sftp = connect();
            if (sftp == null) {
                return;
            }
            // 更改服务器目录
            String remotePath = "/test1/";
            sftp.cd(remotePath);
            // 发送文件
            String remoteFilename = "写文件.txt";
            String content = "测试内容";
            input = new ByteArrayInputStream(content.getBytes());
            sftp.put(input, remoteFilename);
        } catch (Exception e) {
            log.error("发送文件时有异常!", e);
        } finally {
            try {
                if (null != input) {
                    input.close();
                }
                // 关闭连接
                disconnect(sftp);
            } catch (Exception e) {
                log.error("关闭文件时出错!", e);
            }
        }
    }

    /**
     * 创建一个文件目录
     *
     * @param createPath 路径
     * @return
     */
    public boolean createDir(ChannelSftp sftp, String createPath) {
        boolean isSuccess = false;
        try {
            //sftp.cd("/files");
            if (isDirExist(sftp, createPath)) {
                sftp.cd(createPath);
                return true;
            }
            String[] pathArry = createPath.split("/");
            StringBuilder filePath = new StringBuilder("/");
            for (String path : pathArry) {
                if (path.equals("")) {
                    continue;
                }
                filePath.append(path + "/");
                if (isDirExist(sftp, filePath.toString())) {
                    sftp.cd(filePath.toString());
                } else {
                    // 建立目录
                    sftp.mkdir(filePath.toString());
                    // 进入并设置为当前目录
                    sftp.cd(filePath.toString());
                }
            }
            sftp.cd(createPath);
            isSuccess = true;
        } catch (SftpException e) {
            log.error("目录创建异常！", e);
        }
        return isSuccess;
    }

    /**
     * 判断目录是否存在
     *
     * @param directory 路径
     * @return
     */
    public boolean isDirExist(ChannelSftp sftp, String directory) {
        boolean isSuccess = false;
        try {
            SftpATTRS sftpATTRS = sftp.lstat(directory);
            isSuccess = true;
            return sftpATTRS.isDir();
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().equals("no such file")) {
                isSuccess = false;
            }
        }
        return isSuccess;
    }


    /**
     * 上传文件至sftp服务器
     *
     * @return
     */
    public void uploadFile(String remotePath, String fileName, InputStream ins) {
        ChannelSftp sftp = null;
        // 上传文件至服务器此目录
        try {
            sftp = connect();
            if (sftp == null) {
                return;
            }
            if (createDir(sftp, remotePath)) {
                //发送文件
                sftp.put(ins, fileName);
            }
        } catch (Exception e) {
            log.error("上传文件时异常!", e);
            throw new CustomException("上传文件时异常!");
        } finally {
            try {
                // 关闭连接
                disconnect(sftp);
            } catch (Exception e) {
                log.error("关闭文件时出错!", e);
            }
        }
    }


    /**
     * 遍历远程文件
     *
     * @param remotePath
     * @return
     * @throws Exception
     */
    public List<String> listFiles(String remotePath) {
        List<String> ftpFileNameList = new ArrayList<String>();
        ChannelSftp.LsEntry isEntity = null;
        String fileName = null;
        ChannelSftp sftp = null;
        try {
            sftp = connect();
            if (sftp == null) {
                return null;
            }
            Vector<ChannelSftp.LsEntry> sftpFile = sftp.ls(remotePath);
            Iterator<ChannelSftp.LsEntry> sftpFileNames = sftpFile.iterator();
            while (sftpFileNames.hasNext()) {
                isEntity = (ChannelSftp.LsEntry) sftpFileNames.next();
                fileName = isEntity.getFilename();
                ftpFileNameList.add(fileName);
            }
            return ftpFileNameList;
        } catch (Exception e) {
            log.error("遍历查询sftp服务器上文件异常", e);
            return null;
        } finally {
            disconnect(sftp);
        }

    }


    /**
     * 删除远程文件
     *
     * @return
     */
    public void deleteFile(String remotePath, String remoteFilename) {
        boolean success = false;
        ChannelSftp sftp = null;
        try {
            sftp = connect();
            if (sftp == null) {
                return;
            }
            // 更改服务器目录
            sftp.cd(remotePath);
            //判断文件是否存在
            if (listFiles(remotePath).contains(remoteFilename)) {
                // 删除文件
                sftp.rm(remoteFilename);
                log.info("删除远程文件" + remoteFilename + "成功!");
            }

        } catch (Exception e) {
            log.error("删除文件时有异常!", e);
        } finally {
            // 关闭连接
            disconnect(sftp);
        }
    }
}
