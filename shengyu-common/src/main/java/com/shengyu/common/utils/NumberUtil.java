package com.shengyu.common.utils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: SS
 * @Date: 2023/03/06/11:21
 * @Description:
 */
public class NumberUtil {

    public static Integer getDivideFloorVal(Integer v1, Integer v2) {
        if (v2 == null || v2 == 0) {
            return 0;
        }
        BigDecimal val = new BigDecimal(v1).divide(new BigDecimal(v2), 0, BigDecimal.ROUND_CEILING);
        return val.intValue();
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[+-]?[0-9]*\\.?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }
}
