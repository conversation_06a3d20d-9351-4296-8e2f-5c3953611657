package com.shengyu.common.utils.poi;

import com.shengyu.common.annotation.Excel;
import com.shengyu.common.annotation.Excel.ColumnType;
import com.shengyu.common.annotation.Excel.Type;
import com.shengyu.common.annotation.Excels;
import com.shengyu.common.config.CapitalConfig;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.DictUtils;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.excel.ExcelEntity;
import com.shengyu.common.utils.file.FileTypeUtils;
import com.shengyu.common.utils.file.FileUtils;
import com.shengyu.common.utils.file.ImageUtils;
import com.shengyu.common.utils.reflect.ReflectUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 */
public class ExcelUtil<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 最大高度
     */
    private short maxHeight;

    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public ExcelUtil() {

    }

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    public void init(List<T> list, String sheetName, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        createExcelField();
        createWorkbook();
    }

    public void init(List<T> list, String sheetName, Type type, boolean XSSF) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        createExcelField();
        createXSSFWorkbook();
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is) throws Exception {
        return importExcel(StringUtils.EMPTY, is);
    }

    public List<T> importExcel(InputStream is,int headRow,Map<String,String> dataMap) throws Exception {
        this.type = Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        Sheet sheet = wb.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }
        int rows = sheet.getPhysicalNumberOfRows();
        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(headRow);
            Row heard_ = sheet.getRow(headRow+1);
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell)) {
                    Object value = this.getCellValue(heard, i);
                    if(StringUtils.isNull(value) || StringUtils.isEmpty(value.toString())){
                        value = this.getCellValue(heard_, i);
                        if(StringUtils.isNull(value) || StringUtils.isEmpty(value.toString())){
                            cellMap.put(null, i);
                        }else{
                            cellMap.put(value.toString(), i);
                        }
                    }else{
                        cellMap.put(value.toString(), i);
                    }
                } else {
                    Object value = this.getCellValue(heard_, i);
                    if(StringUtils.isNull(value) || StringUtils.isEmpty(value.toString())){
                        cellMap.put(null, i);
                    }else{
                        cellMap.put(value.toString(), i);
                    }
                }
            }
            // 有数据时才处理 得到类的所有field.
            Field[] allFields = clazz.getDeclaredFields();
            // 定义一个map用于存放列的序号和field.
            Map<Integer, Field> fieldsMap = new HashMap<Integer, Field>();
            for (int col = 0; col < allFields.length; col++) {
                Field field = allFields[col];
                Excel attr = field.getAnnotation(Excel.class);
                if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                    // 设置类的私有字段属性可访问.
                    field.setAccessible(true);
                    Integer column = cellMap.get(attr.name());
                    if (column != null) {
                        fieldsMap.put(column, field);
                    }
                }
            }
            Map<String,String> specialMap = new HashMap<>();
            if(dataMap != null){
                dataMap.keySet().forEach(k -> {
                    String[] arr = dataMap.get(k).split(",");
                    Row row_ = sheet.getRow(Integer.parseInt(arr[0]));
                    Object value = this.getCellValue(row_, Integer.parseInt(arr[1]));
                    specialMap.put(k,StringUtils.isNull(value) ? "" : value.toString());
                });
            }
            for (int i = headRow+2; i < rows; i++) {
                // 从第2行开始取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                T entity = null;
                for (Map.Entry<Integer, Field> entry : fieldsMap.entrySet()) {
                    Object val = this.getCellValue(row, entry.getKey());

                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = fieldsMap.get(entry.getKey());
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0")) {
                            val = StringUtils.substringBefore(s, ".0");
                        } else {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat)) {
                                val = DateUtils.parseDateToStr(dateFormat, (Date) val);
                            } else {
                                val = Convert.toStr(val);
                            }
                        }
                    } else if ((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                        val = Convert.toInt(val);
                    } else if (Long.TYPE == fieldType || Long.class == fieldType) {
                        val = Convert.toLong(val);
                    } else if (Double.TYPE == fieldType || Double.class == fieldType) {
                        val = Convert.toDouble(val);
                    } else if (Float.TYPE == fieldType || Float.class == fieldType) {
                        val = Convert.toFloat(val);
                    } else if (BigDecimal.class == fieldType) {
                        val = Convert.toBigDecimal(val);
                    } else if (Date.class == fieldType) {
                        if (val instanceof String) {
                            val = DateUtils.parseDate(val);
                        } else if (val instanceof Double) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    } else if (Boolean.TYPE == fieldType || Boolean.class == fieldType) {
                        val = Convert.toBool(val, false);
                    }
                    if (StringUtils.isNotNull(fieldType)) {
                        Excel attr = field.getAnnotation(Excel.class);
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr())) {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        } else if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                        } else if (StringUtils.isNotEmpty(attr.dictType())) {
                            val = reverseDictByExp(Convert.toStr(val), attr.dictType(), attr.separator());
                        }
                        ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                    for(Map.Entry<String,String> e : specialMap.entrySet()){
                        ReflectUtils.invokeSetter(entity, e.getKey(), e.getValue());
                    }
                }
                list.add(entity);
            }
        }
        return list;
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param is        输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is) throws Exception {
        this.type = Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        Sheet sheet = null;
        if (StringUtils.isNotEmpty(sheetName)) {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = wb.getSheet(sheetName);
        } else {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = wb.getSheetAt(0);
        }

        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(0);
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell)) {
                    String value = this.getCellValue(heard, i).toString();
                    cellMap.put(value, i);
                } else {
                    cellMap.put(null, i);
                }
            }
            // 有数据时才处理 得到类的所有field.
            Field[] allFields = clazz.getDeclaredFields();
            // 定义一个map用于存放列的序号和field.
            Map<Integer, Field> fieldsMap = new HashMap<Integer, Field>();
            for (int col = 0; col < allFields.length; col++) {
                Field field = allFields[col];
                Excel attr = field.getAnnotation(Excel.class);
                if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                    // 设置类的私有字段属性可访问.
                    field.setAccessible(true);
                    Integer column = cellMap.get(attr.name());
                    if (column != null) {
                        fieldsMap.put(column, field);
                    }
                }
            }
            for (int i = 1; i < rows; i++) {
                // 从第2行开始取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                T entity = null;
                for (Map.Entry<Integer, Field> entry : fieldsMap.entrySet()) {
                    Object val = this.getCellValue(row, entry.getKey());

                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = fieldsMap.get(entry.getKey());
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0")) {
                            val = StringUtils.substringBefore(s, ".0");
                        } else {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat)) {
                                val = DateUtils.parseDateToStr(dateFormat, (Date) val);
                            } else {
                                val = Convert.toStr(val);
                            }
                        }
                    } else if ((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                        val = Convert.toInt(val);
                    } else if (Long.TYPE == fieldType || Long.class == fieldType) {
                        val = Convert.toLong(val);
                    } else if (Double.TYPE == fieldType || Double.class == fieldType) {
                        val = Convert.toDouble(val);
                    } else if (Float.TYPE == fieldType || Float.class == fieldType) {
                        val = Convert.toFloat(val);
                    } else if (BigDecimal.class == fieldType) {
                        val = Convert.toBigDecimal(val);
                    } else if (Date.class == fieldType) {
                        if (val instanceof String) {
                            val = DateUtils.parseDate(val);
                        } else if (val instanceof Double) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    } else if (Boolean.TYPE == fieldType || Boolean.class == fieldType) {
                        val = Convert.toBool(val, false);
                    }
                    if (StringUtils.isNotNull(fieldType)) {
                        Excel attr = field.getAnnotation(Excel.class);
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr())) {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        } else if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                        } else if (StringUtils.isNotEmpty(attr.dictType())) {
                            val = reverseDictByExp(Convert.toStr(val), attr.dictType(), attr.separator());
                        }
                        ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                }
                list.add(entity);
            }
        }
        return list;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void exportExcel(List<T> list, String sheetName) {
        this.init(list, sheetName, Type.EXPORT);
        exportExcel();
    }

    /***
     * @Description: 自定义表头
     * @Param: [list, sheetName, headMap]
     * @return: void
     * @Author: SS
     * @Date: 2023/8/31
     */
    public void exportExcel(List<T> list, String sheetName, Map<String, Object> headMap) {
        this.init(list, sheetName, Type.EXPORT);
        exportExcel(headMap, null);
    }

    /***
     * @Description: 自定义表头-特殊表头表尾
     * @Param: [list, sheetName, headMap]
     * @return: void
     * @Author: SS
     * @Date: 2023/8/31
     */
    public void exportExcel(List<T> list, String sheetName, Map<String, Object> headMap, Map<String, Object> otherMap) {
        this.init(list, sheetName, Type.EXPORT);
        exportExcel(headMap, otherMap);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param list      导入是错误数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void exportErrorExcel(List<T> list, String sheetName, Map<Integer, String> errorMap) {
        this.init(list, sheetName, Type.EXPORT, true);
        exportErrorExcel(errorMap);
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void importTemplateExcel(String sheetName) {
        this.init(null, sheetName, Type.IMPORT, true);
        exportExcel();
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public void exportExcel() {
        HttpServletResponse response = ServletUtils.getResponse();
        OutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);

                // 产生一行
                Row row = sheet.createRow(0);
                int column = 0;
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                if (Type.EXPORT.equals(type)) {
                    fillExcelData(index, 1);
                    addStatisticsRow();
                }
            }
            String filename = encodingFilename(sheetName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, filename);
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public void exportExcel(Map<String, Object> headMap, Map<String, Object> otherMap) {
        HttpServletResponse response = ServletUtils.getResponse();
        OutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                int startRowNum = 1;
                int titleNum = 0;
                if (headMap != null) {
                    startRowNum = (int) headMap.get("startRowNum");
                }
                // 产生一行
                Row[] rowArr = new Row[startRowNum];
                for (int i = 0; i < startRowNum; i++) {
                    rowArr[i] = sheet.createRow(i);
                }
                if (otherMap != null) {
                    titleNum = (int) otherMap.get("titleNum");
                    int maxCols = fields.size();
                    String firstTitle = otherMap.get("firstTitle").toString();
                    for (int i = 0; i < titleNum; i++) {
                        for (int j = 0; j < maxCols; j++) {
                            if (i == 0 && j == 0) {
                                this.createCell(firstTitle, rowArr[i], j);
                            } else {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "title"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "title").toString();
                                }
                                this.createCell(curVal, rowArr[i], j);
                            }
                        }
                    }
                    CellRangeAddress region = new CellRangeAddress(0, 0, 0, maxCols - 1);
                    sheet.addMergedRegion(region);
                }
                int column = 0;
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    for (int i = titleNum; i < startRowNum; i++) {
                        Object colVal = null;
                        if(StringUtils.isNotNull(headMap)){
                            colVal = headMap.get(i + "-" + column);
                            if(StringUtils.isNull(colVal)){
                                colVal = headMap.get("c" + column);
                            }
                        }
                        if (StringUtils.isNotNull(colVal) && StringUtils.isNotEmpty(colVal.toString())) {
                            this.createCell(colVal.toString(), rowArr[i], column);
                        } else {
                            this.createCell(excel, rowArr[i], column);
                        }
                    }
                    column++;
                }
                if (headMap != null && StringUtils.isNotNull(headMap.get("rules"))) {
                    String ruleStr = headMap.get("rules").toString();
                    String ruleArr[] = ruleStr.split(",");
                    for (String rule : ruleArr) {
                        String range[] = rule.split(":");
                        CellRangeAddress region = new CellRangeAddress(Integer.parseInt(range[0]),
                                Integer.parseInt(range[1]), Integer.parseInt(range[2]), Integer.parseInt(range[3]));
                        sheet.addMergedRegion(region);
                    }
                }
                if (headMap != null && StringUtils.isNotNull(headMap.get("normal"))) {
                    String normalRules = headMap.get("normal").toString();
                    String normalArr[] = normalRules.split(",");
                    for (String rule : normalArr) {
                        CellRangeAddress region = new CellRangeAddress(titleNum, startRowNum - 1, Integer.parseInt(rule),
                                Integer.parseInt(rule));
                        sheet.addMergedRegion(region);
                    }
                }
                if (Type.EXPORT.equals(type)) {
                    fillExcelData(index, startRowNum);
                    addStatisticsRow();
                }
                if (otherMap != null) {
                    int lastRow = startRowNum + list.size();
                    if (otherMap.get("lastTitle") != null) {
                        int lastRows = (Integer) otherMap.get("lastTitle");
                        for (int i = 1; i <= lastRows; i++) {
                            Row row = sheet.createRow(lastRow + i - 1);
                            for (int j = 0; j < fields.size(); j++) {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "lastTitle"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "lastTitle").toString();
                                }
                                this.createCell(curVal, row, j);
                            }
                        }
                    } else {
                        Row row = sheet.createRow(lastRow);
                        for (int j = 0; j < fields.size(); j++) {
                            String curVal = "";
                            if (StringUtils.isNotNull(otherMap.get(j + "_" + "lastTitle"))) {
                                curVal = otherMap.get(j + "_" + "lastTitle").toString();
                            }
                            this.createCell(curVal, row, j);
                        }
                    }
                }
            }
            String filename = encodingFilename(sheetName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, filename);
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public void exportSimpleExcel(List<String> headList, List<Object[]> dataList, Map<String, Object> headMap,
                                  Map<String, Object> otherMap, String sheetName) {
        HttpServletResponse response = ServletUtils.getResponse();
        OutputStream out = null;
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        try {
            // 取出一共有多少个sheet.
            this.sheetName = sheetName;
            this.type = Type.EXPORT;
            createWorkbook();
            double sheetNo = dataList.size() / sheetSize;
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                int startRowNum = 1;
                int titleNum = 0;
                if (headMap != null) {
                    startRowNum = (int) headMap.get("startRowNum");
                }
                // 产生一行
                Row[] rowArr = new Row[startRowNum];
                for (int i = 0; i < startRowNum; i++) {
                    rowArr[i] = sheet.createRow(i);
                }
                if (otherMap != null) {
                    titleNum = (int) otherMap.get("titleNum");
                    int maxCols = headList.size();
                    String firstTitle = otherMap.get("firstTitle").toString();
                    for (int i = 0; i < titleNum; i++) {
                        int lastMrInx = 0;
                        for (int j = 0; j < maxCols; j++) {
                            if (i == 0 && j == 0) {
                                this.createCell(firstTitle, rowArr[i], j);
                            } else {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "title"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "title").toString();
                                    if (j != 0 && lastMrInx != j - 1) {
                                        CellRangeAddress region = new CellRangeAddress(i, i, lastMrInx, j - 1);
                                        sheet.addMergedRegion(region);
                                    }
                                    lastMrInx = j;
                                }
                                this.createCell(curVal, rowArr[i], j,styles.get("data1"));
                            }
                        }
                        if (i != 0 && lastMrInx != maxCols - 1) {
                            CellRangeAddress region2 = new CellRangeAddress(i, i, lastMrInx, maxCols - 1);
                            sheet.addMergedRegion(region2);
                        }
                    }
                    CellRangeAddress region = new CellRangeAddress(0, 0, 0, maxCols - 1);
                    sheet.addMergedRegion(region);
                }
                int column = 0;
                // 写入各个字段的列头名称
                for (String headTitle : headList) {
                    for (int i = titleNum; i < startRowNum; i++) {
                        Object colVal = headMap.get(i + "-" + column);
                        if (StringUtils.isNotNull(colVal) && StringUtils.isNotEmpty(colVal.toString())) {
                            this.createCell(colVal.toString(), rowArr[i], column);
                        } else {
                            this.createCell(headTitle, rowArr[i], column);
                        }
                    }
                    column++;
                }
                if (headMap != null && StringUtils.isNotNull(headMap.get("rules"))) {
                    String ruleStr = headMap.get("rules").toString();
                    String ruleArr[] = ruleStr.split(",");
                    for (String rule : ruleArr) {
                        String range[] = rule.split(":");
                        CellRangeAddress region = new CellRangeAddress(Integer.parseInt(range[0]),
                                Integer.parseInt(range[1]), Integer.parseInt(range[2]), Integer.parseInt(range[3]));
                        sheet.addMergedRegion(region);
                    }
                }
                if (headMap != null && StringUtils.isNotNull(headMap.get("normal"))) {
                    String normalRules = headMap.get("normal").toString();
                    String normalArr[] = normalRules.split(",");
                    for (String rule : normalArr) {
                        CellRangeAddress region = new CellRangeAddress(titleNum, startRowNum - 1, Integer.parseInt(rule),
                                Integer.parseInt(rule));
                        sheet.addMergedRegion(region);
                    }
                }
                if (Type.EXPORT.equals(type)) {
                    int startNo = index * sheetSize;
                    int endNo = Math.min(startNo + sheetSize, dataList.size());
                    for (int i = startNo; i < endNo; i++) {
                        Row row = sheet.createRow(i + startRowNum - startNo);
                        // 得到导出对象.
                        Object[] objArr = dataList.get(i);
                        int column_ = 0;
                        for (int i1 = 0; i1 < headList.size(); i1++) {
                            Object celVal = objArr[i1];
                            this.createCell(celVal, row, column_++);
                        }
                    }
                }
                if (otherMap != null) {
                    int lastRow = startRowNum + dataList.size();
                    if (otherMap.get("lastTitle") != null) {
                        int lastRows = (Integer) otherMap.get("lastTitle");
                        for (int i = 1; i <= lastRows; i++) {
                            Row row = sheet.createRow(lastRow + i - 1);
                            int lastMrInx = 0;
                            for (int j = 0; j < headList.size(); j++) {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "lastTitle"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "lastTitle").toString();
                                    if (j != 0) {
                                        CellRangeAddress region = new CellRangeAddress(row.getRowNum(),
                                                row.getRowNum(), lastMrInx, j - 1);
                                        sheet.addMergedRegion(region);
                                        lastMrInx = j;
                                    }
                                }
                                this.createCell(curVal, row, j,styles.get("data1"));
                            }
                            CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(), lastMrInx,
                                    headList.size() - 1);
                            sheet.addMergedRegion(region);
                        }
                    } else {
                        if(StringUtils.isNotNull(otherMap.get("0_lastTitle")) || StringUtils.isNotNull(otherMap.get(
                                "1_lastTitle"))){
                            Row row = sheet.createRow(lastRow);
                            int lastMrInx = 0;
                            for (int j = 0; j < headList.size(); j++) {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(j + "_" + "lastTitle"))) {
                                    curVal = otherMap.get(j + "_" + "lastTitle").toString();
                                    if (j != 0) {
                                        CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(),
                                                lastMrInx, j - 1);
                                        sheet.addMergedRegion(region);
                                        lastMrInx = j;
                                    }
                                }
                                this.createCell(curVal, row, j,styles.get("data1"));
                            }
                            CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(), lastMrInx,
                                    headList.size() - 1);
                            sheet.addMergedRegion(region);
                        }
                    }
                }
            }
            String filename = encodingFilename(sheetName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, filename);
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public void exportSimpleExcel(List<String> headList, List<Object[]> dataList, Map<String, Object> formulas,
                                  String sheetName) {
        HttpServletResponse response = ServletUtils.getResponse();
        OutputStream out = null;
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        try {
            // 取出一共有多少个sheet.
            this.sheetName = sheetName;
            this.type = Type.EXPORT;
            createWorkbook();
            double sheetNo = dataList.size() / sheetSize;
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                int startRowNum = 1;
                int titleNum = 0;
                // 产生一行
                Row[] rowArr = new Row[startRowNum];
                for (int i = 0; i < startRowNum; i++) {
                    rowArr[i] = sheet.createRow(i);
                }
                int column = 0;
                // 写入各个字段的列头名称
                for (String headTitle : headList) {
                    for (int i = titleNum; i < startRowNum; i++) {
                        this.createCell(headTitle, rowArr[i], column);
                    }
                    column++;
                }
                if (Type.EXPORT.equals(type)) {
                    int startNo = index * sheetSize;
                    int endNo = Math.min(startNo + sheetSize, dataList.size());
                    for (int i = startNo; i < endNo; i++) {
                        Row row = sheet.createRow(i + startRowNum - startNo);
                        // 得到导出对象.
                        Object[] objArr = dataList.get(i);
                        int column_ = 0;
                        for (int i1 = 0; i1 < headList.size(); i1++) {
                            Object celVal = objArr[i1];
                            this.createCell(celVal, row, column_++);
                        }
                    }
                    if (StringUtils.isNotNull(formulas)) {
                        Row totalRow = sheet.createRow(endNo + startRowNum - startNo);
                        for (int i1 = 0; i1 < headList.size(); i1++) {
                            int startCell = (Integer) formulas.get("startCell");
                            Cell cell = this.createCell(i1 == 0 ? "合计" : "", totalRow, i1);
                            if (i1 >= startCell) {
                                char colChar = (char) (i1 + 65);
                                cell.setCellFormula("SUM(" + colChar + "2:" + colChar + totalRow.getRowNum() + ")");
                            }

                        }
                    }
                }
            }
            String filename = encodingFilename(sheetName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, filename);
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public void exportSimpleExcel(List<Object[]> dataList, String sheetName) {
        HttpServletResponse response = ServletUtils.getResponse();
        OutputStream out = null;
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        try {
            // 取出一共有多少个sheet.
            this.sheetName = sheetName;
            this.type = Type.EXPORT;
            createWorkbook();
            double sheetNo = dataList.size() / sheetSize;
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                int startRowNum = 0;
                if (Type.EXPORT.equals(type)) {
                    int startNo = index * sheetSize;
                    int endNo = Math.min(startNo + sheetSize, dataList.size());
                    for (int i = startNo; i < endNo; i++) {
                        Row row = sheet.createRow(i + startRowNum - startNo);
                        // 得到导出对象.
                        Object[] objArr = dataList.get(i);
                        int cellSize = 0;
                        if(objArr != null){
                            cellSize = objArr.length;
                        }
                        int column_ = 0;
                        for (int i1 = 0; i1 < cellSize; i1++) {
                            Object celVal = objArr[i1];
                            this.createCell(celVal, row, column_++);
                        }
                    }
                }
            }
            String filename = encodingFilename(sheetName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, filename);
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public ExcelEntity exportExcelEntity(List<String> headList, List<Object[]> dataList, Map<String, Object> headMap,
                                         Map<String, Object> otherMap, String sheetName) {
        ExcelEntity entity = new ExcelEntity();
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        try {
            // 取出一共有多少个sheet.
            this.sheetName = sheetName.substring(sheetName.lastIndexOf("/") + 1);
            this.type = Type.EXPORT;
            createWorkbook();
            double sheetNo = dataList.size() / sheetSize;
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);
                int startRowNum = 1;
                int titleNum = 0;
                if (headMap != null) {
                    startRowNum = (int) headMap.get("startRowNum");
                }
                // 产生一行
                Row[] rowArr = new Row[startRowNum];
                for (int i = 0; i < startRowNum; i++) {
                    rowArr[i] = sheet.createRow(i);
                }
                if (otherMap != null) {
                    titleNum = (int) otherMap.get("titleNum");
                    int maxCols = headList.size();
                    String firstTitle = otherMap.get("firstTitle").toString();
                    for (int i = 0; i < titleNum; i++) {
                        int lastMrInx = 0;
                        for (int j = 0; j < maxCols; j++) {
                            if (i == 0 && j == 0) {
                                this.createCell(firstTitle, rowArr[i], j);
                            } else {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "title"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "title").toString();
                                    if (j != 0) {
                                        CellRangeAddress region = new CellRangeAddress(i, i, lastMrInx, j - 1);
                                        sheet.addMergedRegion(region);
                                        lastMrInx = j;
                                    }
                                }
                                this.createCell(curVal, rowArr[i], j);
                            }
                        }
                        if (i != 0) {
                            CellRangeAddress region2 = new CellRangeAddress(i, i, lastMrInx, maxCols - 1);
                            sheet.addMergedRegion(region2);
                        }
                    }
                    CellRangeAddress region = new CellRangeAddress(0, 0, 0, maxCols - 1);
                    sheet.addMergedRegion(region);
                }
                int column = 0;
                // 写入各个字段的列头名称
                for (String headTitle : headList) {
                    for (int i = titleNum; i < startRowNum; i++) {
                        Object colVal = headMap.get(i + "-" + column);
                        if (StringUtils.isNotNull(colVal) && StringUtils.isNotEmpty(colVal.toString())) {
                            this.createCell(colVal.toString(), rowArr[i], column);
                        } else {
                            this.createCell(headTitle, rowArr[i], column);
                        }
                    }
                    column++;
                }
                if (Type.EXPORT.equals(type)) {
                    int startNo = index * sheetSize;
                    int endNo = Math.min(startNo + sheetSize, dataList.size());
                    for (int i = startNo; i < endNo; i++) {
                        Row row = sheet.createRow(i + startRowNum - startNo);
                        // 得到导出对象.
                        Object[] objArr = dataList.get(i);
                        int column_ = 0;
                        for (int i1 = 0; i1 < headList.size(); i1++) {
                            Object celVal = objArr[i1];
                            this.createCell(celVal, row, column_++);
                        }
                    }
                }
                if (otherMap != null) {
                    int lastRow = startRowNum + dataList.size();
                    if (otherMap.get("lastTitle") != null) {
                        int lastRows = (Integer) otherMap.get("lastTitle");

                        for (int i = 1; i <= lastRows; i++) {
                            Row row = sheet.createRow(lastRow + i - 1);
                            int lastMrInx = 0;
                            for (int j = 0; j < headList.size(); j++) {
                                String curVal = "";
                                if (StringUtils.isNotNull(otherMap.get(i + "_" + j + "_" + "lastTitle"))) {
                                    curVal = otherMap.get(i + "_" + j + "_" + "lastTitle").toString();
                                    if (j != 0) {
                                        CellRangeAddress region = new CellRangeAddress(row.getRowNum(),
                                                row.getRowNum(), lastMrInx, j - 1);
                                        sheet.addMergedRegion(region);
                                        lastMrInx = j;
                                    }
                                }
                                this.createCell(curVal, row, j);
                            }
                            CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(), lastMrInx,
                                    headList.size() - 1);
                            sheet.addMergedRegion(region);
                        }
                    } else {
                        Row row = sheet.createRow(lastRow);
                        int lastMrInx = 0;
                        for (int j = 0; j < headList.size(); j++) {
                            String curVal = "";
                            if (StringUtils.isNotNull(otherMap.get(j + "_" + "lastTitle"))) {
                                curVal = otherMap.get(j + "_" + "lastTitle").toString();
                                if (j != 0) {
                                    CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(),
                                            lastMrInx, j - 1);
                                    sheet.addMergedRegion(region);
                                    lastMrInx = j;
                                }
                            }
                            this.createCell(curVal, row, j);
                        }
                        CellRangeAddress region = new CellRangeAddress(row.getRowNum(), row.getRowNum(), lastMrInx,
                                headList.size() - 1);
                        sheet.addMergedRegion(region);
                    }
                }
            }
            wb.write(out);
            entity.setData(out.toByteArray());
            entity.setName(sheetName);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            try {
                out.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
        return entity;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param errorMap 实体类与错误信息对应关系
     * @return 结果
     */
    public void exportErrorExcel(Map<Integer, String> errorMap) {
        OutputStream out = null;
        try {
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                createSheet(sheetNo, index);

                // 产生一行
                Row row = sheet.createRow(0);
                int column = 0;
                // 写入各个字段的列头名称
                for (Object[] os : fields) {
                    Excel excel = (Excel) os[1];
                    this.createCell(excel, row, column++);
                }
                //加入错误信息字段
                createErrorCell(row, column++);
                if (Type.EXPORT.equals(type)) {
                    fillExcelData(index, errorMap);
                }
            }
            String filename = encodingFilename(sheetName);
            out = new FileOutputStream(getAbsoluteFile(filename));
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new CustomException("导出Excel失败，请联系网站管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     */
    public void fillExcelData(int index, int startRowNum) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        for (int i = startNo; i < endNo; i++) {
            Row row = sheet.createRow(i + startRowNum - startNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                // 设置实体类私有属性可访问
                field.setAccessible(true);
                this.addCell(excel, row, vo, field, column++);
            }
        }
    }

    /**
     * 填充excel数据
     *
     * @param index    sheet页序号
     * @param errorMap 错误信息
     */
    public void fillExcelData(int index, Map<Integer, String> errorMap) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        for (int i = startNo; i < endNo; i++) {
            int rowNum = i + 1 - startNo;
            Row row = sheet.createRow(rowNum);
            // 得到导出对象.
            T vo = (T) list.get(i);
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                // 设置实体类私有属性可访问
                field.setAccessible(true);
                this.addCell(excel, row, vo, field, column++);
            }
            //再加入错误信息列
            this.addErrorCell(row, vo, column++, errorMap);
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setWrapText(true); // 设置为自动换行
        Font errorDataFont = wb.createFont();
        errorDataFont.setFontName("Arial");
        errorDataFont.setFontHeightInPoints((short) 10);
        errorDataFont.setColor(IndexedColors.RED.getIndex());
        errorDataFont.setItalic(true);
        style.setFont(errorDataFont);
        styles.put("errorData", style);


        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);


        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("errorData"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font headerErrorFont = wb.createFont();
        headerErrorFont.setFontName("Arial");
        headerErrorFont.setFontHeightInPoints((short) 10);
        headerErrorFont.setBold(true);
        headerErrorFont.setColor(IndexedColors.RED.getIndex());
        headerErrorFont.setItalic(true);
        style.setFont(headerErrorFont);
        styles.put("errorHeader", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.LEFT);
        styles.put("data1", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        styles.put("data2", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.RIGHT);
        styles.put("data3", style);

        return styles;
    }

    /**
     * 创建单元格
     */
    public Cell createCell(Excel attr, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        //判断是否有*号表示必填，必填用红色表示
        if (attr.name().contains("*") && wb instanceof XSSFWorkbook) {
            Font headFont = wb.createFont();
            headFont.setColor(IndexedColors.RED1.getIndex());
            headFont.setFontName("Arial");
            headFont.setBold(false); //是否加粗
            headFont.setFontHeightInPoints((short) 10);
            XSSFRichTextString richString = new XSSFRichTextString(attr.name());
            richString.applyFont(richString.length() - 1, richString.length(), headFont);
            cell.setCellValue(richString);
        } else {
            String value = attr.name().replaceAll("\\*", "");
            cell.setCellValue(value.trim());
        }
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    public Cell createCell(String name, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(name);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    public Cell createCell(Object val, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        String celString = val != null ? val.toString() : "";
        cell.setCellValue(celString);
        cell.setCellStyle(styles.get("data"));
        return cell;
    }

    public Cell createCell(String name, Row row, int column,CellStyle style) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(name);
        cell.setCellStyle(style);
        return cell;
    }

    /**
     * 创建单元格
     */
    public Cell createErrorCell(Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue("错误信息");
        sheet.setColumnWidth(column, 10000);
        cell.setCellStyle(styles.get("errorHeader"));
        return cell;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        if (ColumnType.STRING == attr.cellType()) {
            cell.setCellValue(StringUtils.isNull(value) ? attr.defaultValue() : value + attr.suffix());
        } else if (ColumnType.NUMERIC == attr.cellType()) {
            if (StringUtils.isNotNull(value)) {
                cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
            }
        } else if (ColumnType.IMAGE == attr.cellType()) {
            ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1),
                    cell.getRow().getRowNum() + 1);
            String imagePath = Convert.toStr(value);
            if (StringUtils.isNotEmpty(imagePath)) {
                byte[] data = ImageUtils.getImage(imagePath);
                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
            }
        }
    }

    /**
     * 获取画布
     */
    public static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if (sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    public int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if ("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        // 如果设置了提示信息则鼠标放上去提示.
        if (StringUtils.isNotEmpty(attr.prompt())) {
            // 这里默认设了2-101列提示.
            setXSSFPrompt(sheet, "", attr.prompt(), 1, 100, column, column);
        }
        // 如果设置了combo属性则本列只能选择不能输入
        if (attr.combo().length > 0) {
            // 这里默认设了2-101列只能选择不能输入.
            setXSSFValidation(sheet, attr.combo(), 1, 100, column, column);
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight(maxHeight);
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                int align = attr.align().value();
                cell.setCellStyle(styles.get("data" + (align >= 1 && align <= 3 ? align : "")));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                String dictType = attr.dictType();
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(DateUtils.parseDateToStr(dateFormat, (Date) value));
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                } else if (StringUtils.isNotEmpty(dictType) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertDictByExp(Convert.toStr(value), dictType, separator));
                } else if (value instanceof BigDecimal && -1 != attr.scale()) {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).toString());
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
                addStatisticsData(column, Convert.toStr(value), attr);
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    /**
     * 添加单元格
     */
    public Cell addErrorCell(Row row, T vo, int column, Map<Integer, String> errorMap) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight(maxHeight);
            // 创建cell
            cell = row.createCell(column);
            cell.setCellStyle(styles.get("errorData"));
            // 用于读取对象中的属性
            String value = errorMap.get(row.getRowNum());
            cell.setCellValue(value);
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    /**
     * 设置 POI XSSFSheet 单元格提示
     *
     * @param sheet         表单
     * @param promptTitle   提示标题
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFPrompt(Sheet sheet, String promptTitle, String promptContent, int firstRow, int endRow,
                              int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        dataValidation.createPromptBox(promptTitle, promptContent);
        dataValidation.setShowPromptBox(true);
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public void setXSSFValidation(Sheet sheet, String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String convertByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(separator, propertyValue)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[0].equals(value)) {
                        propertyString.append(itemArray[1] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String reverseByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(separator, propertyValue)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[1].equals(value)) {
                        propertyString.append(itemArray[0] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 解析字典值
     *
     * @param dictValue 字典值
     * @param dictType  字典类型
     * @param separator 分隔符
     * @return 字典标签
     */
    public static String convertDictByExp(String dictValue, String dictType, String separator) {
        return DictUtils.getDictLabel(dictType, dictValue, separator);
    }

    /**
     * 反向解析值字典值
     *
     * @param dictLabel 字典标签
     * @param dictType  字典类型
     * @param separator 分隔符
     * @return 字典值
     */
    public static String reverseDictByExp(String dictLabel, String dictType, String separator) {
        return DictUtils.getDictValue(dictType, dictLabel, separator);
    }

    /**
     * 合计统计信息
     */
    private void addStatisticsData(Integer index, String text, Excel entity) {
        if (entity != null && entity.isStatistics()) {
            Double temp = 0D;
            if (!statistics.containsKey(index)) {
                statistics.put(index, temp);
            }
            try {
                temp = Double.valueOf(text);
            } catch (NumberFormatException e) {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    /**
     * 创建统计行
     */
    public void addStatisticsRow() {
        if (statistics.size() > 0) {
            Cell cell = null;
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }

    /**
     * 编码文件名
     */
    public String encodingFilename(String filename) {
        filename = filename + ".xlsx";
        return filename;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        String downloadPath = CapitalConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if (target.indexOf(".") > -1) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            // 单注解
            if (field.isAnnotationPresent(Excel.class)) {
                putToField(field, field.getAnnotation(Excel.class));
            }

            // 多注解
            if (field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for (Excel excel : excels) {
                    putToField(field, excel);
                }
            }
        }
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight() {
        double maxHeight = 0;
        for (Object[] os : this.fields) {
            Excel excel = (Excel) os[1];
            maxHeight = maxHeight > excel.height() ? maxHeight : excel.height();
        }
        return (short) (maxHeight * 20);
    }

    /**
     * 放到字段集合中
     */
    private void putToField(Field field, Excel attr) {
        if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
            this.fields.add(new Object[]{field, attr});
        }
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
    }

    /**
     * 创建一个工作簿
     */
    public void createXSSFWorkbook() {
        this.wb = new XSSFWorkbook();
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(double sheetNo, int index) {
        this.sheet = wb.createSheet();
        this.styles = createStyles(wb);
        // 设置工作表的名称.
        if (sheetNo == 0) {
            wb.setSheetName(index, sheetName);
        } else {
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        if (row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (StringUtils.isNotNull(cell)) {
                if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if (DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    public static List<List<String>> read(InputStream is, String fileName) {
        List<List<String>> res = new ArrayList<>();
        try {
            Workbook workbook;
            if (fileName.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(is);
            } else {
                workbook = new XSSFWorkbook(is);
            }
            int sheetSize = workbook.getNumberOfSheets();
            for (int i = 0; i < sheetSize; i++) {
                Sheet sheetAt = workbook.getSheetAt(i);
                int rows = sheetAt.getLastRowNum();// 指的行数，一共有多少行+
                if (rows == 0) {
                    continue;
                }
                for (Row row : sheetAt) {
                    if (row.getRowNum() > 5 && row.getCell(0) == null && row.getCell(1) == null && row.getCell(2) == null) {
                        break;//如果数据行 首列并无数据 则直接终止
                    }
                    List<String> rowData = new ArrayList<>();
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        //单元格处理
                        Cell cell = row.getCell(j);
                        String cellStr = "";
                        if (cell == null) {// 单元格为空设置cellStr为空串
                            cellStr = "";
                        } else if (cell.getCellType() == CellType.BOOLEAN) {
                            // 对布尔值的处理
                            cellStr = String.valueOf(cell.getBooleanCellValue());
                        } else if (cell.getCellType() == CellType.NUMERIC) {
                            // 对数字值的处理
                            DecimalFormat df = new DecimalFormat("0.00");
                            cellStr = df.format(cell.getNumericCellValue());
                        } else {
                            // 其余按照字符串处理
                            cellStr = cell.getStringCellValue();
                        }
                        rowData.add(cellStr);
                    }
                    res.add(rowData);
                }
            }
        } catch (Exception e) {
            throw new CustomException("解析excel数据异常");
        }
        return res;
    }

    public static List<List<String>> readNormal(InputStream is, String fileName) {
        List<List<String>> res = new ArrayList<>();
        try {
            Workbook workbook;
            if (fileName.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(is);
            } else {
                workbook = new XSSFWorkbook(is);
            }
            int sheetSize = workbook.getNumberOfSheets();
            for (int i = 0; i < sheetSize; i++) {
                Sheet sheetAt = workbook.getSheetAt(i);
                int rows = sheetAt.getLastRowNum();// 指的行数，一共有多少行+
                if (rows == 0) {
                    continue;
                }
                for (Row row : sheetAt) {
                    if (row.getRowNum() > 5 && row.getCell(0) == null && row.getCell(1) == null && row.getCell(2) == null) {
                        break;//如果数据行 首列并无数据 则直接终止
                    }
                    List<String> rowData = new ArrayList<>();
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        //单元格处理
                        Cell cell = row.getCell(j);
                        String cellStr = "";
                        if (cell == null) {// 单元格为空设置cellStr为空串
                            cellStr = "";
                        } else if (cell.getCellType() == CellType.BOOLEAN) {
                            // 对布尔值的处理
                            cellStr = String.valueOf(cell.getBooleanCellValue());
                        } else if (cell.getCellType() == CellType.NUMERIC) {
                            DecimalFormat df = new DecimalFormat("0.00");
                            if(j == 0){
                                df = new DecimalFormat("0");
                            }
                            cellStr = df.format(cell.getNumericCellValue());
                        } else {
                            // 其余按照字符串处理
                            cellStr = cell.getStringCellValue();
                        }
                        rowData.add(cellStr);
                    }
                    res.add(rowData);
                }
            }
        } catch (Exception e) {
            throw new CustomException("解析excel数据异常");
        }
        return res;
    }
}
