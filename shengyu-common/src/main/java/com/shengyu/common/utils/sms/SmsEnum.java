package com.shengyu.common.utils.sms;

import com.shengyu.common.utils.StringUtils;

/**
 * description: SmsEnum <br>
 * date: 2021/7/27 10:10 <br>
 *
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public enum SmsEnum {
    /**
     * 此处配置短信的模板编码，签名和参数名的key
     */
    LOGIN_TEMPLATE_CODE("SMS_238291099", "河南晟宇", "code");


    /**
     * 短信模板编码
     */
    private String templateCode;
    /**
     * 签名
     */
    private String signName;
    /**
     * 短信模板必需的数据名称，多个key以逗号分隔，此处配置作为校验
     */
    private String keys;

    private SmsEnum(String templateCode, String signName, String keys) {
        this.templateCode = templateCode;
        this.signName = signName;
        this.keys = keys;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getKeys() {
        return keys;
    }

    public void setKeys(String keys) {
        this.keys = keys;
    }

    public static SmsEnum toEnum(String templateCode) {
        if (StringUtils.isEmpty(templateCode)) {
            return null;
        }
        for (SmsEnum item : SmsEnum.values()) {
            if (item.getTemplateCode().equals(templateCode)) {
                return item;
            }
        }
        return null;
    }

}
