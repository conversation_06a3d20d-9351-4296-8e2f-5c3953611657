package com.shengyu.common.utils.bean;

import com.shengyu.common.core.domain.model.LoginUser;

import java.util.List;
import java.util.Map;

/**
 * description: 用于 <br>
 * date: 2021/7/29 17:07 <br>
 *
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public class ImportDataErrorInfo {

    /**
     * 错误数据存到缓存中的key
     */
    private String redisKey;
    /**
     * 错误数据列表
     */
    private List errorList;
    /**
     * 错误提示与excel的行号对应关系
     */
    private Map<Integer, String> errorMap;
    /**
     * 错误数据条数
     */
    private int failureNum;
    /**
     * 更新数据条数
     */
    private int updateNum;
    /**
     * 插入数据条数
     */
    private int insertNum;
    /**
     * 数据总条数
     */
    private int totalNum;
    /**
     * 当前用户
     */
    private LoginUser LoginUser;


    public int getInsertNum() {
        return insertNum;
    }

    public void setInsertNum(int insertNum) {
        this.insertNum = insertNum;
    }

    public int getUpdateNum() {
        return updateNum;
    }

    public void setUpdateNum(int updateNum) {
        this.updateNum = updateNum;
    }

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public List getErrorList() {
        return errorList;
    }

    public void setErrorList(List errorList) {
        this.errorList = errorList;
    }

    public Map<Integer, String> getErrorMap() {
        return errorMap;
    }

    public void setErrorMap(Map<Integer, String> errorMap) {
        this.errorMap = errorMap;
    }

    public int getFailureNum() {
        return failureNum;
    }

    public void setFailureNum(int failureNum) {
        this.failureNum = failureNum;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public com.shengyu.common.core.domain.model.LoginUser getLoginUser() {
        return LoginUser;
    }

    public void setLoginUser(com.shengyu.common.core.domain.model.LoginUser loginUser) {
        LoginUser = loginUser;
    }
}
