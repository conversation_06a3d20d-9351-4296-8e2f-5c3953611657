package com.shengyu.common.core.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: SS
 * @Date: 2023/06/09/17:08
 * @Description:
 */
@Data
public class SocketMsg implements Serializable {
    private static final long serialVersionUID = 1L;
    private String type;

    private String userId;

    private Map<String, Object> data;

    public SocketMsg(String type, String userId, Map<String, Object> data) {
        this.type = type;
        this.userId = userId;
        this.data = data;
    }
}
