package com.shengyu.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shengyu.common.annotation.Excel;
import com.shengyu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 组织架构表 sys_dept
 *
 * <AUTHOR>
 */
@ApiModel("组织架构对象")
public class SysDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 组织架构ID
     */
    private Long deptId;

    /**
     * 父组织架构ID
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    @Excel(name = "排序")
    private String orderNum;

    /**
     * 组织架构名称
     */
    @Excel(name = "组织架构名称")
    private String deptName;

    /**
     * 组织架构编码
     */
    @Excel(name = "组织架构编码")
    private String deptCode;

    @Excel(name = "社会统一信用代码")
    @ApiModelProperty("社会统一信用代码")
    private String creditCode;

    /**
     * 显示顺序
     */


    /**
     * 负责人
     */
    @Excel(name = "法人")
    private String leader;

    /**
     * 联系电话
     */

    @Excel(name = "电话")
    private String phone;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 组织架构状态:1正常,0停用
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Excel(name = "上级组织架构编码")
    @ApiModelProperty("上级组织架构编码")
    private String parentCode;

    @ApiModelProperty("层级-3县级，4乡级，5村")
    private Integer level;
    @Excel(name = "组织类型", readConverterExp = "1=村委组织,2=股份经济合作社,3=新型经营主体,4=其他")
    @ApiModelProperty("1村委组织、2股份经济合作社、3新型经营主体、4其他")
    private String type;
    @Excel(name = "是否业务组织", readConverterExp = "1=是,0=否")
    @ApiModelProperty("是否业务组织")
    private String isBusiness;
    @Excel(name = "组织属性", readConverterExp = "1=乡镇,2=村,3=组")
    @ApiModelProperty("组织属性")
    private String nature;

    /**
     * 父组织架构名称
     */
    @ApiModelProperty("上级组织架构名称")
    private String parentName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("行政区划id")
    private Long areaId;

    @TableField(exist = false)
    private String merName;


    private String mapCapitalId;
    private String tenantId;
    @Excel(name = "是否记账", readConverterExp = "1=是,2否")
    @ApiModelProperty("组级账套是否记账-默认否")
    private String isVch;

    @TableField(exist = false)
    @ApiModelProperty("截止层级")
    private Integer toLevel;

    @TableField(exist = false)
    @ApiModelProperty("乡镇名称")
    private String townName;

    @TableField(exist = false)
    @ApiModelProperty("开始层级")
    private Integer fromLevel;

    @TableField(exist = false)
    private Long codeId;
    @TableField(exist = false)
    private String registerName;
    /**
     * 子组织架构
     */
    private List<SysDept> children = new ArrayList<SysDept>();

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    @NotBlank(message = "组织架构名称不能为空")
    @Size(min = 0, max = 30, message = "组织架构名称长度不能超过30个字符")
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    @Size(min = 0, max = 11, message = "联系电话长度不能超过11个字符")
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<SysDept> getChildren() {
        return children;
    }

    public void setChildren(List<SysDept> children) {
        this.children = children;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMapCapitalId() {
        return mapCapitalId;
    }

    public void setMapCapitalId(String mapCapitalId) {
        this.mapCapitalId = mapCapitalId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIsBusiness() {
        return isBusiness;
    }

    public void setIsBusiness(String isBusiness) {
        this.isBusiness = isBusiness;
    }

    public Integer getToLevel() {
        return toLevel;
    }

    public void setToLevel(Integer toLevel) {
        this.toLevel = toLevel;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getTownName() {
        return townName;
    }

    public void setTownName(String townName) {
        this.townName = townName;
    }

    public Integer getFromLevel() {
        return fromLevel;
    }

    public void setFromLevel(Integer fromLevel) {
        this.fromLevel = fromLevel;
    }

    public Long getCodeId() {
        return codeId;
    }

    public void setCodeId(Long codeId) {
        this.codeId = codeId;
    }

    public String getIsVch() {
        return isVch;
    }

    public void setIsVch(String isVch) {
        this.isVch = isVch;
    }

    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("deptId", getDeptId())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("deptName", getDeptName())
                .append("orderNum", getOrderNum())
                .append("leader", getLeader())
                .append("phone", getPhone())
                .append("email", getEmail())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
