package com.shengyu.common.core.domain;

import com.shengyu.common.core.domain.entity.HelpDocument;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysMenu;
import com.shengyu.common.utils.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    private List<TreeSelect> children;
    /**
     * 标识，用于组合树区分各层内容
     */
    private String flag;

    private int level;

    public TreeSelect() {

    }

    public TreeSelect(SysDept dept) {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.level = dept.getLevel();
        if (StringUtils.isNotEmpty(dept.getChildren())) {
            this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        }

    }

    public TreeSelect(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        if (StringUtils.isNotEmpty(menu.getChildren())) {
            this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        }
    }

    public TreeSelect(HelpDocument document) {
        this.id = document.getId();
        this.label = document.getTitle();
        if (StringUtils.isNotEmpty(document.getChildren())) {
            this.children = document.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        }
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<TreeSelect> getChildren() {
        return children;
    }

    public void setChildren(List<TreeSelect> children) {
        this.children = children;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
}
