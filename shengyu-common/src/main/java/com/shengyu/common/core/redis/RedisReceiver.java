package com.shengyu.common.core.redis;


import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.utils.spring.SpringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class RedisReceiver {

    /**
     * 接受消息并调用业务逻辑处理器
     *
     * @param params
     */
    public void onMessage(SocketMsg params) {
        RedisListener messageListener = SpringUtils.getBean("socketHandler", RedisListener.class);
        messageListener.onMessage(params);
    }

}
