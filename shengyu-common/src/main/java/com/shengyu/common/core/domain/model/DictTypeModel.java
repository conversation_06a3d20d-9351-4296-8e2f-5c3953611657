package com.shengyu.common.core.domain.model;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Author: SS
 * @Date: 2023/10/18/15:06
 * @Description:
 */
@Data
@ApiModel(value = "DictTypeModel-自定义dictModel")
public class DictTypeModel {

    private String id;
    private String parentId;
    private String name;
    private boolean isLeaf;
    private List<DictTypeModel> children;
}
