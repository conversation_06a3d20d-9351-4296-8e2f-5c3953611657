package com.shengyu.common.core.domain.dto;

import com.shengyu.common.core.domain.entity.SysUser;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
public class SysUserDto extends SysUser {
    private Integer groupRole;

    private Integer isConfirm;

    private String confirmTime;

    public Integer getGroupRole() {
        return groupRole;
    }

    public void setGroupRole(Integer groupRole) {
        this.groupRole = groupRole;
    }

    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }

    public String getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }
}
