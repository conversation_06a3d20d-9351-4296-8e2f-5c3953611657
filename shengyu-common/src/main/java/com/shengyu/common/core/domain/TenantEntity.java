package com.shengyu.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;

public class TenantEntity extends BaseEntity {

    @ApiModelProperty("租户ID")
    @TableField(value = "tenant_id")
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            tenantId = ConfigUtils.getTenantId();
        }
        this.tenantId = tenantId;
    }
}
