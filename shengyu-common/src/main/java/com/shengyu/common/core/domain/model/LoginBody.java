package com.shengyu.common.core.domain.model;

import io.swagger.annotations.ApiModelProperty;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
public class LoginBody {
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty("用户密码")
    private String password;

    @ApiModelProperty("电话")
    private String phone;

    /**
     * 验证码
     */
    @ApiModelProperty("code编码")
    private String code;
    /**
     * 登录类型，0-账号密码登录，1-手机号验证码登录
     */
    @ApiModelProperty("登录类型，0-账号密码登录，1-手机号验证码登录")
    private int loginType;

    /**
     * 唯一标识
     */
    private String uuid = "";

    @ApiModelProperty("登录平台1:手机")
    private int platform = 0;

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }


    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }
}
