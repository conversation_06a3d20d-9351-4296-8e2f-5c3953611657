package com.shengyu.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.common.annotation.Excel;
import com.shengyu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * 帮助文档对象 sys_help_document
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@ApiModel(value = "帮助文档对象")
@TableName(value = "sys_help_document")
public class HelpDocument extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long parentId;

    /**
     * 操作说明简介
     */
    @Excel(name = "操作说明简介")
    @ApiModelProperty("操作说明简介")
    private String title;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 操作内容
     */
    @Excel(name = "操作内容")
    @ApiModelProperty("操作内容")
    private String contents;

    @TableField(exist = false)
    private List<HelpDocument> children = new ArrayList<HelpDocument>();

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public String getContents() {
        return contents;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<HelpDocument> getChildren() {
        return children;
    }

    public void setChildren(List<HelpDocument> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("orderNum", getOrderNum())
                .append("contents", getContents())
                .toString();
    }
}
