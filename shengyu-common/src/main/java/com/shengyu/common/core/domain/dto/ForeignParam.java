package com.shengyu.common.core.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: SS
 * @Date: 2023/03/09/13:59
 * @Description:
 */
@ApiModel(description = "对外统一参数")
@Data
public class ForeignParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("签名")
    private String sign;
    @ApiModelProperty("时间戳")
    private Long timeStamp;
    @ApiModelProperty("数据")
    private String data;
    @ApiModelProperty("系统类型")
    private String type="1";

    public ForeignParam() {

    }

    public ForeignParam(String sign, String data, Long timeStamp) {
        this.sign = sign;
        this.timeStamp = timeStamp;
        this.data = data;
    }
}
