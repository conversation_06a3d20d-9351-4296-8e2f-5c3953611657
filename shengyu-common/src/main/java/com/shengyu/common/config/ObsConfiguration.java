package com.shengyu.common.config;

import com.obs.services.ObsClient;
import com.shengyu.common.utils.file.ObsUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: SS
 * @Date: 2022/06/24/9:52
 * @Description: 华为云配置
 */
@Configuration
public class ObsConfiguration {

    private final String accessKeyId = "ELRSJKJNTAN2WWSENDBY";
    private final String accessKeySecret = "wAe40DA3Zibk9g9SsMLkJzxYTp7dua5tj5GzLxoN";
    private final String bucketName = "farmaffairs";
    private final String server = "https://obs.cn-north-9.myhuaweicloud.com";

    @Bean
    public void initObsConfiguration() {
        ObsClient obsClient = new ObsClient(accessKeyId, accessKeySecret, server);
        ObsUtil.setObsClient(obsClient);
        ObsUtil.setBucketName(bucketName);
    }
}
