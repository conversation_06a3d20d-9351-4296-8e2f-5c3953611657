package com.shengyu.common.config;

import com.xxl.sso.core.util.JedisUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * redisson配置
 *
 * <AUTHOR>
 */
@Configuration
public class JedisConfig {
    @Value("${xxl.sso.redis.address}")
    private String xxlSsoRedisAddress;

    @PostConstruct
    public void initJedis() {
        JedisUtil.init(xxlSsoRedisAddress);
    }
}