package com.shengyu.common.config;

import com.shengyu.common.constant.WebsocketConst;
import com.shengyu.common.core.domain.SocketMsg;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * redis客户端
 */
@Component
public class RedisClient {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 发送消息
     *
     * @param params
     */
    public void sendMessage(SocketMsg params) {
        redisTemplate.convertAndSend(WebsocketConst.REDIS_TOPIC_NAME, params);
    }
}
