<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysNoticeStatusMapper">

    <resultMap type="SysNoticeStatus" id="SysNoticeStatusResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectSysNoticeStatusVo">
        select id, notice_id, user_id, create_time, del_flag from sys_notice_status
    </sql>

    <select id="selectSysNoticeStatusList" parameterType="SysNoticeStatus" resultMap="SysNoticeStatusResult">
        <include refid="selectSysNoticeStatusVo"/>
        where del_flag='0'
        <if test="noticeId != null ">
            and notice_id = #{noticeId}
        </if>
        <if test="userId != null ">
            and user_id = #{userId}
        </if>
    </select>

    <select id="selectSysNoticeStatusById" parameterType="Long"
            resultMap="SysNoticeStatusResult">
        <include refid="selectSysNoticeStatusVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysNoticeStatus" parameterType="SysNoticeStatus" useGeneratedKeys="true" keyProperty="id">
        insert into sys_notice_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="noticeId != null">notice_id,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="noticeId != null">#{noticeId},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateSysNoticeStatus" parameterType="SysNoticeStatus">
        update sys_notice_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id =
                #{noticeId},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteSysNoticeStatusById" parameterType="Long">
        update sys_notice_status set del_flag='2' where id = #{id}
    </update>

    <update id="deleteSysNoticeStatusByIds" parameterType="String">
        update sys_notice_status set del_flag='2' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>