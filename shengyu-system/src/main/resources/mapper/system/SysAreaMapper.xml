<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysAreaMapper">

    <resultMap type="SysArea" id="SysAreaResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="simpleName" column="simple_name"/>
        <result property="level" column="level"/>
        <result property="cityCode" column="city_code"/>
        <result property="zipCode" column="zip_code"/>
        <result property="merName" column="mer_name"/>
        <result property="lng" column="Lng"/>
        <result property="lat" column="lat"/>
        <result property="pinYin" column="pin_yin"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentId" column="parent_id"/>
    </resultMap>

    <sql id="selectSysAreaVo">
        select t.*
        ,p.id parent_id
        from sys_area t
        left join sys_area p on p.code=t.parent_code
    </sql>

    <select id="selectSysAreaList" parameterType="SysArea" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        <where>
            <if test="code != null  and code != ''">and t.code = #{code}</if>
            <if test="name != null  and name != ''">and t.name like concat('%', #{name}, '%')</if>
            <if test="parentCode != null  and parentCode != ''">and t.parent_code = #{parentCode}</if>
            <if test="simpleName != null  and simpleName != ''">and t.simple_name like concat('%', #{simpleName}, '%')
            </if>
            <if test="level != null and level!=0">and t.level = #{level}</if>
            <if test="cityCode != null  and cityCode != ''">and t.city_code = #{cityCode}</if>
            <if test="zipCode != null  and zipCode != ''">and t.zip_code = #{zipCode}</if>
            <if test="merName != null  and merName != ''">and t.mer_name like concat('%', #{merName}, '%')</if>
            <if test="lng != null ">and t.Lng = #{lng}</if>
            <if test="lat != null ">and t.lat = #{lat}</if>
            <if test="pinYin != null  and pinYin != ''">and t.pin_yin = #{pinYin}</if>
        </where>
    </select>

    <select id="selectSysAreaById" parameterType="Long" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertSysArea" parameterType="SysArea" useGeneratedKeys="true" keyProperty="id">
        insert into sys_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="simpleName != null">simple_name,</if>
            <if test="level != null">level,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="zipCode != null">zip_code,</if>
            <if test="merName != null">mer_name,</if>
            <if test="lng != null">Lng,</if>
            <if test="lat != null">lat,</if>
            <if test="pinYin != null">pin_yin,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="simpleName != null">#{simpleName},</if>
            <if test="level != null">#{level},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="zipCode != null">#{zipCode},</if>
            <if test="merName != null">#{merName},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="pinYin != null">#{pinYin},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateSysArea" parameterType="SysArea">
        update sys_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="simpleName != null">simple_name = #{simpleName},</if>
            <if test="level != null">level = #{level},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="zipCode != null">zip_code = #{zipCode},</if>
            <if test="merName != null">mer_name = #{merName},</if>
            <if test="lng != null">Lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="pinYin != null">pin_yin = #{pinYin},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAreaById" parameterType="Long">
        delete from sys_area where id = #{id}
    </delete>

    <delete id="deleteSysAreaByIds" parameterType="String">
        delete from sys_area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectActiveTree" resultType="AreaTreeVo" parameterType="Object">
        select id,code,parent_code,name from sys_area
        <if test="defaultCode != null and defaultCode != ''">
            where
            <foreach item="code" collection="defaultCode.split(',')" open="(" separator=" or " close=")">
                left(code,2) = left(#{code},2)
            </foreach>
        </if>
        union all
        select dept_id,dept_id,case when d.level=2 then a.code else d.parent_id end,dept_name
        from sys_dept d
        left join sys_area a on a.id=d.area_id
        where d.level>1 and d.level &lt;= #{level} and d.del_flag='0'
        <if test="defaultCode != null and defaultCode != ''">
            and
            <foreach item="code" collection="defaultCode.split(',')" open="(" separator=" or " close=")">
                left(a.code,2) = left(#{code},2)
            </foreach>
        </if>
    </select>

    <select id="selectActiveLevelTree" resultType="AreaTreeVo" parameterType="Object">
        select id,code,parent_code,name,`level` from sys_area
        <if test="defaultCode != null and defaultCode != ''">
            where
            <foreach item="code" collection="defaultCode.split(',')" open="(" separator=" or " close=")">
                left(code,2) = left(#{code},2)
            </foreach>
        </if>
        union all
        select dept_id,dept_id,case when d.level=2 then a.code else d.parent_id end,dept_name,d.`level`+2 `level`
        from sys_dept d
        left join sys_area a on a.id=d.area_id
        where d.level>1 and d.del_flag='0'
        <if test="defaultCode != null and defaultCode != ''">
            and
            <foreach item="code" collection="defaultCode.split(',')" open="(" separator=" or " close=")">
                left(a.code,2) = left(#{code},2)
            </foreach>
        </if>
        ${entity.params.dataScope}
    </select>
</mapper>