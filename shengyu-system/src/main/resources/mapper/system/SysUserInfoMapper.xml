<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysUserInfoMapper">

    <resultMap type="com.shengyu.system.domain.SysUserInfo" id="SysUserInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="idcard" column="idcard"/>
        <result property="sex" column="sex"/>
        <result property="deptId" column="dept_id"/>
        <result property="postId" column="post_id"/>
        <result property="phone" column="phone"/>
        <result property="type" column="type"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userInfoNumber" column="user_info_number"/>

    </resultMap>


    <sql id="selectSysUserInfoVo">
        select id, user_id, name, idcard, sex, dept_id, post_id, phone, type, del_flag, create_by, create_time, update_by, update_time,use_status, remark,user_info_number,edu from sys_user_info
    </sql>

    <select id="selectSysUserInfoList" parameterType="com.shengyu.system.domain.SysUserInfo"
            resultMap="SysUserInfoResult">
        select uinfo.id, uinfo.user_id, uinfo.name, uinfo.idcard, uinfo.sex, uinfo.dept_id, uinfo.post_id, uinfo.phone,
        uinfo.type, uinfo.del_flag, uinfo.create_by, uinfo.create_time, uinfo.update_by, uinfo.update_time,
        uinfo.remark,
        u.user_name,d.dept_name,p.post_name,uinfo.use_status,uinfo.user_info_number,uinfo.edu
        from sys_user_info uinfo
        left join sys_user u on u.user_id=uinfo.create_by
        left join sys_dept d on d.dept_id=uinfo.dept_id
        left join sys_post p on p.post_id=uinfo.post_id

        where uinfo.del_flag = '0'
        <if test="userId != null ">and uinfo.user_id = #{userId}</if>
        <if test="name != null  and name != ''">and uinfo.name like concat('%', #{name}, '%')</if>
        <if test="idcard != null  and idcard != ''">and uinfo.idcard = #{idcard}</if>
        <if test="sex != null  and sex != ''">and uinfo.sex = #{sex}</if>
        <if test="deptId != null ">and uinfo.dept_id = #{deptId}</if>
        <if test="deptName != null ">and d.dept_name like concat('%',#{deptName}, '%')</if>
        <if test="postId != null ">and uinfo.post_id = #{postId}</if>
        <if test="phone != null  and phone != ''">and uinfo.phone = #{phone}</if>
        <if test="type != null  and type != ''">and uinfo.type = #{type}</if>
        <if test="useStatus != null  and useStatus != ''">and uinfo.use_status = #{useStatus}</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(uinfo.create_time,'%y%m%d %H:%i:%s') &gt;= date_format(#{params.beginTime},'%y%m%d
            %H:%i:%s')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(uinfo.create_time,'%y%m%d %H:%i:%s' ) &lt;= date_format(#{params.endTime},'%y%m%d %H:%i:%s')
        </if>
        <if test="notInRoomId != null and  notInRoomId !=''">
            and uinfo.use_status='0' and uinfo.id not in (select rp.user_info_id from lab_room_person rp where
            rp.room_id=#{notInRoomId})
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND uinfo.tenant_id = #{tenantId}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectSysUserInfoById" parameterType="Long" resultMap="SysUserInfoResult">
        <include refid="selectSysUserInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectByNameNumber" parameterType="com.shengyu.system.domain.SysUserInfo"
            resultType="com.shengyu.system.domain.SysUserInfo">
        select uinfo.id, uinfo.user_id, uinfo.name, uinfo.idcard, uinfo.sex, uinfo.dept_id, uinfo.post_id, uinfo.phone,
        uinfo.type, uinfo.del_flag, uinfo.create_by, uinfo.create_time, uinfo.update_by, uinfo.update_time,
        uinfo.remark,
        u.user_name,d.dept_name,p.post_name,uinfo.use_status,uinfo.user_info_number,uinfo.edu
        from sys_user_info uinfo
        left join sys_user u on u.user_id=uinfo.create_by
        left join sys_dept d on d.dept_id=uinfo.dept_id
        left join sys_post p on p.post_id=uinfo.post_id
        where uinfo.del_flag = '0' and uinfo.use_status='0'
        <if test="searchValue != null and searchValue!=''">
            and (uinfo.name like concat('%', #{searchValue}, '%')
            or uinfo.idcard like concat('%', #{searchValue}, '%')
            or uinfo.user_info_number like concat('%', #{searchValue}, '%')
            )
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <insert id="insertSysUserInfo" parameterType="com.shengyu.system.domain.SysUserInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="idcard != null">idcard,</if>
            <if test="sex != null">sex,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="phone != null">phone,</if>
            <if test="type != null">type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy !=''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="userInfoNumber != null">user_info_number,</if>
            <if test="edu != null">edu,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="tenantId != null and tenantId != ''">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="idcard != null">#{idcard},</if>
            <if test="sex != null">#{sex},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="type != null">#{type},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy !=''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="userInfoNumber != null">#{userInfoNumber},</if>
            <if test="edu != null">#{edu},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
        </trim>
    </insert>

    <update id="updateSysUserInfo" parameterType="com.shengyu.system.domain.SysUserInfo">
        update sys_user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="idcard != null">idcard = #{idcard},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="type != null">type = #{type},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy !=''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="userInfoNumber != null">user_info_number = #{userInfoNumber},</if>
            <if test="edu != null">edu = #{edu},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysUserInfoById" parameterType="Long">
        delete from sys_user_info where id = #{id}
    </delete>

    <delete id="deleteSysUserInfoByIds" parameterType="String">
        delete from sys_user_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
