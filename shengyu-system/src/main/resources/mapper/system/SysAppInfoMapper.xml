<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysAppInfoMapper">

    <resultMap type="SysAppInfo" id="SysAppInfoResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="platform" column="platform"/>
        <result property="version" column="version"/>
        <result property="type" column="type"/>
        <result property="packageUrl" column="package_url"/>
        <result property="serverVersion" column="server_version"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectSysAppInfoVo">
        select * from sys_app_info
    </sql>

    <select id="selectSysAppInfoList" parameterType="SysAppInfo" resultMap="SysAppInfoResult">
        <include refid="selectSysAppInfoVo"/>
        <where>
            <if test="title != null  and title != ''">
                and title = #{title}
            </if>
            <if test="content != null  and content != ''">
                and content = #{content}
            </if>
            <if test="platform != null  and platform != ''">
                and platform = #{platform}
            </if>
            <if test="version != null  and version != ''">
                and version = #{version}
            </if>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="packageUrl != null  and packageUrl != ''">
                and package_url = #{packageUrl}
            </if>
            <if test="serverVersion != null  and serverVersion != ''">
                and server_version = #{serverVersion}
            </if>
            <if test="tenantId != null  and tenantId != ''">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="selectSysAppInfoById" parameterType="Long"
            resultMap="SysAppInfoResult">
        <include refid="selectSysAppInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAppInfo" parameterType="SysAppInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_app_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,
            </if>
            <if test="content != null and content != ''">content,
            </if>
            <if test="platform != null">platform,
            </if>
            <if test="version != null">version,
            </if>
            <if test="type != null">type,
            </if>
            <if test="packageUrl != null">package_url,
            </if>
            <if test="serverVersion != null">server_version,
            </if>
            <if test="tenantId != null  and tenantId != ''">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},
            </if>
            <if test="content != null and content != ''">#{content},
            </if>
            <if test="platform != null">#{platform},
            </if>
            <if test="version != null">#{version},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="packageUrl != null">#{packageUrl},
            </if>
            <if test="serverVersion != null">#{serverVersion},
            </if>
            <if test="tenantId != null  and tenantId != ''">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateSysAppInfo" parameterType="SysAppInfo">
        update sys_app_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title =
                #{title},
            </if>
            <if test="content != null and content != ''">content =
                #{content},
            </if>
            <if test="platform != null">platform =
                #{platform},
            </if>
            <if test="version != null">version =
                #{version},
            </if>
            <if test="type != null">type =
                #{type},
            </if>
            <if test="packageUrl != null">package_url =
                #{packageUrl},
            </if>
            <if test="serverVersion != null">server_version =
                #{serverVersion},
            </if>
            <if test="tenantId != null  and tenantId != ''">
                tenant_id = #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAppInfoById" parameterType="Long">
        delete from sys_app_info where id = #{id}
    </delete>

    <delete id="deleteSysAppInfoByIds" parameterType="String">
        delete from sys_app_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>