<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="merName" column="mer_name"/>
        <result property="areaId" column="area_id"/>
        <result property="userType" column="user_type"/>
        <result property="wechatName" column="wechat_name"/>
        <result property="openId" column="open_id"/>
        <result property="roleNameStr" column="role_name_str"/>
        <result property="idCard" column="id_card"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="lastUpdPwdTime" column="last_upd_pwd_time"/>
        <result property="isEnc" column="is_enc"/>
        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="deptCode" column="dept_code"/>
        <result property="areaId" column="area_id"/>
        <result property="address" column="address"/>
        <result property="level" column="level"/>
        <result property="ancestors" column="ancestors"/>
        <result property="type" column="type"/>
        <result property="creditCode" column="credit_code"/>
        <result property="isBusiness" column="is_business"/>
        <result property="tenantId" column="d_tenant_id"/>
        <result property="registerName" column="register_name"/>
    </resultMap>

    <resultMap id="RoleResult" type="SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <resultMap id="sysUserDto" type="com.shengyu.common.core.domain.dto.SysUserDto">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="idCard" column="id_card"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.user_type,
        u.wechat_name,u.open_id,u.tenant_id,u.id_card,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,d.tenant_id d_tenant_id,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        ,d.address,d.dept_code,d.level,d.ancestors,u.last_upd_pwd_time,d.is_business,d.credit_code,d.type
        ,aa.id area_id
        ,aa.mer_name
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        left join sys_area aa on aa.id=d.area_id
    </sql>

    <sql id="selectUserDto">
        select u.* from sys_user u
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.tenant_id,u.id_card,
        d.dept_name, d.leader,d.address,d.area_id,u.last_upd_pwd_time
        ,aa.mer_name
        ,u.user_type
        ,d.dept_code
        ,d.level
        ,d.ancestors
        ,r.role_name role_name_str
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_area aa on aa.id=d.area_id
        left join (select ur.user_id,group_concat(r.role_name separator ',') role_name from sys_user_role ur
        left join sys_role r on r.role_id = ur.role_id group by ur.user_id) r on r.user_id=u.user_id
        where u.del_flag = '0'
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="userType != null and userType != ''">
            AND u.user_type = #{userType}
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="idCard != null and idCard != ''">
            AND u.id_card like concat('%', #{idCard}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE MATCH(ancestors) against(#{deptId}) )
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND u.tenant_id = #{tenantId}
        </if>
        <if test="roleIds != null and roleIds.length > 0">
            AND u.user_id in
            (select user_id from sys_user_role where role_id in
            <foreach item="rId" collection="roleIds" open="(" separator="," close=")">
                #{rId}
            </foreach>
            )
        </if>
        <if test="areaId != null and areaId != ''">
            AND exists (
            select 1 from sys_area aa4
            left join sys_area aa3 on aa4.parent_code = aa3.code
            left join sys_area aa2 on aa3.parent_code = aa2.code
            left join sys_area aa1 on aa2.parent_code = aa1.code
            where aa4.id = d.area_id and (aa4.id=#{areaId} or aa3.id=#{areaId}
            or aa2.id=#{areaId} or aa1.id=#{areaId})
            )
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where (u.user_name = #{userName} or u.phonenumber = #{userName} or u.id_card = #{userName})
        and u.del_flag = '0'
    </select>

    <select id="selectUserByPhone" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.phonenumber = #{phone} and u.del_flag = '0'
    </select>


    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId} and u.del_flag = '0'
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>

    <select id="checkIdCardUnique" parameterType="String" resultMap="SysUserResult">
		select t.user_id, t.id_card,d.area_id,t.tenant_id from sys_user t
		left join sys_dept d on t.dept_id = d.dept_id
		where t.id_card = #{idCard} and t.del_flag = '0' limit 1
	</select>

    <select id="selectByKeyWord" parameterType="SysUser" resultType="com.shengyu.common.core.domain.entity.SysUser">
        select u.user_id,u.user_name,u.nick_name, u.phonenumber from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="deptId != null">
            AND u.dept_id = #{deptId}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND u.tenant_id = #{tenantId}
        </if>
        <if test="searchValue != null and searchValue != ''">
            AND (u.user_name like concat('%', #{searchValue}, '%')
            or u.phonenumber like concat('%', #{searchValue}, '%')
            )
        </if>

        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    <select id="selectListByIds" resultType="com.shengyu.common.core.domain.entity.SysUser">
        select user_id,user_name,nick_name, phonenumber from sys_user
        where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>


    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="userType != null and userType != ''">user_type,</if>
        <if test="wechatName != null and wechatName != ''">wechat_name,</if>
        <if test="idCard != null and idCard != ''">id_card,</if>
        <if test="tenantId != null and tenantId != ''">tenant_id,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="deptId != null and deptId != ''">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="userType != null and userType != ''">#{userType},</if>
        <if test="wechatName != null and wechatName != ''">#{wechatName},</if>
        <if test="idCard != null and idCard != ''">#{idCard},</if>
        <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
        <if test="dongleInfo != null and dongleInfo != ''">#{dongleInfo},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="SysUser">
        update sys_user
        <set>
            <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="userType != null and userType != ''">user_type=#{userType},</if>
            <if test="wechatName != null and wechatName != ''">wechat_name=#{wechatName},</if>
            <if test="openId != null and openId != ''">open_id=#{openId},</if>
            <if test="idCard != null and idCard != ''">id_card=#{idCard},</if>
            <if test="tenantId != null and tenantId != ''">tenant_id=#{tenantId},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

    <update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

    <update id="updateUserPhone" parameterType="SysUser">
 		update sys_user set phonenumber = #{phone} where user_name = #{userName}
	</update>

    <update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password},last_upd_pwd_time=sysdate() where user_name = #{userName}
	</update>

    <delete id="deleteUserById" parameterType="Long">
		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>

    <delete id="deleteUserByIds" parameterType="Long">
        update sys_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="selectById" parameterType="Long" resultMap="sysUserDto">
        <include refid="selectUserDto"/>
        where u.user_id = #{userId} and u.del_flag = '0'
    </select>

    <update id="batchResetPwd" parameterType="Object">
        update sys_user set password = #{password} where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <update id="updateUserToken" parameterType="Object">
        update sys_user set token=#{token} where user_name=#{userName}
    </update>

    <select id="getUserByToken" parameterType="Object" resultMap="SysUserResult">
        select * from sys_user where token=#{token} and del_flag = '0'
    </select>

    <select id="countUser" resultType="Integer">
        select count(1) from sys_user where del_flag = '0' and status='1'
    </select>

    <select id="selectListByRoleKey" parameterType="Object" resultType="String">
         select user_name from sys_user where user_id in
         (select user_id from sys_user_role where role_id in
         (select role_id from sys_role where role_key=#{roleKey} and tenant_id=#{tenantId}))
         and dept_id =#{deptId} and del_flag = '0'  and status='1'
    </select>

    <select id="selectUserByDepts" resultType="SysUser" parameterType="Object">
        select * from sys_user where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="getUserByPost" resultMap="SysUserResult" parameterType="Object">
        select  t.*,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        d.address,d.dept_code,d.level,d.ancestors,d.is_business,d.credit_code,d.type
        from sys_user t
        inner join sys_user_post up on up.user_id=t.user_id
        inner join sys_post p on p.post_id=up.post_id
        left join sys_dept d on t.dept_id = d.dept_id
        where p.post_code=#{postCode} and t.dept_id=#{deptId} limit 1
    </select>

    <update id="updateSyncRemark" parameterType="Object">
        <choose>
            <when test="remark != null and remark != ''">
                update sys_user set remark = concat(IFNULL(remark,''),#{remark}) where user_id=#{userId}
            </when>
            <otherwise>
                update sys_user set remark = substr(remark,1,locate('同步失败',remark)-1) where user_id=#{userId}
                and locate('同步失败',remark) > 0
            </otherwise>
        </choose>
    </update>

    <select id="getUserByPostInRoleDept" resultType="SysUser" parameterType="Object">
        select DISTINCT u.*
        from sys_user u
        inner join sys_user_post t on u.user_id=t.user_id
        inner join sys_post p on t.post_id=p.post_id
        left join sys_user_role r on r.user_id=t.user_id
        where p.post_code=#{postCode} and u.tenant_id=#{tenantId}
        and exists(select 1 from sys_role_dept where role_id=r.role_id and dept_id=#{deptId})
        limit 1
    </select>

</mapper>
