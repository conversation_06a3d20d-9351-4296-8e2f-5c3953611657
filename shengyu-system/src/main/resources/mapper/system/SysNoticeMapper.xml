<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysNoticeMapper">

    <resultMap type="SysNotice" id="SysNoticeResult">
        <result property="noticeId" column="notice_id"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="isAll" column="is_all"/>
    </resultMap>

    <sql id="selectNoticeVo">
        select t.notice_id, t.notice_title, t.notice_type, cast(t.notice_content as char) as notice_content, t.status,
        t.create_by, t.create_time, t.update_by, t.update_time, t.remark,t.is_all
		from sys_notice t
		left join sys_user u on u.user_name=t.create_by and u.del_flag='0'
		left join sys_dept d on d.dept_id=u.dept_id
    </sql>

    <select id="selectNoticeById" parameterType="Long" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where t.notice_id = #{noticeId}
    </select>

    <sql id="whereCommon">
        <if test="noticeTitle != null and noticeTitle != ''">
            AND t.notice_title like concat('%', #{noticeTitle}, '%')
        </if>
        <if test="noticeType != null and noticeType != ''">
            AND t.notice_type = #{noticeType}
        </if>
        <if test="createBy != null and createBy != ''">
            AND t.create_by = #{createBy}
        </if>
    </sql>

    <select id="selectNoticeList" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where>
            <include refid="whereCommon"/>
        </where>
        ${params.dataScope}
    </select>

    <insert id="insertNotice" parameterType="SysNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into sys_notice (
        <if test="noticeTitle != null and noticeTitle != '' ">notice_title,</if>
        <if test="noticeType != null and noticeType != '' ">notice_type,</if>
        <if test="noticeContent != null and noticeContent != '' ">notice_content,</if>
        <if test="status != null and status != '' ">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="isAll != null and isAll != ''">is_all,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
        <if test="noticeType != null and noticeType != ''">#{noticeType},</if>
        <if test="noticeContent != null and noticeContent != ''">#{noticeContent},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="isAll != null and isAll != ''">#{isAll},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateNotice" parameterType="SysNotice">
        update sys_notice
        <set>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeType != null and noticeType != ''">notice_type = #{noticeType},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isAll != null and isAll != ''">is_all=#{isAll},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteNoticeById" parameterType="Long">
        delete from sys_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteNoticeByIds" parameterType="Long">
        delete from sys_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <select id="selectOwnNoticeList" parameterType="SysNoticeDto" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where (t.notice_type = '2' or (t.notice_type = '1' and
        ((t.is_all='1' ${params.dataScope}) or (t.is_all='0' and d.dept_id in
        (select dept_id from sys_notice_dept where notice_id=t.notice_id) ) ) ))
        <include refid="whereCommon"/>
        <if test="statusType != null and statusType != ''">
            <choose>
                <when test="statusType == '1'.toString()">
                    and exists (select 1 from sys_notice_status where notice_id=t.notice_id and user_id=#{userId})
                </when>
                <otherwise>
                    and not exists (select 1 from sys_notice_status where notice_id=t.notice_id and
                    user_id=#{userId})
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectUnReadNoticeList" parameterType="Object" resultType="SysNoticeVo">
        select t.* from sys_notice t
        left join sys_user u on u.user_name=t.create_by and u.del_flag='0'
        left join sys_dept d on d.dept_id=u.dept_id
        left join sys_notice_status ts on ts.notice_id=t.notice_id and ts.user_id=#{userId}
        where t.status='1'
        <if test="searchVal != null and searchVal != ''">
            and (t.notice_title like concat('%',#{searchVal},'%')
            or t.notice_content like concat('%',#{searchVal},'%')
            )
        </if>
        and ts.id is null
        and (t.notice_type = '2' or (t.notice_type = '1' and
        ((t.is_all='1' ${params.dataScope}) or (t.is_all='0' and d.dept_id in
        (select dept_id from sys_notice_dept where notice_id=t.notice_id)))
        ))
    </select>

    <select id="selectReadNoticeList" parameterType="Object" resultType="SysNoticeVo">
        select t.*,ts.create_time read_time
        from sys_notice t
        inner join sys_notice_status ts on ts.notice_id=t.notice_id and ts.user_id=#{userId}
        where t.status='1' and ts.del_flag='0'
        <if test="searchVal != null and searchVal != ''">
            and (t.notice_title like concat('%',#{searchVal},'%')
            or t.notice_content like concat('%',#{searchVal},'%')
            )
        </if>
    </select>

</mapper>