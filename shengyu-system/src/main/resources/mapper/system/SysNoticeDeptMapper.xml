<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysNoticeDeptMapper">

    <resultMap type="SysNoticeDept" id="SysNoticeDeptResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <sql id="selectSysNoticeDeptVo">
        select * from sys_notice_dept
    </sql>

    <select id="selectSysNoticeDeptList" parameterType="SysNoticeDept" resultMap="SysNoticeDeptResult">
        <include refid="selectSysNoticeDeptVo"/>
        where 1=1
        <if test="noticeId != null ">
            and notice_id = #{noticeId}
        </if>
        <if test="deptId != null ">
            and dept_id = #{deptId}
        </if>
    </select>

    <select id="selectSysNoticeDeptById" parameterType="Long"
            resultMap="SysNoticeDeptResult">
        <include refid="selectSysNoticeDeptVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysNoticeDept" parameterType="SysNoticeDept" useGeneratedKeys="true" keyProperty="id">
        insert into sys_notice_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="noticeId != null">notice_id,
            </if>
            <if test="deptId != null">dept_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="noticeId != null">#{noticeId},
            </if>
            <if test="deptId != null">#{deptId},
            </if>
        </trim>
    </insert>

    <update id="updateSysNoticeDept" parameterType="SysNoticeDept">
        update sys_notice_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id =
                #{noticeId},
            </if>
            <if test="deptId != null">dept_id =
                #{deptId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysNoticeDeptByNoticeId" parameterType="Long">
        delete from sys_notice_dept where notice_id = #{noticeId}
    </delete>

    <delete id="deleteSysNoticeDeptByNoticeIds" parameterType="Long">
        delete from sys_notice_dept where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
</mapper>