<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.HelpDocumentMapper">

    <resultMap type="HelpDocument" id="HelpDocumentResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="title" column="title"/>
        <result property="orderNum" column="order_num"/>
        <result property="contents" column="contents"/>
    </resultMap>

    <sql id="selectHelpDocumentVo">
        select id,parent_id, title, order_num, contents from sys_help_document t
    </sql>

    <select id="selectHelpDocumentList" parameterType="HelpDocument" resultMap="HelpDocumentResult">
        select id,parent_id,title, order_num from sys_help_document t
        where 1=1
        <if test="title != null  and title != ''">
            and t.title = #{title}
        </if>
        <if test="orderNum != null ">
            and t.order_num = #{orderNum}
        </if>
        <if test="contents != null  and contents != ''">
            and t.contents = #{contents}
        </if>
        order by order_num
    </select>

    <select id="selectHelpDocumentById" parameterType="Long"
            resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertHelpDocument" parameterType="HelpDocument" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_help_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="parentId != null">parent_id,
            </if>
            <if test="title != null and title != ''">title,
            </if>
            <if test="orderNum != null">order_num,
            </if>
            <if test="contents != null">contents,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="parentId != null">#{parentId},
            </if>
            <if test="title != null and title != ''">#{title},
            </if>
            <if test="orderNum != null">#{orderNum},
            </if>
            <if test="contents != null">#{contents},
            </if>
        </trim>
    </insert>

    <update id="updateHelpDocument" parameterType="HelpDocument">
        update sys_help_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},
            </if>
            <if test="title != null and title != ''">title =
                #{title},
            </if>
            <if test="orderNum != null">order_num =
                #{orderNum},
            </if>
            <if test="contents != null">contents =
                #{contents},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHelpDocumentById" parameterType="Long">
        delete from sys_help_document where id = #{id}
    </delete>

    <delete id="deleteHelpDocumentByIds" parameterType="String">
        delete from sys_help_document where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="hasChildById" resultType="Integer" parameterType="Object">
        select count(1) from sys_help_document where parent_id=#{id}
    </select>
</mapper>