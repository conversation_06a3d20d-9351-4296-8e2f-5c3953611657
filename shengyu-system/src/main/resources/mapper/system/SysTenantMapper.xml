<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysTenantMapper">

    <resultMap type="SysTenant" id="SysTenantResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectSysTenantVo">
        select id, name, status, create_by, create_time from sys_tenant
    </sql>

    <select id="selectSysTenantList" parameterType="SysTenant" resultMap="SysTenantResult">
        <include refid="selectSysTenantVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectSysTenantById" parameterType="Integer"
            resultMap="SysTenantResult">
        <include refid="selectSysTenantVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysTenant" parameterType="SysTenant" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updateSysTenant" parameterType="SysTenant">
        update sys_tenant
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTenantById" parameterType="Integer">
        delete from sys_tenant where id = #{id}
    </delete>

    <delete id="deleteSysTenantByIds" parameterType="String">
        delete from sys_tenant where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="initData" parameterType="Object">
        insert into t_base_subject(sys_code, name, code, type,count_type, direction, level, parent_id,
        is_auxiliary, is_num, is_limit, auxiliary_type, accounting_date, dept_id, create_by,
        create_time, update_by, update_time, remark, tenant_id,all_name)
        select sys_code, name, code, type,count_type, direction, level, parent_id,
        is_auxiliary, is_num, is_limit, auxiliary_type, accounting_date, ${deptId}, 'admin',
        SYSDATE(), null, null, remark, '${tenantId}',all_name
        from t_base_subject where tenant_id='0';
        update t_base_subject t left join t_base_subject gp on gp.id=t.parent_id and gp.tenant_id='0'
        left join t_base_subject rp on rp.tenant_id='${tenantId}' and rp.sys_code=gp.sys_code
        set t.parent_id=IFNULL(rp.id,0) where t.tenant_id='${tenantId}';
        insert into t_base_voucher_template(name, type, remark, create_by, create_time,
        update_by, update_time, dept_id, tenant_id)
        select name, type, remark, 'admin', SYSDATE(),
        null, null, ${deptId}, '${tenantId}'
        from t_base_voucher_template where tenant_id='0';
        insert into t_base_voucher_template_subs(subject_id, digest, subject_direction,
        template_id, tenant_id,dept_id, remark)
        select s2.id, t.digest, t.subject_direction,
        t.template_id, '${tenantId}',#{deptId}, t.remark
        from t_base_voucher_template_subs t
        left join t_base_subject s1 on s1.id=t.subject_id and s1.dept_id=t.dept_id
        left join t_base_subject s2 on s1.sys_code=s2.sys_code and s2.dept_id=#{deptId}
        where t.tenant_id='0';
        update t_base_voucher_template_subs t
        left join t_base_voucher_template gp on gp.id=t.template_id and gp.tenant_id='0'
        left join t_base_voucher_template rp on gp.remark=rp.remark and rp.tenant_id='${tenantId}'
        set t.template_id=rp.id where t.tenant_id='${tenantId}';
    </insert>

</mapper>