<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysDeptMapper">
    <resultMap type="SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptCode" column="dept_code"/>
        <result property="areaId" column="area_id"/>
        <result property="merName" column="mer_name"/>
        <result property="level" column="level"/>
        <result property="address" column="address"/>
        <result property="type" column="type"/>
        <result property="address" column="address"/>
        <result property="isBusiness" column="is_business"/>
        <result property="creditCode" column="credit_code"/>
        <result property="parentCode" column="parent_code"/>
        <result property="nature" column="nature"/>
        <result property="codeId" column="code_id"/>
        <result property="registerName" column="register_name"/>
    </resultMap>

    <sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader,
               d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,d.address,d.tenant_id,
               d.dept_code,d.level,d.area_id,pd.dept_name parent_name,a.mer_name,d.type,d.is_business,
               d.credit_code,pd.dept_code parent_code,d.nature
        from sys_dept d
        left join sys_dept pd on pd.dept_id=d.parent_id
        left join sys_area a on a.id = d.area_id
    </sql>

    <select id="selectDeptList" parameterType="com.shengyu.common.core.domain.entity.SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND d.tenant_id = #{tenantId}
        </if>
        <if test="parentId != null and parentId != 0">
            AND d.parent_id = #{parentId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND d.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND d.status = #{status}
        </if>
        <if test="level != null">
            AND d.level = #{level}
        </if>
        <if test="fromLevel != null">
            AND d.level >= #{fromLevel}
        </if>
        <if test="toLevel != null">
            AND d.level &lt;= #{toLevel}
        </if>
        <if test="areaId != null">
            AND d.area_id = #{areaId}
        </if>
        <if test="leader != null and leader !=''">
            AND d.leader like concat('%', #{leader}, '%')
        </if>
        <if test="phone != null and phone !=''">
            AND d.phone =#{phone}
        </if>
        <if test="deptCode != null and deptCode !=''">
            AND d.dept_code =#{deptCode}
        </if>
        <if test="nature != null and nature !=''">
            AND d.nature =#{nature}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(d.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(d.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="createBy != null and createBy != ''">
            AND d.create_by like concat('%', #{createBy}, '%')
        </if>
        <if test="type != null and type !=''">
            AND d.type =#{type}
        </if>
        <if test="isBusiness != null and isBusiness !=''">
            AND d.is_business =#{isBusiness}
        </if>
        <if test="params.deptNature != null and params.deptNature != ''">
            and ((d.nature =#{params.deptNature} and d.is_business='1') or d.is_business !='1')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by d.order_num,d.dept_id
    </select>

    <select id="selectDeptListByRoleId" resultType="Integer">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.order_num,d.dept_id
    </select>

    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.dept_id = #{deptId}
    </select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>

    <select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where MATCH(ancestors) against(#{deptId}) and dept_id != #{deptId} and del_flag='0'
	</select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 1 and del_flag = '0' and MATCH(ancestors) against(#{deptId}) and dept_id != #{deptId}
	</select>

    <select id="checkDeptCodeUnique" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where (d.dept_code=#{deptCode} or d.credit_code=#{deptCode}) and d.del_flag = '0' limit 1
    </select>

    <insert id="insertDept" parameterType="com.shengyu.common.core.domain.entity.SysDept" useGeneratedKeys="true"
            keyProperty="deptId">
        insert into sys_dept(
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="parentId != null and parentId != 0">parent_id,</if>
        <if test="deptName != null and deptName != ''">dept_name,</if>
        <if test="ancestors != null and ancestors != ''">ancestors,</if>
        <if test="orderNum != null and orderNum != ''">order_num,</if>
        <if test="leader != null and leader != ''">leader,</if>
        <if test="phone != null and phone != ''">phone,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="status != null">status,</if>
        <if test="areaId != null">area_id,</if>
        <if test="level != null">level,</if>
        <if test="address != null and address != ''">address,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="deptCode != null and deptCode != ''">dept_code,</if>
        <if test="type != null and type != ''">type,</if>
        <if test="isBusiness != null and isBusiness != ''">is_business,</if>
        <if test="tenantId != null and tenantId != ''">tenant_id,</if>
        <if test="creditCode != null and creditCode != ''">credit_code,</if>
        <if test="nature != null and nature !=''">nature,</if>
        <if test="isVch != null and isVch !=''">is_vch,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="parentId != null and parentId != 0">#{parentId},</if>
        <if test="deptName != null and deptName != ''">#{deptName},</if>
        <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
        <if test="leader != null and leader != ''">#{leader},</if>
        <if test="phone != null and phone != ''">#{phone},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="status != null">#{status},</if>
        <if test="areaId != null">#{areaId},</if>
        <if test="level != null">#{level},</if>
        <if test="address != null and address != ''">#{address},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="deptCode != null and deptCode != ''">#{deptCode},</if>
        <if test="type != null and type != ''">#{type},</if>
        <if test="isBusiness != null and isBusiness != ''">#{isBusiness},</if>
        <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
        <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
        <if test="nature != null and nature !=''">#{nature},</if>
        <if test="isVch != null and isVch !=''">#{isVch},</if>
        sysdate()
        )
    </insert>

    <update id="updateDept" parameterType="com.shengyu.common.core.domain.entity.SysDept">
        update sys_dept
        <set>
            <if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="level != null">level = #{level},</if>
            <if test="address != null and address != ''">address=#{address},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="deptCode != null and deptCode != ''">dept_code = #{deptCode},</if>
            <if test="type != null and type != ''">type= #{type},</if>
            <if test="isBusiness != null and isBusiness != ''">is_business= #{isBusiness},</if>
            <if test="creditCode != null and creditCode != ''">credit_code= #{creditCode},</if>
            <if test="nature != null and nature !=''">nature=#{nature},</if>
            <if test="tenantId != null and tenantId != ''">tenant_id= #{tenantId},</if>
            <if test="isVch != null and isVch !=''">is_vch= #{isVch},</if>
            update_time = sysdate()
        </set>
        where dept_id = #{deptId}
    </update>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update sys_dept set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case dept_id" close="end">
            when #{item.deptId} then #{item.ancestors}
        </foreach>
        where dept_id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal" parameterType="Long">
        update sys_dept set status = '1' where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '2' where dept_id = #{deptId}
	</delete>

    <select id="selectDeptByClassId" parameterType="Long" resultMap="SysDeptResult">
        select ds.*
        from sys_dept t
        left join sys_dept dg on t.parent_id=dg.dept_id
        left join sys_dept ds on dg.parent_id=ds.dept_id
        where t.dept_id=#{id} and t.del_flag='0' order by t.parent_id,t.order_num
    </select>

    <select id="selectClassDeptList" resultType="Map">
        select t.dept_id class_id,gt.dept_id grade_id,gt.parent_id school_id,a1.id d_id,a2.id c_id,a3.id p_id
        from sys_dept t
        left join sys_dept gt on gt.dept_id=t.parent_id
        left join sys_area a1 on a1.id=t.area_id
        left join sys_area a2 on a2.code=a1.parent_code
        left join sys_area a3 on a3.code=a2.parent_code
        where t.`level`=4 order by t.parent_id,t.order_num
    </select>

    <select id="getTownList" resultType="SysDept" parameterType="String">
        select * from sys_dept where del_flag='0' and level &lt;=4 and tenant_id=#{tenantId} order by level,order_num
    </select>

    <select id="getDeptIdByAreaId" parameterType="Long" resultType="SysDept">
        select * from sys_dept where del_flag='0' and area_id=#{areaId} limit 1
    </select>

    <resultMap type="SupervisionVo" id="SupervisionMap">
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="isBusiness" column="is_business"/>
        <result property="childNumT" column="child_num_t"/>
        <result property="childNumV" column="child_num_v"/>
        <result property="childNumG" column="child_num_g"/>
        <result property="buildT" column="build_t"/>
        <result property="buildV" column="build_v"/>
        <result property="buildG" column="build_g"/>
        <result property="level" column="level"/>
        <collection property="monthList" javaType="java.util.List" resultMap="MonthResult"/>
    </resultMap>

    <resultMap type="MonthVo" id="MonthResult">
        <result property="month" column="month"/>
        <result property="num" column="num"/>
    </resultMap>

    <select id="supervision" resultMap="SupervisionMap" parameterType="ScheduleParam">
        select
        t.dept_id
        ,t.dept_name
        ,t.dept_code
        ,t.is_business
        ,IFNULL(t1.child_num,0) child_num_t
        ,IFNULL(t12.child_num,0) child_num_v
        ,IFNULL(t13.child_num,0) child_num_g
        ,IFNULL(t3.build,0) build_t
        ,IFNULL(t32.build,0) build_v
        ,IFNULL(t33.build,0) build_g
        ,vl.`month`
        ,vl.voucher_num num
        ,t.level
        from sys_dept t
        left join(select count(1) child_num,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='1'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t1 on t1.dept_id=t.dept_id
        left join(select count(1) child_num,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='2'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t12 on t12.dept_id=t.dept_id
        left join(select count(1) child_num,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='3'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t13 on t13.dept_id=t.dept_id
        left join(select IFNULL(sum(IF(sc.id is null,0,1)),0) build,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        left join t_base_custom_config sc on sc.dept_id = ct.dept_id and sc.code='subjectInitFlag' and sc.code_val='Y'
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='1'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t3 on t3.dept_id=t.dept_id
        left join(select IFNULL(sum(IF(sc.id is null,0,1)),0) build,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        left join t_base_custom_config sc on sc.dept_id = ct.dept_id and sc.code='subjectInitFlag' and sc.code_val='Y'
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='2'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t32 on t32.dept_id=t.dept_id
        left join(select IFNULL(sum(IF(sc.id is null,0,1)),0) build,tw.dept_id
        from sys_dept ct
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(ct.ancestors, ',',#{level}),',', -1)
        left join t_base_custom_config sc on sc.dept_id = ct.dept_id and sc.code='subjectInitFlag' and sc.code_val='Y'
        where ct.is_business='1' and ct.del_flag='0' and ct.nature='3'
        and ct.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and ct.type =#{params.deptType}
        </if>
        group by tw.dept_id
        )t33 on t33.dept_id=t.dept_id
        left join (
        select DATE_FORMAT(v.create_time,'%m') `month`,count(v.id) voucher_num,tw.dept_id
        from t_account_voucher v
        left join sys_dept d on d.dept_id=v.dept_id
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(d.ancestors, ',',#{level}),',', -1)
        where DATE_FORMAT(v.create_time,'%Y') = #{date}
        and v.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        group by DATE_FORMAT(v.create_time,'%m'),tw.dept_id
        ) vl on vl.dept_id=t.dept_id
        where t.del_flag='0'
        <choose>
            <when test="isChild != null and isChild == '1'.toString()">and t.parent_id=#{deptId}</when>
            <otherwise>and t.dept_id=#{deptId}</otherwise>
        </choose>
        ${params.dataScope}
        order by t.order_num,t.dept_id
    </select>

    <select id="countSchedule" resultType="CountScheduleVo" parameterType="ScheduleParam">
        select
        t.dept_id
        ,t.dept_name
        ,t.dept_code
        ,t.is_business
        ,t.level
        ,t1.child_num_t
        ,t1.child_num_v
        ,t1.child_num_g
        ,t1.build_t
        ,t1.build_v
        ,t1.build_g
        ,t1.greater_acc_date
        from sys_dept t
        left join(
        select IFNULL(sum(if(d.nature='1',1,0)),0) child_num_t
        ,IFNULL(sum(if(d.nature='2',1,0)),0) child_num_v
        ,IFNULL(sum(if(d.nature='3',1,0)),0) child_num_g
        ,IFNULL(sum(case when d.nature='1' and t.is_build='1' then 1 else 0 end),0) build_t
        ,IFNULL(sum(case when d.nature='2' and t.is_build='1' then 1 else 0 end),0) build_v
        ,IFNULL(sum(case when d.nature='3' and t.is_build='1' then 1 else 0 end),0) build_g
        ,IFNULL(sum(IF(sc.code_val>#{date} ,1,0)),0) greater_acc_date
        ,tw.dept_id
        from t_account_dept_indicator t
        left join sys_dept d on t.dept_id=d.dept_id
        left join t_base_custom_config sc on sc.dept_id = d.dept_id and sc.code='accountingDate'
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(d.ancestors, ',',#{level}),',', -1)
        where t.`year`= SUBSTR(#{date},1,4)
        and t.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        <if test="params.deptType != null and params.deptType != ''">
            and d.type =#{params.deptType}
        </if>
        group by tw.dept_id) t1 on t1.dept_id=t.dept_id
        where t.del_flag='0'
        <choose>
            <when test="isChild != null and isChild == '1'.toString()"> and t.parent_id=#{deptId}</when>
            <otherwise> and t.dept_id=#{deptId}</otherwise>
        </choose>
        ${params.dataScope}
        order by t.order_num,t.dept_id
    </select>

    <resultMap type="ScheduleDetailsVo" id="ScheduleDetailsMap">
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptCode" column="dept_code"/>
        <result property="townName" column="town_name"/>
        <result property="isBusiness" column="is_business"/>
        <result property="nature" column="nature"/>
        <result property="isBuild" column="is_build"/>
        <result property="buildDate" column="build_date"/>
        <result property="accMonth" column="acc_month"/>
        <result property="scheduleMonth" column="schedule_month"/>
        <collection property="monthList" javaType="java.util.List" resultMap="MonthResult"/>
    </resultMap>

    <select id="scheduleDetails" resultMap="ScheduleDetailsMap" parameterType="ScheduleParam">
        select
        t.dept_id
        ,t.dept_name
        ,t.dept_code
        ,tw.dept_name town_name
        ,t.is_business
        ,t.nature
        ,t.level
        ,IF(sc1.id is not null,1,0) is_build
        ,sc.code_val build_date
        ,sc2.code_val acc_month
        ,vl.`month`
        ,vl.voucher_num num
        ,case when sc2.code_val is null then ''
            when sc2.code_val &lt; #{date} then concat('未结转到',SUBSTR(#{date},1,4),'年')
            else sc2.code_val end schedule_month
        from sys_dept t
        left join t_base_custom_config sc1 on sc1.dept_id = t.dept_id and sc1.code='subjectInitFlag'
        and sc1.code_val='Y'
        left join t_base_custom_config sc on sc.dept_id = t.dept_id and sc.code='startDate'
        left join t_base_custom_config sc2 on sc2.dept_id = t.dept_id and sc2.code='accountingDate'
        left join (
        select DATE_FORMAT(v.create_time,'%m') `month`,v.dept_id
        ,count(v.id) voucher_num
        from t_account_voucher v
        where DATE_FORMAT(v.create_time,'%Y') = SUBSTR(#{date},1,4)
        group by DATE_FORMAT(v.create_time,'%m'),v.dept_id
        ) vl on t.dept_id = vl.dept_id
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(t.ancestors, ',',5),',', -1)
        where t.del_flag='0' and t.is_business='1' and MATCH(t.ancestors) against(#{deptId})
        <if test="params.deptType != null and params.deptType != ''">
            and t.type =#{params.deptType}
        </if>
        ${params.dataScope}
        order by t.order_num,t.dept_id
    </select>

    <select id="selectBuildDeptList" resultType="SysDept" parameterType="ScheduleParam">
        select t.*
        ,tw.dept_name town_name
        from sys_dept t
        left join t_base_custom_config sc on sc.dept_id = t.dept_id and sc.code='subjectInitFlag' and sc.code_val='Y'
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(t.ancestors, ',',5),',', -1)
        where t.del_flag='0' and t.is_business='1'
        and MATCH(t.ancestors) against(#{deptId})
        <if test="isBuild != null and isBuild != ''"></if>
        <choose>
            <when test="isBuild == '1'.toString">and sc.id is not null</when>
            <otherwise>and sc.id is null</otherwise>
        </choose>
        ${params.dataScope}
        order by t.order_num,t.dept_id
    </select>

    <select id="selectChildListForScope" resultType="SysDept" parameterType="SysDept">
        select t.* from sys_dept t
        where MATCH(t.ancestors) against(#{deptId}) and del_flag='0'
        ${params.dataScope}
        order by t.order_num,t.dept_id
    </select>

    <select id="getAllDeptName" parameterType="Long" resultType="String">
        SELECT GROUP_CONCAT(dept_name ORDER BY level ASC SEPARATOR '') from sys_dept t
        WHERE t.level>2 and t.level &lt; 5
        and t.dept_id in (
        SELECT substring_index(substring_index(a.ancestors,',',b.number + 1),',' ,- 1) AS new_id
        FROM sys_dept a JOIN t_sequence b ON b.number &lt; (length(a.ancestors) - length(REPLACE (a.ancestors, ',', '')) + 1)
        and a.dept_id=#{deptId})
    </select>

    <select id="getTownAndVillage" parameterType="Long" resultType="String">
        SELECT GROUP_CONCAT(dept_name ORDER BY level ASC SEPARATOR '') from sys_dept t
        WHERE t.level>3 and t.level &lt; 6
        and t.dept_id in (
        SELECT substring_index(substring_index(a.ancestors,',',b.number + 1),',' ,- 1) AS new_id
        FROM sys_dept a JOIN t_sequence b ON b.number &lt; (length(a.ancestors) - length(REPLACE (a.ancestors, ',', '')) + 1)
        and a.dept_id=#{deptId})
    </select>

    <select id="getAutoName" parameterType="Long" resultType="String">
        SELECT GROUP_CONCAT(dept_name ORDER BY level ASC SEPARATOR '-') from sys_dept t
        WHERE t.level>=2 and t.level &lt; 6
        and t.dept_id in (
        SELECT substring_index(substring_index(a.ancestors,',',b.number + 1),',' ,- 1) AS new_id
        FROM sys_dept a JOIN t_sequence b ON b.number &lt; (length(a.ancestors) - length(REPLACE (a.ancestors, ',', '')) + 1)
        and a.dept_id=#{deptId})
    </select>

    <select id="getAllDept" resultType="SysDept">
        select t.* from sys_dept t where del_flag='0' and is_business='1'
        and exists (select 1 from t_base_custom_config where code='startDate' and dept_id=t.dept_id)
    </select>

    <select id="getAllListByTenantId" parameterType="String" resultType="SysDept">
        select t.* from sys_dept t where del_flag='0' and tenant_id=#{tenantId}
    </select>

    <select id="getCodeStr" parameterType="Long" resultType="String">
        select case when length(dept_code)=14 || length(dept_code)=12
        then concat(SUBSTR(dept_code,1,6),SUBSTR(dept_code,8,2),SUBSTR(dept_code,11,2))
        else dept_code end codeStr
        from sys_dept where dept_id=#{deptId}
    </select>

    <select id="getCityDept" resultType="Map">
        select t.dept_id,m.start_mon,m.end_mon from sys_dept t
        inner join t_account_indicator_marks m on m.dept_id=t.dept_id
        where t.del_flag='0' and t.level=4
    </select>

    <select id="getDeptAccountingList" parameterType="ScheduleParam" resultType="AccountingVo">
        select sum(t.is_build) build_num,
        sum(if(d.nature='1',t.is_build,0)) build_t,
        sum(if(d.nature='2',t.is_build,0)) build_v,
        sum(if(d.nature='3',t.is_build,0)) build_g,
        sum(t.is_book) book_num,
        sum(if(d.nature='1',t.is_book,0)) book_t,
        sum(if(d.nature='2',t.is_book,0)) book_v,
        sum(if(d.nature='3',t.is_book,0)) book_g,
        IFNULL(sum(t.is_book)/sum(t.is_build)*100,0) book_rate,
        IFNULL(sum(if(d.nature='1',t.is_book,0))/sum(if(d.nature='1',t.is_build,0))*100,0) book_rate_t,
        IFNULL(sum(if(d.nature='2',t.is_book,0))/sum(if(d.nature='2',t.is_build,0))*100,0) book_rate_v,
        IFNULL(sum(if(d.nature='3',t.is_book,0))/sum(if(d.nature='3',t.is_build,0))*100,0) book_rate_g,
        sum(t.is_null_end) end_num,
        sum(if(d.nature='1',t.is_null_end,0)) end_t,
        sum(if(d.nature='2',t.is_null_end,0)) end_v,
        sum(if(d.nature='3',t.is_null_end,0)) end_g,
        IFNULL(sum(t.is_null_end)/sum(t.is_build)*100,0) end_rate,
        IFNULL(sum(if(d.nature='1',t.is_null_end,0))/sum(if(d.nature='1',t.is_build,0))*100,0) end_rate_t,
        IFNULL(sum(if(d.nature='2',t.is_null_end,0))/sum(if(d.nature='2',t.is_build,0))*100,0) end_rate_v,
        IFNULL(sum(if(d.nature='3',t.is_null_end,0))/sum(if(d.nature='3',t.is_build,0))*100,0) end_rate_g,
        sum(t.voucher_num) voucher_num,
        IFNULL(sum(t.voucher_num)/sum(t.is_build)/12,0) voucher_mon,
        sum(if(d.nature='1',t.voucher_num,0)) voucher_t,
        sum(if(d.nature='2',t.voucher_num,0)) voucher_v,
        sum(if(d.nature='3',t.voucher_num,0)) voucher_g,
        sum(t.asset_num) asset_num,
        IFNULL(sum(t.asset_num)/sum(t.is_build),0) asset_mon,
        sum(if(d.nature='1',t.asset_num,0)) asset_t,
        sum(if(d.nature='2',t.asset_num,0)) asset_v,
        sum(if(d.nature='3',t.asset_num,0)) asset_g,
        sum(t.contract_num) contract_num,
        IFNULL(sum(t.contract_num)/sum(t.is_build),0) contract_mon,
        sum(if(d.nature='1',t.contract_num,0)) contract_t,
        sum(if(d.nature='2',t.contract_num,0)) contract_v,
        sum(if(d.nature='3',t.contract_num,0)) contract_g,
        tw.dept_id,tw.dept_name
        from t_account_dept_indicator t
        left join sys_dept d on t.dept_id=d.dept_id
        left join sys_dept tw on tw.dept_id=SUBSTRING_INDEX(SUBSTRING_INDEX(d.ancestors, ',',#{level}+1),',', -1)
        where t.dept_id in (SELECT dept_id FROM sys_dept WHERE MATCH(ancestors) against(#{deptId}))
        and t.year=#{date}
        <if test="params.deptType != null and params.deptType != ''">
            and d.type =#{params.deptType}
        </if>
        group by tw.dept_id,tw.dept_name
        order by tw.order_num,tw.dept_id
    </select>
</mapper>