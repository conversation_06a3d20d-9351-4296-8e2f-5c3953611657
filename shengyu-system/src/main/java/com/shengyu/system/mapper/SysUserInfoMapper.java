package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.system.domain.SysUserInfo;

import java.util.List;

/**
 * 用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-09
 */
public interface SysUserInfoMapper extends BaseMapper<SysUserInfo> {
    /**
     * 查询用户信息
     *
     * @param id 用户信息ID
     * @return 用户信息
     */
    public SysUserInfo selectSysUserInfoById(Long id);

    /**
     * 查询用户信息列表
     *
     * @param sysUserInfo 用户信息
     * @return 用户信息集合
     */
    public List<SysUserInfo> selectSysUserInfoList(SysUserInfo sysUserInfo);

    /**
     * 新增用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    public int insertSysUserInfo(SysUserInfo sysUserInfo);

    /**
     * 修改用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    public int updateSysUserInfo(SysUserInfo sysUserInfo);

    /**
     * 删除用户信息
     *
     * @param id 用户信息ID
     * @return 结果
     */
    public int deleteSysUserInfoById(Long id);

    /**
     * 批量删除用户信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysUserInfoByIds(Long[] ids);

    /**
     * description: 根据关键字查询人员信息
     * version: 1.0
     * date: 2021/8/24 14:33
     *
     * @param userInfo
     * @return java.util.List<com.shengyu.system.domain.SysUserInfo>
     * @author: lwy
     */
    List<SysUserInfo> selectByNameNumber(SysUserInfo userInfo);
}
