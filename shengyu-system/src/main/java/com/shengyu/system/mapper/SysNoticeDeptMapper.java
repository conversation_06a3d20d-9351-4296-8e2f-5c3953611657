package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.system.domain.SysNoticeDept;

import java.util.List;

/**
 * 发布区域Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
public interface SysNoticeDeptMapper extends BaseMapper<SysNoticeDept> {
    /**
     * 查询发布区域
     *
     * @param id 发布区域ID
     * @return 发布区域
     */
    public SysNoticeDept selectSysNoticeDeptById(Long id);

    /**
     * 查询发布区域列表
     *
     * @param sysNoticeDept 发布区域
     * @return 发布区域集合
     */
    public List<SysNoticeDept> selectSysNoticeDeptList(SysNoticeDept sysNoticeDept);

    /**
     * 新增发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    public int insertSysNoticeDept(SysNoticeDept sysNoticeDept);

    /**
     * 修改发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    public int updateSysNoticeDept(SysNoticeDept sysNoticeDept);

    /**
     * 删除发布区域
     *
     * @return 结果
     */
    public int deleteSysNoticeDeptByNoticeId(Long noticeId);

    int deleteSysNoticeDeptByNoticeIds(Long[] noticeId);

}
