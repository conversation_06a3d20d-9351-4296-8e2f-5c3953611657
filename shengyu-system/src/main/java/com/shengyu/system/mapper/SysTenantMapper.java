package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.system.domain.SysTenant;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 租户Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
public interface SysTenantMapper extends BaseMapper<SysTenant> {
    /**
     * 查询租户
     *
     * @param id 租户ID
     * @return 租户
     */
    public SysTenant selectSysTenantById(Integer id);

    /**
     * 查询租户列表
     *
     * @param sysTenant 租户
     * @return 租户集合
     */
    public List<SysTenant> selectSysTenantList(SysTenant sysTenant);

    /**
     * 新增租户
     *
     * @param sysTenant 租户
     * @return 结果
     */
    public int insertSysTenant(SysTenant sysTenant);

    /**
     * 修改租户
     *
     * @param sysTenant 租户
     * @return 结果
     */
    public int updateSysTenant(SysTenant sysTenant);

    /**
     * 删除租户
     *
     * @param id 租户ID
     * @return 结果
     */
    public int deleteSysTenantById(Integer id);

    /**
     * 批量删除租户
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysTenantByIds(Integer[] ids);

    //初始化模板数据
    @InterceptorIgnore(tenantLine = "true")
    int initData(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

}
