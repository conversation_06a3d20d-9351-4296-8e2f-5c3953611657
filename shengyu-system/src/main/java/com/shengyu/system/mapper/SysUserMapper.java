package com.shengyu.system.mapper;

import com.shengyu.common.core.domain.dto.SysUserDto;
import com.shengyu.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper {
    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户名查询用户
     *
     * @param phone 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByPhone(String phone);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    int updateUserPhone(@Param("userName") String userName, @Param("phone") String phone);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public int checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    /**
     * description: 根据关键字查询用户
     * version: 1.0
     * date: 2021/8/24 14:52
     *
     * @param user
     * @return java.util.List<com.shengyu.common.core.domain.entity.SysUser>
     * @author: lwy
     */
    List<SysUser> selectByKeyWord(SysUser user);

    /**
     * description: 根据id集合查询用户
     * version: 1.0
     * date: 2021/8/26 9:30
     *
     * @param userIds
     * @return java.util.List<com.shengyu.common.core.domain.entity.SysUser>
     * @author: lwy
     */
    List<SysUser> selectListByIds(@Param("array") List<Long> userIds);

    SysUserDto selectById(Long userId);

    //批量修改密码
    int batchResetPwd(@Param("array") Long[] ids, @Param("password") String password);

    //更新token
    int updateUserToken(@Param("token") String token, @Param("userName") String userName);

    //获取user对象
    SysUser getUserByToken(@Param("token") String token);

    Integer countUser();

    List<String> selectListByRoleKey(@Param("roleKey") String roleKey, @Param("deptId") Long deptId
            , @Param("tenantId") String tenantId);

    SysUser checkIdCardUnique(String idCard);

    List<SysUser> selectUserByDepts(Long[] deptIds);

    SysUser getUserByPost(@Param("deptId") Long deptId,@Param("postCode") String postCode);

    void updateSyncRemark(@Param("userId") Long userId,@Param("remark") String remark);

    SysUser getUserByPostInRoleDept(@Param("deptId") Long deptId,@Param("tenantId") String tenantId,@Param("postCode") String postCode);

}
