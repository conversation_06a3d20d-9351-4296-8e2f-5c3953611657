package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.system.domain.model.ScheduleParam;
import com.shengyu.system.domain.vo.AccountingVo;
import com.shengyu.system.domain.vo.CountScheduleVo;
import com.shengyu.system.domain.vo.ScheduleDetailsVo;
import com.shengyu.system.domain.vo.SupervisionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 部门管理 数据层
 *
 * <AUTHOR>
 */
public interface SysDeptMapper {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @InterceptorIgnore(tenantLine = "true")
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId            角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门
     *
     * @param deptId 部门ID
     * @return 部门列表
     */
    public List<SysDept> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int checkDeptExistUser(Long deptId);

    /**
     * 校验部门code是否唯一
     *
     * @param deptCode 部门code
     * @return 结果
     */
    public SysDept checkDeptCodeUnique(@Param("deptCode") String deptCode);

    /**
     * 新增部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 修改所在部门正常状态
     *
     * @param deptIds 部门ID组
     */
    public void updateDeptStatusNormal(Long[] deptIds);

    /**
     * 修改子元素关系
     *
     * @param depts 子元素
     * @return 结果
     */
    public int updateDeptChildren(@Param("depts") List<SysDept> depts);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    List<SysDept> getTownList(String tenantId);

    SysDept getDeptIdByAreaId(Long areaId);

    @InterceptorIgnore(tenantLine = "true")
    List<SupervisionVo> supervision(ScheduleParam scheduleParam);

    @InterceptorIgnore(tenantLine = "true")
    List<CountScheduleVo> countSchedule(ScheduleParam scheduleParam);

    @InterceptorIgnore(tenantLine = "true")
    List<ScheduleDetailsVo> scheduleDetails(ScheduleParam scheduleParam);

    @InterceptorIgnore(tenantLine = "true")
    List<SysDept> selectBuildDeptList(ScheduleParam param);

    @InterceptorIgnore(tenantLine = "true")
    List<SysDept> selectChildListForScope(SysDept SysDept);

    @InterceptorIgnore(tenantLine = "true")
    String getAllDeptName(Long deptId);

    @InterceptorIgnore(tenantLine = "true")
    List<SysDept> getAllDept();

    List<SysDept> getAllListByTenantId(String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    String getTownAndVillage(Long deptId);

    @InterceptorIgnore(tenantLine = "true")
    String getAutoName(Long deptId);

    @InterceptorIgnore(tenantLine = "true")
    String getCodeStr(Long deptId);

    @InterceptorIgnore(tenantLine = "true")
    List<Map<String,Object>> getCityDept();

    @InterceptorIgnore(tenantLine = "true")
    List<AccountingVo> getDeptAccountingList(ScheduleParam param);
}
