package com.shengyu.system.mapper;

import com.shengyu.system.domain.SysNotice;
import com.shengyu.system.domain.model.SysNoticeDto;
import com.shengyu.system.domain.vo.SysNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR>
 */
public interface SysNoticeMapper {
    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    public List<SysNotice> selectNoticeList(SysNotice notice);

    //个人消息列表
    List<SysNotice> selectOwnNoticeList(SysNoticeDto notice);

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int insertNotice(SysNotice notice);

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int updateNotice(SysNotice notice);

    /**
     * 批量删除公告
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(Long[] noticeIds);

    //未读消息
    List<SysNoticeVo> selectUnReadNoticeList(@Param("userId") Long userId, @Param("ancestors") String ancestors,
                                             @Param("searchVal") String searchVal);

    //已读消息
    List<SysNoticeVo> selectReadNoticeList(@Param("userId") Long userId, @Param("searchVal") String searchVal);
}
