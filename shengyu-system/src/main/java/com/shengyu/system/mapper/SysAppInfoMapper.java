package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.system.domain.SysAppInfo;

import java.util.List;

/**
 * app管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
public interface SysAppInfoMapper extends BaseMapper<SysAppInfo> {
    /**
     * 查询app管理
     *
     * @param id app管理ID
     * @return app管理
     */
    public SysAppInfo selectSysAppInfoById(Long id);

    /**
     * 查询app管理列表
     *
     * @param sysAppInfo app管理
     * @return app管理集合
     */
    public List<SysAppInfo> selectSysAppInfoList(SysAppInfo sysAppInfo);

    /**
     * 新增app管理
     *
     * @param sysAppInfo app管理
     * @return 结果
     */
    public int insertSysAppInfo(SysAppInfo sysAppInfo);

    /**
     * 修改app管理
     *
     * @param sysAppInfo app管理
     * @return 结果
     */
    public int updateSysAppInfo(SysAppInfo sysAppInfo);

    /**
     * 删除app管理
     *
     * @param id app管理ID
     * @return 结果
     */
    public int deleteSysAppInfoById(Long id);

    /**
     * 批量删除app管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysAppInfoByIds(Long[] ids);
}
