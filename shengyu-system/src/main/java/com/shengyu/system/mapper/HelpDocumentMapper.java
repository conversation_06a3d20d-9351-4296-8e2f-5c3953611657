package com.shengyu.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.common.core.domain.entity.HelpDocument;

import java.util.List;

/**
 * 帮助文档Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface HelpDocumentMapper extends BaseMapper<HelpDocument> {
    /**
     * 查询帮助文档
     *
     * @param id 帮助文档ID
     * @return 帮助文档
     */
    public HelpDocument selectHelpDocumentById(Long id);

    /**
     * 查询帮助文档列表
     *
     * @param helpDocument 帮助文档
     * @return 帮助文档集合
     */
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument);

    /**
     * 新增帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int insertHelpDocument(HelpDocument helpDocument);

    /**
     * 修改帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int updateHelpDocument(HelpDocument helpDocument);

    /**
     * 删除帮助文档
     *
     * @param id 帮助文档ID
     * @return 结果
     */
    public int deleteHelpDocumentById(Long id);

    /**
     * 批量删除帮助文档
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteHelpDocumentByIds(Long[] ids);

    Integer hasChildById(Long id);
}
