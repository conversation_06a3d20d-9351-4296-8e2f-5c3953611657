package com.shengyu.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * app管理对象 sys_app_info
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@ApiModel("app管理")
@TableName(value = "sys_app_info")
public class SysAppInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    @Excel(name = "标题")
    @ApiModelProperty("标题")
    private String title;


    @Excel(name = "内容")
    @ApiModelProperty("内容")
    private String content;


    @Excel(name = "平台")
    @ApiModelProperty("平台")
    private String platform;


    @Excel(name = "版本号")
    @ApiModelProperty("版本号")
    private String version;


    @Excel(name = "更新类型")
    @ApiModelProperty("更新类型")
    private String type;


    @Excel(name = "包路径")
    @ApiModelProperty("包路径")
    private String packageUrl;


    @Excel(name = "服务版本号")
    @ApiModelProperty("服务版本号")
    private String serverVersion;

    private String tenantId;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatform() {
        return platform;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl;
    }

    public String getPackageUrl() {
        return packageUrl;
    }

    public void setServerVersion(String serverVersion) {
        this.serverVersion = serverVersion;
    }

    public String getServerVersion() {
        return serverVersion;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("platform", getPlatform())
                .append("version", getVersion())
                .append("type", getType())
                .append("packageUrl", getPackageUrl())
                .append("serverVersion", getServerVersion())
                .toString();
    }
}
