package com.shengyu.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: SS
 * @Date: 2023/08/21/9:29
 * @Description:
 */
@Data
@ApiModel(value = "组织财务进度明细")
public class ScheduleDetailsVo {

    @ApiModelProperty("乡镇名称")
    private String townName;
    @ApiModelProperty("组织名称")
    private String deptName;
    @ApiModelProperty("组织id")
    private Long deptId;
    @ApiModelProperty("组织编码")
    private String deptCode;
    @ApiModelProperty("是否业务组织")
    private String isBusiness;
    @ApiModelProperty("是否建账")
    private String isBuild;
    @ApiModelProperty("结账进度：文字或日期")
    private String scheduleMonth;
    @ApiModelProperty("建账日期")
    private String buildDate;
    @ApiModelProperty("已结转到月份")
    private String accMonth;
    @ApiModelProperty("组织级别")
    private String nature;

    @ApiModelProperty("月份列表")
    private List<MonthVo> monthList;
}
