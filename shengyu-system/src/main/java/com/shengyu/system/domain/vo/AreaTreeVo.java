package com.shengyu.system.domain.vo;


import com.shengyu.system.domain.SysArea;

import java.util.List;

/**
 * @description: AreaVo <br>
 * @date: 2021/7/7 11:39 <br>
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
public class AreaTreeVo {

    private Long id;

    /**
     * 区域编码
     */
    private String code;

    /**
     * 区域名称
     */
    private String name;

    private String parentCode;

    private Integer level;

    private List<AreaTreeVo> children;

    public AreaTreeVo() {
    }


    public AreaTreeVo(SysArea area) {
        this.id = area.getId();
        this.code = area.getCode();
        this.name = area.getName();
        this.level = area.getLevel();
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AreaTreeVo> getChildren() {
        return children;
    }

    public void setChildren(List<AreaTreeVo> children) {
        this.children = children;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
