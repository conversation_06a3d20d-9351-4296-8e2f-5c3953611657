package com.shengyu.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.shengyu.common.annotation.Excel;
import com.shengyu.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户信息对象 sys_user_info
 *
 * <AUTHOR>
 * @date 2021-07-09
 */
public class SysUserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * userInfo的redisKey
     */
    public static final String USER_INFO_KEY = "userInfo:${userId}:${method}";

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联用户id;sys_user
     */
    @Excel(name = "关联用户编号", type = Excel.Type.IMPORT)
    private Long userId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idcard;

    @Excel(name = "信息编号 *")
    private String userInfoNumber;

    /**
     * 性别，关联字典表
     */
    @Excel(name = "用户性别", dictType = "sys_user_sex", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 所属单位
     */
    @Excel(name = "单位编号", type = Excel.Type.IMPORT)
    private Long deptId;

    /**
     * 部门对象
     */
    @Excel(name = "单位名称", type = Excel.Type.EXPORT)
    @TableField(exist = false)
    private String deptName;
    /**
     * 用户对象
     */
    @Excel(name = "关联用户姓名", type = Excel.Type.EXPORT)
    @TableField(exist = false)
    private String userName;
    /**
     * 职位对象
     */
    @Excel(name = "岗位名称", type = Excel.Type.EXPORT)
    @TableField(exist = false)
    private String postName;

    /**
     * 职位id，关联sys_post
     */
    @Excel(name = "岗位编号", type = Excel.Type.IMPORT)
    private Long postId;

    /**
     * 手机号
     */
    @Excel(name = "联系方式")
    private String phone;

    /**
     * 用户信息类型：学生， 老师，职工。。关联字典表
     */
    @Excel(name = "用户信息类型", dictType = "sys_user_info_type", readConverterExp = "1=学生,2=老师,3=职工")
    private String type;
    /**
     * 学历
     */
    @Excel(name = "用户学历", dictType = "sys_user_info_edu", readConverterExp = "1=本科,2=硕士,3=研究生,4=博士")
    private String edu;
    /**
     * 使用状态
     */
    private String useStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private Long notInRoomId;

    private String tenantId;


    public Long getNotInRoomId() {
        return notInRoomId;
    }

    public void setNotInRoomId(Long notInRoomId) {
        this.notInRoomId = notInRoomId;
    }

    public String getEdu() {
        return edu;
    }

    public void setEdu(String edu) {
        this.edu = edu;
    }

    public String getUserInfoNumber() {
        return userInfoNumber;
    }

    public void setUserInfoNumber(String userInfoNumber) {
        this.userInfoNumber = userInfoNumber;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("name", getName())
                .append("idcard", getIdcard())
                .append("sex", getSex())
                .append("deptId", getDeptId())
                .append("postId", getPostId())
                .append("phone", getPhone())
                .append("type", getType())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
