package com.shengyu.system.domain.model;

import com.shengyu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: SS
 * @Date: 2023/08/19/10:01
 * @Description:
 */
@Data
@ApiModel(value = "进度查询参数-公用")
public class ScheduleParam extends BaseEntity {

    @ApiModelProperty("日期：年(年-月)")
    @NotBlank(message = "日期不可为空")
    private String date;
    @ApiModelProperty("组织id")
    private Long deptId;
    @ApiModelProperty("是否查询次级列表")
    private String isChild;
    @ApiModelProperty("节点层级-(进度监管页面无需传)")
    private Integer level;
    @ApiModelProperty("是否建账")
    private String isBuild;
}
