package com.shengyu.system.domain.vo;

import com.shengyu.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @Author: SS
 * @Date: 2023/08/19/10:08
 * @Description:
 */
@Data
@ApiModel(value = "记账统计-新")
public class AccountingVo {

    @Excel(name = "组织名称")
    @ApiModelProperty("组织名称")
    private String deptName;
    @ApiModelProperty("组织id")
    private Long deptId;
    @NotBlank
    @ApiModelProperty("组织编码")
    private String deptCode;
    @Excel(name = "初始化-总计")
    @ApiModelProperty("初始化-总计")
    private Integer buildNum;
    @Excel(name = "初始化-乡镇级")
    @ApiModelProperty("初始化-乡镇")
    private Integer buildT;
    @Excel(name = "初始化-村级")
    @ApiModelProperty("初始化-组")
    private Integer buildG;
    @Excel(name = "初始化-组级")
    @ApiModelProperty("初始化-村")
    private Integer buildV;

    @Excel(name = "已记账-总计数量")
    @ApiModelProperty("已记账-总计")
    private Integer bookNum;
    @Excel(name = "已记账-乡镇数量")
    @ApiModelProperty("已记账-乡镇")
    private Integer bookT;
    @Excel(name = "已记账-组数量")
    @ApiModelProperty("已记账-组")
    private Integer bookG;
    @Excel(name = "已记账-村数量")
    @ApiModelProperty("已记账-村")
    private Integer bookV;
    @Excel(name = "已记账占比-总计")
    @ApiModelProperty("已记账占比-总计")
    private BigDecimal bookRate;
    @Excel(name = "已记账占比-乡镇")
    @ApiModelProperty("已记账占比-乡镇")
    private BigDecimal bookRateT;
    @Excel(name = "已记账占比-组")
    @ApiModelProperty("已记账占比-组")
    private BigDecimal bookRateG;
    @Excel(name = "已记账占比-村")
    @ApiModelProperty("已记账占比-村")
    private BigDecimal bookRateV;
    @Excel(name = "空月结-总计")
    @ApiModelProperty("空月结-总计")
    private Integer endNum;
    @Excel(name = "空月结-乡镇")
    @ApiModelProperty("空月结-乡镇")
    private Integer endT;
    @Excel(name = "空月结-组")
    @ApiModelProperty("空月结-组")
    private Integer endG;
    @Excel(name = "空月结-村")
    @ApiModelProperty("空月结-村")
    private Integer endV;
    @Excel(name = "空月结占比-总计")
    @ApiModelProperty("空月结占比-总计")
    private BigDecimal endRate;
    @Excel(name = "空月结占比-乡镇")
    @ApiModelProperty("空月结占比-乡镇")
    private BigDecimal endRateT;
    @Excel(name = "空月结占比-组")
    @ApiModelProperty("空月结占比-组")
    private BigDecimal endRateG;
    @Excel(name = "空月结占比-村")
    @ApiModelProperty("空月结占比-村")
    private BigDecimal endRateV;

    @Excel(name = "凭证-总计")
    @ApiModelProperty("凭证-总计")
    private Integer voucherNum;
    @Excel(name = "月均凭证")
    @ApiModelProperty("月均凭证")
    private BigDecimal voucherMon;
    @Excel(name = "乡镇凭证")
    @ApiModelProperty("初始化-乡镇")
    private Integer voucherT;
    @Excel(name = "村级凭证")
    @ApiModelProperty("初始化-组")
    private Integer voucherG;
    @Excel(name = "组级凭证")
    @ApiModelProperty("初始化-村")
    private Integer voucherV;

    @Excel(name = "资产-总计")
    @ApiModelProperty("资产-总计")
    private Integer assetNum;
    @Excel(name = "村均资产条数")
    @ApiModelProperty("村均资产条数")
    private BigDecimal assetMon;
    @Excel(name = "资产-乡镇")
    @ApiModelProperty("初始化-乡镇")
    private Integer assetT;
    @Excel(name = "资产-村级")
    @ApiModelProperty("初始化-组")
    private Integer assetG;
    @Excel(name = "资产-组级")
    @ApiModelProperty("初始化-村")
    private Integer assetV;

    @Excel(name = "合同-总计")
    @ApiModelProperty("合同-总计")
    private Integer contractNum;
    @Excel(name = "村均合同数")
    @ApiModelProperty("村均合同数")
    private BigDecimal contractMon;
    @Excel(name = "合同-乡镇级")
    @ApiModelProperty("合同-乡镇")
    private Integer contractT;
    @Excel(name = "合同-村级")
    @ApiModelProperty("合同-组")
    private Integer contractG;
    @Excel(name = "合同-组级")
    @ApiModelProperty("合同-村")
    private Integer contractV;

    private Integer level;
}
