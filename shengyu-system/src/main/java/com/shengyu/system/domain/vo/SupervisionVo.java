package com.shengyu.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author: SS
 * @Date: 2023/08/19/10:08
 * @Description:
 */
@Data
@ApiModel(value = "进度监管vo")
public class SupervisionVo {

    @ApiModelProperty("组织名称")
    private String deptName;
    @ApiModelProperty("组织id")
    private Long deptId;
    @NotBlank
    @ApiModelProperty("组织编码")
    private String deptCode;
    @ApiModelProperty("是否业务组织")
    private String isBusiness;
    @ApiModelProperty("组织数量-乡镇")
    private Integer childNumT;
    @ApiModelProperty("组织数量-村")
    private Integer childNumV;
    @ApiModelProperty("组织数量-组")
    private Integer childNumG;
    @ApiModelProperty("初始化-乡镇")
    private Integer buildT;
    @ApiModelProperty("初始化-村")
    private Integer buildG;
    @ApiModelProperty("初始化-组")
    private Integer buildV;
//    @ApiModelProperty("未做账数量")
//    private Integer noDone;

    private Integer level;

    @ApiModelProperty("月份列表")
    private List<MonthVo> monthList;
}
