package com.shengyu.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shengyu.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 省市县管理对象 sys_area
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
public class SysArea {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 区域编码
     */
    @Excel(name = "区域编码")
    private String code;

    /**
     * 区域名称
     */
    @Excel(name = "区域名称")
    private String name;

    /**
     * 父级区域编码
     */
    @Excel(name = "父级区域编码")
    private String parentCode;

    /**
     * 区域简称
     */
    @Excel(name = "区域简称")
    private String simpleName;

    /**
     * 区域级别
     */
    @Excel(name = "区域级别")
    private int level;

    /**
     * 该城市编码
     */
    @Excel(name = "该城市编码")
    private String cityCode;

    /**
     * 邮编
     */
    @Excel(name = "邮编")
    private String zipCode;

    /**
     * 组合全称
     */
    @Excel(name = "组合全称")
    private String merName;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private Long lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private Long lat;

    /**
     * 区域拼音
     */
    @Excel(name = "区域拼音")
    private String pinYin;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private Long parentId;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getMerName() {
        return merName;
    }

    public void setLng(Long lng) {
        this.lng = lng;
    }

    public Long getLng() {
        return lng;
    }

    public void setLat(Long lat) {
        this.lat = lat;
    }

    public Long getLat() {
        return lat;
    }

    public void setPinYin(String pinYin) {
        this.pinYin = pinYin;
    }

    public String getPinYin() {
        return pinYin;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("code", getCode())
                .append("name", getName())
                .append("parentCode", getParentCode())
                .append("simpleName", getSimpleName())
                .append("level", getLevel())
                .append("cityCode", getCityCode())
                .append("zipCode", getZipCode())
                .append("merName", getMerName())
                .append("lng", getLng())
                .append("lat", getLat())
                .append("pinYin", getPinYin())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
