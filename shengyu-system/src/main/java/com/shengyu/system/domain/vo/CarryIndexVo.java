package com.shengyu.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: SS
 * @Date: 2023/08/21/14:21
 * @Description:
 */
@Data
@ApiModel(value = "结转展示vo")
public class CarryIndexVo {

    @ApiModelProperty("凭证是否未审核")
    private String isNotAudit;
    @ApiModelProperty("凭证是否未记账")
    private String isNotBook;
    @ApiModelProperty("凭证是否未平衡")
    private String isNotBalance;
    @ApiModelProperty("凭证是否复核")
    private String isNotReview;
}
