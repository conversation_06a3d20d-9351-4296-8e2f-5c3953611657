package com.shengyu.system.domain.model;

import com.shengyu.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: SS
 * @Date: 2023/12/20/10:07
 * @Description:
 */
@Data
@ApiModel(value = "首页参数")
public class IndexParam extends BaseEntity {

    @ApiModelProperty("组织机构id")
    private Long deptId;
    @ApiModelProperty("日期")
    private String date;

    private String countType;

    private String nature;

    private String tableName;

    private String date1;

    private String date2;

    private String startTime;

    private String endTime;

    private Integer level;
}
