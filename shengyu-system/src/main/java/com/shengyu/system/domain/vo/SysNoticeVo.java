package com.shengyu.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 通知公告表 sys_notice
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "消息对象")
public class SysNoticeVo {

    @ApiModelProperty("消息id")
    private Long noticeId;
    /**
     * 公告标题
     */
    @ApiModelProperty("公告标题")
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    @ApiModelProperty("公告类型（1通知 2公告）")
    private String noticeType;

    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    private String noticeContent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;
}
