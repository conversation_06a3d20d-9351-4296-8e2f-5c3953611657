package com.shengyu.system.domain.model;

import com.shengyu.common.annotation.Excel;
import com.shengyu.common.constant.ScheduleConstants;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: SS
 * @Date: 2023/06/25/15:11
 * @Description:
 */
@Data
public class SysJobModel implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long jobId;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    private String jobName;

    /**
     * 任务组名
     */
    @Excel(name = "任务组名")
    private String jobGroup;

    /**
     * 调用目标字符串
     */
    @Excel(name = "调用目标字符串")
    private String invokeTarget;

    /**
     * cron执行表达式
     */
    @Excel(name = "执行表达式 ")
    private String cronExpression;

    /**
     * cron计划策略
     */
    @Excel(name = "计划策略 ", readConverterExp = "0=默认,1=立即触发执行,2=触发一次执行,3=不触发立即执行")
    private String misfirePolicy = ScheduleConstants.MISFIRE_DEFAULT;

    /**
     * 是否并发执行（1允许 0禁止）
     */
    @Excel(name = "并发执行", readConverterExp = "1=允许,0=禁止")
    private String concurrent;

    /**
     * 任务状态（1正常 0暂停）
     */
    @Excel(name = "任务状态", readConverterExp = "1=正常,0=暂停")
    private String status;
}
