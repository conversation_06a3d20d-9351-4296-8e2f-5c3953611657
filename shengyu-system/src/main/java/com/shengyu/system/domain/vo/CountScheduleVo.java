package com.shengyu.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: SS
 * @Date: 2023/08/21/9:12
 * @Description:
 */
@Data
@ApiModel("组织进度统计")
public class CountScheduleVo {

    @ApiModelProperty("组织名称")
    private String deptName;
    @ApiModelProperty("组织id")
    private Long deptId;
    @ApiModelProperty("组织编码")
    private String deptCode;
    @ApiModelProperty("是否业务组织")
    private String isBusiness;
    @ApiModelProperty("节点层级")
    private Integer level;
    @ApiModelProperty("组织数量-乡镇")
    private Integer childNumT;
    @ApiModelProperty("组织数量-村")
    private Integer childNumV;
    @ApiModelProperty("组织数量-组")
    private Integer childNumG;
    @ApiModelProperty("初始化-乡镇")
    private Integer buildT;
    @ApiModelProperty("初始化-村")
    private Integer buildG;
    @ApiModelProperty("初始化-组")
    private Integer buildV;
    @ApiModelProperty("未初始化-乡镇")
    private Integer noBuildT;
    @ApiModelProperty("未初始化-村")
    private Integer noBuildG;
    @ApiModelProperty("未初始化-组")
    private Integer noBuildV;
    @ApiModelProperty("大于查询会计期间")
    private Integer greaterAccDate;
}
