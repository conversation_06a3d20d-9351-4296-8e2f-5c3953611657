package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysNoticeDept;
import com.shengyu.system.mapper.SysNoticeDeptMapper;
import com.shengyu.system.service.ISysNoticeDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 发布区域Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Service
public class SysNoticeDeptServiceImpl extends ServiceImpl<SysNoticeDeptMapper, SysNoticeDept> implements ISysNoticeDeptService {
    @Autowired
    private SysNoticeDeptMapper sysNoticeDeptMapper;

    /**
     * 查询发布区域
     *
     * @param id 发布区域ID
     * @return 发布区域
     */
    @Override
    public SysNoticeDept selectSysNoticeDeptById(Long id) {
        return sysNoticeDeptMapper.selectSysNoticeDeptById(id);
    }

    /**
     * 查询发布区域列表
     *
     * @param sysNoticeDept 发布区域
     * @return 发布区域
     */
    @Override
    public List<SysNoticeDept> selectSysNoticeDeptList(SysNoticeDept sysNoticeDept) {
        return sysNoticeDeptMapper.selectSysNoticeDeptList(sysNoticeDept);
    }

    /**
     * 新增发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    @Override
    public int insertSysNoticeDept(SysNoticeDept sysNoticeDept) {
        return sysNoticeDeptMapper.insertSysNoticeDept(sysNoticeDept);
    }

    /**
     * 修改发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    @Override
    public int updateSysNoticeDept(SysNoticeDept sysNoticeDept) {
        return sysNoticeDeptMapper.updateSysNoticeDept(sysNoticeDept);
    }

    @Override
    public int deleteSysNoticeDeptByNoticeId(Long noticeId) {
        return sysNoticeDeptMapper.deleteSysNoticeDeptByNoticeId(noticeId);
    }

    @Override
    public int deleteSysNoticeDeptByNoticeIds(Long[] noticeIds) {
        return sysNoticeDeptMapper.deleteSysNoticeDeptByNoticeIds(noticeIds);
    }

    @Override
    public void addNoticeDept(Long noticeId, Long[] deptIds) {
        sysNoticeDeptMapper.deleteSysNoticeDeptByNoticeId(noticeId);
        List<SysNoticeDept> insBatch = new ArrayList<>();
        for (Long deptId : deptIds) {
            SysNoticeDept noticeDept = new SysNoticeDept();
            noticeDept.setNoticeId(noticeId);
            noticeDept.setDeptId(deptId);
            insBatch.add(noticeDept);
        }
        this.saveBatch(insBatch);
    }

    @Override
    public Long[] selectSysNoticeDeptByNoticeId(Long noticeId) {
        SysNoticeDept noticeDept = new SysNoticeDept();
        noticeDept.setNoticeId(noticeId);
        List<SysNoticeDept> nList = sysNoticeDeptMapper.selectSysNoticeDeptList(noticeDept);
        if (StringUtils.isEmpty(nList)) {
            return null;
        }
        return nList.stream().map(SysNoticeDept::getDeptId).toArray(Long[]::new);
    }
}
