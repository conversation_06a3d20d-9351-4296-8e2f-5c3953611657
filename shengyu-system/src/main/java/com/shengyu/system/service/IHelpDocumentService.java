package com.shengyu.system.service;


import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.HelpDocument;

import java.util.List;

/**
 * 帮助文档Service接口
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface IHelpDocumentService {
    /**
     * 查询帮助文档
     *
     * @param id 帮助文档ID
     * @return 帮助文档
     */
    public HelpDocument selectHelpDocumentById(Long id);

    /**
     * 查询帮助文档列表
     *
     * @param helpDocument 帮助文档
     * @return 帮助文档集合
     */
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument);

    /**
     * 新增帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int insertHelpDocument(HelpDocument helpDocument);

    /**
     * 修改帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int updateHelpDocument(HelpDocument helpDocument);

    /**
     * 批量删除帮助文档
     *
     * @param ids 需要删除的帮助文档ID
     * @return 结果
     */
    public int deleteHelpDocumentByIds(Long[] ids);

    /**
     * 删除帮助文档信息
     *
     * @param id 帮助文档ID
     * @return 结果
     */
    public int deleteHelpDocumentById(Long id);

    //构建树
    List<TreeSelect> buildTreeSelect(List<HelpDocument> ds);

    //是否有子节点
    boolean hasChildById(Long id);
}
