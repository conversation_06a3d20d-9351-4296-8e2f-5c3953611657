package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysAppInfo;
import com.shengyu.system.mapper.SysAppInfoMapper;
import com.shengyu.system.service.ISysAppInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * app管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Service
public class SysAppInfoServiceImpl extends ServiceImpl<SysAppInfoMapper, SysAppInfo> implements ISysAppInfoService {
    @Autowired
    private SysAppInfoMapper sysAppInfoMapper;

    /**
     * 查询app管理
     *
     * @param id app管理ID
     * @return app管理
     */
    @Override
    public SysAppInfo selectSysAppInfoById(Long id) {
        return sysAppInfoMapper.selectSysAppInfoById(id);
    }

    /**
     * 查询app管理列表
     *
     * @param sysAppInfo app管理
     * @return app管理
     */
    @Override
    public List<SysAppInfo> selectSysAppInfoList(SysAppInfo sysAppInfo) {
        return sysAppInfoMapper.selectSysAppInfoList(sysAppInfo);
    }

    /**
     * 新增app管理
     *
     * @param sysAppInfo app管理
     * @return 结果
     */
    @Override
    public int insertSysAppInfo(SysAppInfo sysAppInfo) {
        if (StringUtils.isEmpty(sysAppInfo.getTenantId())) {
            sysAppInfo.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        return sysAppInfoMapper.insertSysAppInfo(sysAppInfo);
    }

    /**
     * 修改app管理
     *
     * @param sysAppInfo app管理
     * @return 结果
     */
    @Override
    public int updateSysAppInfo(SysAppInfo sysAppInfo) {
        return sysAppInfoMapper.updateSysAppInfo(sysAppInfo);
    }

    /**
     * 批量删除app管理
     *
     * @param ids 需要删除的app管理ID
     * @return 结果
     */
    @Override
    public int deleteSysAppInfoByIds(Long[] ids) {
        return sysAppInfoMapper.deleteSysAppInfoByIds(ids);
    }

    /**
     * 删除app管理信息
     *
     * @param id app管理ID
     * @return 结果
     */
    @Override
    public int deleteSysAppInfoById(Long id) {
        return sysAppInfoMapper.deleteSysAppInfoById(id);
    }

    @Override
    public SysAppInfo getLastApp() {
        List<SysAppInfo> appList = sysAppInfoMapper.selectList(new QueryWrapper<SysAppInfo>()
                .eq("tenant_id", SecurityUtils.getLoginUser().getTenantId())
                .orderByDesc("version"));
        if (StringUtils.isNotEmpty(appList)) {
            return appList.get(0);
        }
        return null;
    }
}
