package com.shengyu.system.service;

import com.shengyu.system.domain.SysNotice;
import com.shengyu.system.domain.model.NoticeAndDeptModel;
import com.shengyu.system.domain.model.SysNoticeDto;
import com.shengyu.system.domain.vo.SysNoticeVo;

import java.util.List;
import java.util.Map;

/**
 * 公告 服务层
 *
 * <AUTHOR>
 */
public interface ISysNoticeService {
    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    public List<SysNotice> selectNoticeList(SysNotice notice);

    //个人消息列表
    public List<SysNotice> selectOwnNoticeList(SysNoticeDto notice);

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int insertNotice(NoticeAndDeptModel notice);

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int updateNotice(NoticeAndDeptModel notice);

    /**
     * 删除公告信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(Long[] noticeIds);

    //未读消息
    List<SysNoticeVo> selectUnReadNoticeList(Long userId, String ancestors, Map<String, String> map);

    //已读消息
    List<SysNoticeVo> selectReadNoticeList(Long userId, Map<String, String> map);

    //读消息
    boolean readMsg(String[] ids);
}
