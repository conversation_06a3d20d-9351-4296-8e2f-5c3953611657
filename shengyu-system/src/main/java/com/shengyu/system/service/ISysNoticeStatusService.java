package com.shengyu.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.system.domain.SysNoticeStatus;

import java.util.List;

/**
 * 消息状态Service接口
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
public interface ISysNoticeStatusService extends IService<SysNoticeStatus> {
    /**
     * 查询消息状态
     *
     * @param id 消息状态ID
     * @return 消息状态
     */
    public SysNoticeStatus selectSysNoticeStatusById(Long id);

    /**
     * 查询消息状态列表
     *
     * @param sysNoticeStatus 消息状态
     * @return 消息状态集合
     */
    public List<SysNoticeStatus> selectSysNoticeStatusList(SysNoticeStatus sysNoticeStatus);

    /**
     * 新增消息状态
     *
     * @param sysNoticeStatus 消息状态
     * @return 结果
     */
    public int insertSysNoticeStatus(SysNoticeStatus sysNoticeStatus);

    /**
     * 修改消息状态
     *
     * @param sysNoticeStatus 消息状态
     * @return 结果
     */
    public int updateSysNoticeStatus(SysNoticeStatus sysNoticeStatus);

    /**
     * 批量删除消息状态
     *
     * @param ids 需要删除的消息状态ID
     * @return 结果
     */
    public int deleteSysNoticeStatusByIds(Long[] ids);

    /**
     * 删除消息状态信息
     *
     * @param id 消息状态ID
     * @return 结果
     */
    public int deleteSysNoticeStatusById(Long id);
}
