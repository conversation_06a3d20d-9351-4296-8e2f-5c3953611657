package com.shengyu.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.system.domain.SysNoticeDept;

import java.util.List;

/**
 * 发布区域Service接口
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
public interface ISysNoticeDeptService extends IService<SysNoticeDept> {
    /**
     * 查询发布区域
     *
     * @param id 发布区域ID
     * @return 发布区域
     */
    public SysNoticeDept selectSysNoticeDeptById(Long id);

    /**
     * 查询发布区域列表
     *
     * @param sysNoticeDept 发布区域
     * @return 发布区域集合
     */
    public List<SysNoticeDept> selectSysNoticeDeptList(SysNoticeDept sysNoticeDept);

    /**
     * 新增发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    public int insertSysNoticeDept(SysNoticeDept sysNoticeDept);

    /**
     * 修改发布区域
     *
     * @param sysNoticeDept 发布区域
     * @return 结果
     */
    public int updateSysNoticeDept(SysNoticeDept sysNoticeDept);

    /**
     * 批量删除发布区域
     *
     * @return 结果
     */
    public int deleteSysNoticeDeptByNoticeId(Long noticeId);

    int deleteSysNoticeDeptByNoticeIds(Long[] noticeId);

    //添加发布区域
    void addNoticeDept(Long noticeId, Long[] deptIds);

    //查询发布区域
    Long[] selectSysNoticeDeptByNoticeId(Long noticeId);
}
