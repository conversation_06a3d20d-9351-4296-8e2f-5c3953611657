package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shengyu.common.annotation.DataScope;
import com.shengyu.common.config.RedisClient;
import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.SocketMsgUtil;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.system.domain.SysNotice;
import com.shengyu.system.domain.SysNoticeStatus;
import com.shengyu.system.domain.model.NoticeAndDeptModel;
import com.shengyu.system.domain.model.SysNoticeDto;
import com.shengyu.system.domain.vo.SysNoticeVo;
import com.shengyu.system.mapper.SysNoticeMapper;
import com.shengyu.system.service.ISysNoticeDeptService;
import com.shengyu.system.service.ISysNoticeService;
import com.shengyu.system.service.ISysNoticeStatusService;
import com.shengyu.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {
    @Autowired
    private SysNoticeMapper noticeMapper;
    @Autowired
    private ISysNoticeStatusService statusService;
    @Autowired
    private ISysNoticeDeptService noticeDeptService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ScheduledExecutorService executor;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysNotice> selectNoticeList(SysNotice notice) {
        return noticeMapper.selectNoticeList(notice);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<SysNotice> selectOwnNoticeList(SysNoticeDto notice) {
        return noticeMapper.selectOwnNoticeList(notice);
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertNotice(NoticeAndDeptModel notice) {
        int res = noticeMapper.insertNotice(notice);
        if (res > 0 && StringUtils.isNotEmpty(notice.getDeptIds())) {
            notice.setIsAll("0");
            noticeDeptService.addNoticeDept(notice.getNoticeId(), notice.getDeptIds());
            List<SysUser> users = userService.selectUserByDepts(notice.getDeptIds());
            RedisClient redisClient = SpringUtils.getBean(RedisClient.class);
            for (SysUser user : users) {
                if (user.getUserName().equals(SecurityUtils.getUsername())) {
                    continue;
                }
                SocketMsg msg = SocketMsgUtil.createMsg("notice", String.valueOf(user.getUserId()), "您有未读消息!");
                executor.schedule(sendMsg(msg, redisClient), 1, TimeUnit.MILLISECONDS);//消息推送
            }
        }
        return res;
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateNotice(NoticeAndDeptModel notice) {
        int res = noticeMapper.updateNotice(notice);
        if (res > 0 && StringUtils.isNotEmpty(notice.getDeptIds())) {
            notice.setIsAll("0");
            noticeDeptService.addNoticeDept(notice.getNoticeId(), notice.getDeptIds());
        }
        return res;
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteNoticeById(Long noticeId) {
        noticeDeptService.deleteSysNoticeDeptByNoticeId(noticeId);
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteNoticeByIds(Long[] noticeIds) {
        noticeDeptService.deleteSysNoticeDeptByNoticeIds(noticeIds);
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<SysNoticeVo> selectUnReadNoticeList(Long userId, String ancestors, Map<String, String> map) {
        String searchVal = map == null ? null : map.get("searchVal");
        return noticeMapper.selectUnReadNoticeList(userId, ancestors, searchVal);
    }

    @Override
    public List<SysNoticeVo> selectReadNoticeList(Long userId, Map<String, String> map) {
        String searchVal = map == null ? null : map.get("searchVal");
        return noticeMapper.selectReadNoticeList(userId, searchVal);
    }

    @Override
    public boolean readMsg(String[] ids) {
        List<SysNoticeStatus> existsData = statusService.list(
                new QueryWrapper<SysNoticeStatus>().in("notice_id", ids)
                        .eq("user_id", SecurityUtils.getLoginUser().getUser().getUserId()));
        List<Long> existsIds = existsData.stream().map(SysNoticeStatus::getNoticeId).collect(Collectors.toList());
        List<SysNoticeStatus> data = new ArrayList<>();
        for (String str : ids) {
            if (existsIds.contains(Long.parseLong(str))) {
                continue;
            }
            SysNoticeStatus s = new SysNoticeStatus();
            s.setNoticeId(Long.parseLong(str));
            s.setCreateTime(DateUtils.getNowDate());
            s.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
            data.add(s);
        }
        if (StringUtils.isNotEmpty(data)) {
            return statusService.saveBatch(data);
        }
        return true;
    }

    private static TimerTask sendMsg(final SocketMsg params, final RedisClient redisClient) {
        return new TimerTask() {
            @Override
            public void run() {
                redisClient.sendMessage(params);
            }
        };
    }
}
