package com.shengyu.system.service;

import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.system.domain.SysUserInfo;

import java.util.List;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @date 2021-07-09
 */
public interface ISysUserInfoService {
    /**
     * 查询用户信息
     *
     * @param id 用户信息ID
     * @return 用户信息
     */
    public SysUserInfo selectSysUserInfoById(Long id);

    /**
     * 查询用户信息列表
     *
     * @param sysUserInfo 用户信息
     * @return 用户信息集合
     */
    public List<SysUserInfo> selectSysUserInfoList(SysUserInfo sysUserInfo);

    /**
     * 新增用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    public int insertSysUserInfo(SysUserInfo sysUserInfo);

    /**
     * 修改用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    public int updateSysUserInfo(SysUserInfo sysUserInfo);

    /**
     * 批量删除用户信息
     *
     * @param ids 需要删除的用户信息ID
     * @return 结果
     */
    public int deleteSysUserInfoByIds(Long[] ids);

    /**
     * 删除用户信息信息
     *
     * @param id 用户信息ID
     * @return 结果
     */
    public int deleteSysUserInfoById(Long id);

    /**
     * 导入用户数据
     *
     * @param userInfoList    用户信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param allInSupport    支持整体导入
     * @param loginUser       操作用户
     * @return 结果
     */
    AjaxResult importUserInfo(List<SysUserInfo> userInfoList, boolean isUpdateSupport, LoginUser loginUser, boolean allInSupport);

    /**
     * description: 根据关键字查询人员信息
     * version: 1.0
     * date: 2021/8/24 14:26
     *
     * @param userInfo
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: lwy
     */
    List<SysUserInfo> selectByNameNumber(SysUserInfo userInfo);


}
