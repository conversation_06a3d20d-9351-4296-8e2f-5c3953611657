package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.annotation.DataScope;
import com.shengyu.common.constant.HttpStatus;
import com.shengyu.common.constant.RedisConstants;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.ImportDataErrorUtil;
import com.shengyu.common.utils.MessageUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.bean.ImportDataErrorInfo;
import com.shengyu.system.domain.SysUserInfo;
import com.shengyu.system.mapper.SysUserInfoMapper;
import com.shengyu.system.service.ISysUserInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-09
 */
@Service
public class SysUserInfoServiceImpl extends ServiceImpl<SysUserInfoMapper, SysUserInfo> implements ISysUserInfoService {
    @Resource
    private SysUserInfoMapper sysUserInfoMapper;
    @Resource
    private RedisCache redisCache;

    /**
     * 查询用户信息
     *
     * @param id 用户信息ID
     * @return 用户信息
     */
    @Override
    public SysUserInfo selectSysUserInfoById(Long id) {
        return sysUserInfoMapper.selectSysUserInfoById(id);
    }

    /**
     * 查询用户信息列表
     *
     * @param sysUserInfo 用户信息
     * @return 用户信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserInfo> selectSysUserInfoList(SysUserInfo sysUserInfo) {
        return sysUserInfoMapper.selectSysUserInfoList(sysUserInfo);
    }

    /**
     * 新增用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    @Override
    public int insertSysUserInfo(SysUserInfo sysUserInfo) {
        return sysUserInfoMapper.insertSysUserInfo(sysUserInfo);
    }

    /**
     * 修改用户信息
     *
     * @param sysUserInfo 用户信息
     * @return 结果
     */
    @Override
    public int updateSysUserInfo(SysUserInfo sysUserInfo) {
        sysUserInfo.setUpdateTime(DateUtils.getNowDate());
        return sysUserInfoMapper.updateSysUserInfo(sysUserInfo);
    }

    /**
     * 批量删除用户信息
     *
     * @param ids 需要删除的用户信息ID
     * @return 结果
     */
    @Override
    public int deleteSysUserInfoByIds(Long[] ids) {
        return sysUserInfoMapper.deleteSysUserInfoByIds(ids);
    }

    /**
     * 删除用户信息信息
     *
     * @param id 用户信息ID
     * @return 结果
     */
    @Override
    public int deleteSysUserInfoById(Long id) {
        return sysUserInfoMapper.deleteSysUserInfoById(id);
    }

    /**
     * 导入用户数据
     *
     * @param userInfoList    用户信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param loginUser       操作用户
     * @return 结果
     */
    @Override
    public AjaxResult importUserInfo(List<SysUserInfo> userInfoList, boolean isUpdateSupport, LoginUser loginUser, boolean allInSupport) {
        if (StringUtils.isNull(userInfoList) || userInfoList.size() == 0) {
            throw new CustomException("导入用户数据不能为空！");
        }
        int failureNum = 0;
        List<SysUserInfo> errorList = new ArrayList();
        Map<Integer, String> errorMap = new HashMap();
        //标题行去除掉，所以初始值是1
        int count = 1;
        List<SysUserInfo> saveList = new ArrayList();
        List<SysUserInfo> updateList = new ArrayList();
        // 验证是否存在这个用户信息
        LambdaQueryWrapper<SysUserInfo> wrapper = new LambdaQueryWrapper();
        for (SysUserInfo info : userInfoList) {
            try {
                if (StringUtils.isEmpty(info.getUserInfoNumber())) {
                    throw new CustomException("信息编号不能为空！");
                }
                wrapper.clear();
                wrapper.eq(SysUserInfo::getUserInfoNumber, info.getUserInfoNumber());
                //未删除的
                wrapper.eq(SysUserInfo::getDelFlag, "0");
                List<SysUserInfo> checkList = sysUserInfoMapper.selectList(wrapper);
                if (checkList.size() < 1) {
                    info.setCreateBy(loginUser.getUsername());
                    info.setCreateTime(new Date());
                    saveList.add(info);
                    if (allInSupport) {
                        //如果是整体导入的话，导出错误数据时也需要将正确的一并提供给用户
                        errorList.add(info);
                        errorMap.put(count, "");
                        count++;
                    }
                } else if (checkList.size() == 1 && isUpdateSupport) {
                    SysUserInfo myInfo = checkList.get(0);
                    info.setId(myInfo.getId());
                    info.setUpdateTime(new Date());
                    info.setUpdateBy(loginUser.getUsername());
                    updateList.add(info);
                } else {
                    errorList.add(info);
                    errorMap.put(count, "用户信息 " + info.getName() + " 已存在");
                    count++;
                    failureNum++;
                }
            } catch (Exception e) {
                e.printStackTrace();
                errorList.add(info);
                errorMap.put(count, e.getMessage());
                count++;
                failureNum++;
            }
        }
        if (failureNum > 0) {
            ImportDataErrorInfo errorInfo = new ImportDataErrorInfo();
            errorInfo.setErrorList(errorList);
            errorInfo.setErrorMap(errorMap);
            errorInfo.setFailureNum(failureNum);
            errorInfo.setTotalNum(userInfoList.size());
            errorInfo.setInsertNum(0);
            errorInfo.setUpdateNum(0);
            errorInfo.setLoginUser(loginUser);
            String redisKey = RedisConstants.IMPORT_ERROR_DATA.replace("${bizName}", "userInfo")
                    .replace("${userId}", loginUser.getUser().getUserId().toString());
            errorInfo.setRedisKey(redisKey);
            if (!allInSupport) {
                this.saveBatch(saveList);
                this.updateBatchById(updateList);
                errorInfo.setUpdateNum(updateList.size());
                errorInfo.setInsertNum(saveList.size());
            }
            return AjaxResult.error(HttpStatus.OPERATION_FAILED, ImportDataErrorUtil.setError(errorInfo));
        } else {
            this.saveBatch(saveList);
            this.updateBatchById(updateList);
            String message = MessageUtils.message("user.import.success").replace("${total}", userInfoList.size() + "")
                    .replace("${update}", updateList.size() + "")
                    .replace("${save}", saveList.size() + "");
            return AjaxResult.success(message);
        }
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserInfo> selectByNameNumber(SysUserInfo userInfo) {
        return sysUserInfoMapper.selectByNameNumber(userInfo);
    }


}
