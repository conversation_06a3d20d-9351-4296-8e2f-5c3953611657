package com.shengyu.system.service.impl;

import com.shengyu.common.annotation.DataScope;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysRole;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.model.ScheduleParam;
import com.shengyu.system.domain.vo.AccountingVo;
import com.shengyu.system.domain.vo.CountScheduleVo;
import com.shengyu.system.domain.vo.ScheduleDetailsVo;
import com.shengyu.system.domain.vo.SupervisionVo;
import com.shengyu.system.mapper.SysDeptMapper;
import com.shengyu.system.mapper.SysRoleMapper;
import com.shengyu.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl implements ISysDeptService {
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 查询组织管理数据
     *
     * @param dept 组织信息
     * @return 组织信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 组织列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<>();
        Set<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        // 按父级分组
        Map<Long, List<SysDept>> groupByParentIdDepts = depts.stream().filter(dept -> dept.getParentId() != null)
                .collect(Collectors.groupingBy(SysDept::getParentId));
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(groupByParentIdDepts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 组织列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询组织树信息
     *
     * @param roleId 角色ID
     * @return 选中组织列表
     */
    @Override
    public List<Integer> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据组织ID查询信息
     *
     * @param deptId 组织ID
     * @return 组织信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子组织（正常状态）
     *
     * @param deptId 组织ID
     * @return 子组织数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 组织ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 查询组织是否存在用户
     *
     * @param deptId 组织ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 校验组织名称是否唯一
     *
     * @param dept 组织信息
     * @return 结果
     */
    @Override
    public String checkDeptCodeUnique(SysDept dept) {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptCodeUnique(dept.getDeptCode());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增保存组织信息
     *
     * @param dept 组织信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDept dept) {
        SysDept info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (info != null) {
            if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
                throw new CustomException("父节点组织停用，不允许新增");
            }
            if (UserConstants.NORMAL.equals(info.getIsBusiness())) {
                throw new CustomException("父节点组织为业务组织(最小子节点)，不允许新增");
            }
        }
        if (info != null) {
            dept.setAncestors(info.getAncestors());
            dept.setLevel(info.getLevel() + 1);
            if (info.getLevel() > 2) {//父节点为县级节点以下，则县级节点以下节点都为同一租户
                dept.setTenantId(info.getTenantId());
            }
            if (StringUtils.isNull(dept.getAreaId())) {
                dept.setAreaId(info.getAreaId());
            }
        } else {
            dept.setAncestors("0");
            dept.setLevel(1);
        }
        if (UserConstants.NORMAL.equals(dept.getIsBusiness()) && dept.getLevel() <= 4) {
            throw new CustomException("最小业务组织节点必须为村级(或以下)");
        }
        if (StringUtils.isEmpty(dept.getTenantId())) {
            dept.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        if (StringUtils.isEmpty(dept.getOrderNum())) {
            dept.setOrderNum(dept.getDeptCode());
        }
        int result = deptMapper.insertDept(dept);
        dept.setAncestors(dept.getAncestors() + "," + dept.getDeptId());
        deptMapper.updateDept(dept);//节点加入自己
        return result;
    }

    /**
     * 修改保存组织信息
     *
     * @param dept 组织信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept) {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        if (StringUtils.isNotNull(newParentDept) && UserConstants.NORMAL.equals(newParentDept.getIsBusiness())) {
            throw new CustomException("父节点组织为业务组织(最小子节点)，不允许新增");
        }
        if (StringUtils.isNotNull(newParentDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + dept.getDeptId();
            dept.setAncestors(newAncestors);
            SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
            if (StringUtils.isNotNull(oldDept)) {
                String oldAncestors = oldDept.getAncestors();
                updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors, newParentDept.getAreaId());
            }
        }
        if (StringUtils.isNotNull(newParentDept)) {
            dept.setLevel(newParentDept.getLevel() + 1);
            dept.setAreaId(newParentDept.getAreaId());
        } else {
            dept.setLevel(1);
        }
        if (UserConstants.NORMAL.equals(dept.getIsBusiness()) && dept.getLevel() <= 4) {
            throw new CustomException("最小业务组织节点必须为村级(或以下)");
        }
        int result = deptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus())) {
            // 如果该组织是启用状态，则启用该组织的所有上级组织
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该组织的父级组织状态
     *
     * @param dept 当前组织
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        if (StringUtils.isNotEmpty(ancestors)) {
            Long[] deptIds = Convert.toLongArray(ancestors);
            deptMapper.updateDeptStatusNormal(deptIds);
        }
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的组织ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors, Long areaId) {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            child.setAreaId(areaId);
        }
        if (children.size() > 0) {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除组织管理信息
     *
     * @param deptId 组织ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId) {
        int res = deptMapper.deleteDeptById(deptId);
        return res;
    }

    @Override
    public List<SysDept> selectChildrenDeptById(Long deptId) {
        return deptMapper.selectChildrenDeptById(deptId);
    }

    @Override
    @Transactional
    public String importDept(List<SysDept> list, String operName) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new CustomException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysDept info : list) {
            try {
                // 验证是否存在这个用户
                SysDept pD = deptMapper.checkDeptCodeUnique(String.valueOf(info.getParentCode()));
                if (StringUtils.isNull(pD)) {
                    failureNum++;
                    failureMsg.append("父节点组织不存在，不允许新增（注意导入数据的排序，父节点在前，子节点在父节点后）!");
                    continue;
                }
                if (!UserConstants.DEPT_NORMAL.equals(pD.getStatus())) {
                    failureNum++;
                    failureMsg.append(pD.getDeptCode()).append("组织停用，不允许新增!");
                    continue;
                }
                info.setParentId(pD.getDeptId());
                info.setAncestors(pD.getAncestors());
                info.setLevel(pD.getLevel() + 1);
                info.setCreateBy(SecurityUtils.getUsername());
                info.setAreaId(pD.getAreaId());
                if (StringUtils.isEmpty(info.getTenantId())) {
                    info.setTenantId(ConfigUtils.getTenantId());
                }
                if (StringUtils.isEmpty(info.getOrderNum())) {
                    info.setOrderNum(info.getDeptCode());
                }
                SysDept e = deptMapper.checkDeptCodeUnique(info.getDeptCode());
                if (StringUtils.isNull(e)) {
                    deptMapper.insertDept(info);
                    if (StringUtils.isNull(info.getDeptId())) {
                        failureNum++;
                        failureMsg.append(info.getDeptCode()).append("组织新增时异常!");
                        continue;
                    }
                    info.setAncestors(info.getAncestors() + "," + info.getDeptId());
                    deptMapper.updateDept(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、编码 " + info.getDeptCode() + " 导入成功!");
                } else {
                    info.setAncestors(pD.getAncestors() + "," + info.getDeptId());
                    info.setDeptId(e.getDeptId());
                    deptMapper.updateDept(info);
                    successMsg.append("<br/>" + successNum + "、编码 " + info.getDeptCode() + " 导入成功!");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、编码 " + info.getDeptCode() + " 导入失败!";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 递归列表
     */
    private void recursionFn(Map<Long, List<SysDept>> groupByParentIdDepts, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = groupByParentIdDepts.get(t.getDeptId());
        if (childList != null) {
            t.setChildren(childList);
            // 为每个子节点递归找到子节点
            for (SysDept tChild : childList) {
                recursionFn(groupByParentIdDepts, tChild);
            }
        } else {
            t.setChildren(new ArrayList<>(0));
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    @Override
    public List<SysDept> getTownList() {
        return deptMapper.getTownList(SecurityUtils.getLoginUser().getTenantId());
    }

    @Override
    public SysDept getDeptIdByAreaId(Long areaId) {
        return deptMapper.getDeptIdByAreaId(areaId);
    }

    @Override
    @DataScope(deptAlias = "t")
    public List<SupervisionVo> supervision(ScheduleParam param) {
        return deptMapper.supervision(param);
    }

    @Override
    @DataScope(deptAlias = "t")
    public List<CountScheduleVo> countSchedule(ScheduleParam param) {
        List<CountScheduleVo> list = deptMapper.countSchedule(param);
        list.forEach(l -> {
            l.setNoBuildT(l.getChildNumT() - l.getBuildT());
            l.setNoBuildV(l.getChildNumV() - l.getBuildV());
            l.setNoBuildG(l.getChildNumG() - l.getBuildG());
        });
        return list;
    }

    @Override
    @DataScope(deptAlias = "t")
    public List<ScheduleDetailsVo> scheduleDetails(ScheduleParam param) {
        return deptMapper.scheduleDetails(param);
    }

    @Override
    @DataScope(deptAlias = "t")
    public List<SysDept> selectBuildDeptList(ScheduleParam param) {
        return deptMapper.selectBuildDeptList(param);
    }

    @Override
    @DataScope(deptAlias = "t")
    public List<SysDept> selectChildListForScope(Long deptId) {
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        return deptMapper.selectChildListForScope(dept);
    }

    @Override
    public String getAllDeptName(Long deptId) {
        String pName = deptMapper.getAllDeptName(deptId);
        SysDept dept = deptMapper.selectDeptById(deptId);
        //此处统一按照 县-乡 - 组织机构名称 来展示
        return pName + dept.getDeptName();
    }

    @Override
    public List<SysDept> getAllDept() {
        return deptMapper.getAllDept();
    }

    @Override
    public String getTownAndVillage(Long deptId) {
        return deptMapper.getTownAndVillage(deptId);
    }

    @Override
    public String getAutoName(Long deptId) {
        return deptMapper.getAutoName(deptId);
    }

    @Override
    public List<Map<String,Object>> getCityDept() {
        return deptMapper.getCityDept();
    }

    @Override
    public List<AccountingVo> getDeptAccountingList(ScheduleParam param) {
        return deptMapper.getDeptAccountingList(param);
    }
}
