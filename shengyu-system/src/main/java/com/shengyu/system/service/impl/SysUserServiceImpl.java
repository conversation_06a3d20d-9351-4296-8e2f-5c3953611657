package com.shengyu.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.annotation.DataScope;
import com.shengyu.common.constant.KeysConstants;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.domain.dto.ForeignParam;
import com.shengyu.common.core.domain.dto.SysUserDto;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysRole;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.JsonUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.http.HttpClientUtil;
import com.shengyu.common.utils.sign.SecurityDesUtil;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.system.domain.SysPost;
import com.shengyu.system.domain.SysUserPost;
import com.shengyu.system.domain.SysUserRole;
import com.shengyu.system.mapper.*;
import com.shengyu.system.service.ISysConfigService;
import com.shengyu.system.service.ISysMenuService;
import com.shengyu.system.service.ISysUserService;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ScheduledExecutorService executor;
    @Value("${xxl.sso.server}")
    private String xxlSsoUrl;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户名查询用户
     *
     * @param phone 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByPhone(String phone) {
        return userMapper.selectUserByPhone(phone);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysRole role : list) {
            idsStr.append(role.getRoleName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysPost post : list) {
            idsStr.append(post.getPostName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(String userName) {
        int count = userMapper.checkUserNameUnique(userName);
        if (count > 0) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public String checkIdCardUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkIdCardUnique(user.getIdCard());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new CustomException("不允许操作超级管理员用户");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user, boolean isSync) {
        if (StringUtils.isNull(user.getDeptId())) {
            user.setDeptId(1534L);//默认部门
        }
        if (StringUtils.isEmpty(user.getTenantId())) {
            user.setTenantId(SecurityUtils.getLoginUser().getTenantId());
        }
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户与角色管理
        insertUserRole(user);
        insertUserPost(user);
        if (isSync) {//同步数据
            user.setAreaId(deptMapper.selectDeptById(user.getDeptId()).getAreaId());
            executor.schedule(syncUser(xxlSsoUrl, user), 1, TimeUnit.MILLISECONDS);
        }
        return rows;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user, boolean isSync) {
        Long userId = user.getUserId();
        if (isSync) {
            // 删除用户与角色关联
            userRoleMapper.deleteUserRoleByUserId(userId);
            userPostMapper.deleteUserPostByUserId(userId);
            // 新增用户与角色管理、岗位管理
            insertUserRole(user);
            insertUserPost(user);
        }
        int res = userMapper.updateUser(user);
        String manageKey = "temp_manage_dept_id_" + user.getUserName();
        redisCache.deleteObject(manageKey);//修改用户组织机构则删除该用户的临时管理组织机构id的缓存
        if (isSync) {
            executor.schedule(syncUser(xxlSsoUrl, userMapper.selectUserById(userId)),
                    1, TimeUnit.MILLISECONDS);
        }
        return res;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        int row = userMapper.updateUser(user);
        return row;
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user, boolean isSync) {
        int res = userMapper.updateUser(user);
        if (isSync && res > 0) {
            syncCommon(user);
        }
        return res;
    }

    private void syncCommon(SysUser user) {
        if (StringUtils.isEmpty(user.getIdCard())) {
            SysUser dataUser = userMapper.selectUserById(user.getUserId());
            user.setIdCard(dataUser.getIdCard());
        }
        executor.schedule(syncUser(xxlSsoUrl, user),
                1, TimeUnit.MILLISECONDS);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        int res = userMapper.updateUser(user);
        if (res > 0) {
            syncCommon(user);
        }
        return res;
    }

    @Override
    public int batchResetPwd(Long[] ids, String password) {
        int res = userMapper.batchResetPwd(ids, password);
        if (res > 0) {
            List<SysUser> userList = userMapper.selectListByIds(Arrays.asList(ids));
            userList.forEach(user -> {
                user.setPassword(password);
                syncCommon(user);
            });
        }
        return res;
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        int res = userMapper.resetUserPwd(userName, password);
        if (res > 0) {
            SysUser user = userMapper.selectUserByUserName(userName);
            user.setPassword(password);
            syncCommon(user);
        }
        return res;
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roles) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getUserId());
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotNull(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0) {
                userPostMapper.batchUserPost(list);
            }
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotNull(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId, boolean isSync) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        int row = userMapper.deleteUserById(userId);
        return row;
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new CustomException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                if (StringUtils.isEmpty(user.getUserName())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(user.getIdCard()) && !StringUtils.isValidIdNumber(user.getIdCard())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(user.getUserName()).append(" " +
                            "导入失败：身份证号有误!");
                    continue;
                }
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());

                SysDept dept = deptMapper.checkDeptCodeUnique(String.valueOf(user.getDeptId()));
                if (StringUtils.isNull(dept)) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(user.getUserName()).append(" 导入失败：组织机构未找到!");
                    continue;
                }
                if (StringUtils.isNotEmpty(user.getRoleCode())) {
                    SysRole r = roleMapper.checkRoleKeyUnique(user.getRoleCode(),
                            SecurityUtils.getLoginUser().getTenantId());
                    if (StringUtils.isNotNull(r)) {
                        Long[] rIds = {r.getRoleId()};
                        user.setRoleIds(rIds);
                    }
                }
                if (StringUtils.isNotEmpty(user.getPostCode())) {
                    SysPost p = postMapper.checkPostCodeUnique(user.getPostCode(),
                            SecurityUtils.getLoginUser().getTenantId());
                    if (StringUtils.isNotNull(p)) {
                        Long[] pIds = {p.getPostId()};
                        user.setPostIds(pIds);
                    }
                }
                user.setDeptId(dept.getDeptId());
                if (StringUtils.isNull(u)) {
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    if (StringUtils.isEmpty(user.getTenantId())) {
                        user.setTenantId(SecurityUtils.getLoginUser().getTenantId());
                    }
                    this.insertUser(user, true);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 导入成功");
                } else if (isUpdateSupport) {
                    user.setUpdateBy(operName);
                    this.updateUser(user, true);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(user.getUserName()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.append(failureMsg).toString();
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectByKeyWord(SysUser user) {
        return userMapper.selectByKeyWord(user);
    }

    @Override
    public SysUserDto selectById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    public LoginUser createAppLoginUser(String userKey, String token) {
        SysUser user = userMapper.getUserByToken(token);
        if (StringUtils.isNull(user)) {
            return null;
        }
        LoginUser loginUser = new LoginUser(user, getMenuPermission(user));
        loginUser.setExpireTime(4102545600000L);//过期时间设置到2100年 永久有效
        loginUser.setPlatform(2);
        redisCache.setCacheObject(userKey, loginUser);
        return loginUser;
    }

    @Override
    public int updateUserToken(String token, String userName) {
        return userMapper.updateUserToken(token, userName);
    }

    @Override
    public int updateAppUser(SysUser user) {
        return userMapper.updateUser(user);
    }

    private Set<String> getMenuPermission(SysUser user) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
        }
        return perms;
    }

    @Override
    public boolean updateUserPhone(String userName, String phone) {
        boolean res = userMapper.updateUserPhone(userName, phone) > 0;
        if (res) {
            SysUser user = userMapper.selectUserByUserName(userName);
            syncCommon(user);
        }
        return res;
    }

    @Override
    public List<String> selectListByRoleKey(String roleKey, Long deptId) {
        return userMapper.selectListByRoleKey(roleKey, deptId, SecurityUtils.getLoginUser().getTenantId());
    }

    @Override
    public SysUser getUserByIdCard(String idCard) {
        return userMapper.checkIdCardUnique(idCard);
    }

    private static TimerTask syncUser(final String url, final SysUser user) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    String syncMethod = "/api/syncUser";
                    Long timeStamp = System.currentTimeMillis();
                    String dataUser = JsonUtils.getJsonString(user);
                    String sign = SecurityDesUtil.signMethod(dataUser + timeStamp, KeysConstants.PRI_KEY);
                    String desData = SecurityDesUtil.tripleDESEncrypt(dataUser, KeysConstants.RSA_SECRET);
                    ForeignParam foreignParam = new ForeignParam(sign, desData, timeStamp);
                    String res = HttpClientUtil.httpSendPostStr(url + syncMethod, JsonUtils.getJsonString(foreignParam),
                            "utf-8", ContentType.APPLICATION_JSON);
                    if (StringUtils.isNotEmpty(res) && StringUtils.isNotNull(user.getUserId())) {
                        SysUserMapper userMapper = SpringUtils.getBean(SysUserMapper.class);
                        JSONObject resJSON = JSONObject.parseObject(res);
                        if (resJSON.getIntValue("code") != 200) {
                            String msg = resJSON.getString("msg");
                            userMapper.updateSyncRemark(user.getUserId(), "同步失败(" + msg + ")");
                        } else {
                            userMapper.updateSyncRemark(user.getUserId(), null);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
    }

    @Override
    public List<SysUser> selectUserByDepts(Long[] deptIds) {
        return userMapper.selectUserByDepts(deptIds);
    }

    @Override
    public SysUser getUserByPost(Long deptId, String post) {
        SysUser user = userMapper.getUserByPost(deptId, post);
        if (StringUtils.isNotNull(user)) {
            return user;
        }
        return SecurityUtils.getLoginUser().getUser();
    }

    @Override
    public SysUser getUserByPostNoDefVal(Long deptId, String post) {
        SysUser user = userMapper.getUserByPost(deptId, post);
        if (StringUtils.isNotNull(user)) {
            return user;
        }
        return null;
    }

    @Override
    public SysUser getUserByPostForParent(Long deptId, String post) {
        SysUser user = userMapper.getUserByPostInRoleDept(deptId, ConfigUtils.getTenantId(), post);
        if (StringUtils.isNotNull(user)) {
            return user;
        }
        SysDept curDept = deptMapper.selectDeptById(deptId);
        Long cityDeptId = Long.parseLong(curDept.getAncestors().split(",")[3]);
        return getUserByPostNoDefVal(cityDeptId, post);
    }

    @Override
    public int authorize(Long id, String secStr) {
        SysUser user = new SysUser();
        user.setUserId(id);
        user.setDongleInfo(secStr);
        int res = userMapper.updateUser(user);
        executor.schedule(syncUser(xxlSsoUrl, userMapper.selectUserById(id)),
                1, TimeUnit.MILLISECONDS);
        return res;
    }

}
