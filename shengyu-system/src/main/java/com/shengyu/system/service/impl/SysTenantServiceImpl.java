package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysRole;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.ChineseUtils;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysArea;
import com.shengyu.system.domain.SysConfig;
import com.shengyu.system.domain.SysPost;
import com.shengyu.system.domain.SysTenant;
import com.shengyu.system.domain.model.TenantModel;
import com.shengyu.system.mapper.SysTenantMapper;
import com.shengyu.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 租户Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Service
public class SysTenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenant> implements ISysTenantService {
    @Autowired
    private SysTenantMapper sysTenantMapper;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysPostService sysPostService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysAreaService areaService;
    @Autowired
    private ScheduledExecutorService executor;
    @Autowired
    private ISysRoleService roleService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ISysUserService userService;

    /**
     * 查询租户
     *
     * @param id 租户ID
     * @return 租户
     */
    @Override
    public SysTenant selectSysTenantById(Integer id) {
        return sysTenantMapper.selectSysTenantById(id);
    }

    /**
     * 查询租户列表
     *
     * @param sysTenant 租户
     * @return 租户
     */
    @Override
    public List<SysTenant> selectSysTenantList(SysTenant sysTenant) {
        return sysTenantMapper.selectSysTenantList(sysTenant);
    }

    /**
     * 新增租户
     *
     * @param tenantModel 租户
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSysTenant(TenantModel tenantModel) {
        tenantModel.setCreateTime(DateUtils.getNowDate());
        tenantModel.setCreateBy(SecurityUtils.getUsername());
        SysDept dept = new SysDept();
        dept.setAreaId(tenantModel.getAreaId());
        if (StringUtils.isEmpty(tenantModel.getDeptName())) {
            dept.setDeptName(tenantModel.getName());
        } else {
            dept.setDeptName(tenantModel.getDeptName());
        }
        int res = sysTenantMapper.insertSysTenant(tenantModel);
        if (res > 0) {//初始化该县区顶级部门
            dept.setCreateBy(SecurityUtils.getUsername());
            dept.setCreateTime(DateUtils.getNowDate());
            dept.setTenantId(String.valueOf(tenantModel.getId()));
            SysArea area = areaService.selectSysAreaById(tenantModel.getAreaId());
            if (StringUtils.isNotNull(area)) {
                dept.setDeptCode(area.getCode());
            }
            dept.setIsBusiness("0");//非业务组织
            dept.setParentId(area.getParentId());//直接划分在某个行政市下的区县 (需事先将行政区划脚本导入进组织机构，俩表的省市id可保持一致)
            dept.setAreaId(tenantModel.getAreaId());
            if (area.getLevel() == 2) {
                dept.setDeptId(area.getId());
                res = deptService.updateDept(dept);//市级组织机构一开始已经初始化进数据库中，所以此时只用修改其组织id即可
            } else {
                res = deptService.insertDept(dept);
            }
            String createBy = ChineseUtils.toFirstSpell(dept.getDeptName()) + "Admin" + StringUtils.randomStr(2);
            initSysPost(String.valueOf(tenantModel.getId()), createBy);//初始化审批流的岗位
            initSysRole(String.valueOf(tenantModel.getId()), createBy, dept);//初始化游客角色
            executor.schedule(initData(dept.getDeptId(), String.valueOf(tenantModel.getId()), sysTenantMapper), 1,
                    TimeUnit.MILLISECONDS);
        }
        return res;
    }

    private void initSysPost(String tenantId, String createBy) {
        String postKeyStr = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "system.camuada.postKey");
        String[] postArr = postKeyStr.split(",");
        int index = 1;
        for (String postStr : postArr) {
            String[] posts = postStr.split(":");
            SysPost post = new SysPost();
            post.setPostCode(posts[1]);
            post.setPostName(posts[2]);
            post.setTenantId(tenantId);
            post.setPostSort(String.valueOf(index));
            post.setCreateBy(createBy);
            sysPostService.insertPost(post);
            index++;
        }
    }

    private void initSysRole(String tenantId, String createBy, SysDept dept) {
        SysRole adminRole = new SysRole();
        adminRole.setRoleKey(createBy);
        adminRole.setRoleName(dept.getDeptName() + "管理员");
        adminRole.setRoleSort("1");
        adminRole.setDataScope("4");//所有部门及部门以下数据权限
        adminRole.setTenantId(tenantId);
        List<Long> aMenuIds = new ArrayList<>();
        aMenuIds.add(1L);
        adminRole.setMenuIds(aMenuIds.toArray(new Long[0]));
        roleService.insertRole(adminRole);
        SysUser adminUser = new SysUser();
        adminUser.setDeptId(dept.getDeptId());
        adminUser.setUserName(adminRole.getRoleKey());
        adminUser.setNickName(dept.getDeptName() + "管理员");
        adminUser.setPhonenumber(adminRole.getRoleKey());
        adminUser.setIdCard(adminRole.getRoleKey());
        Long[] roleIds = new Long[1];
        roleIds[0] = adminRole.getRoleId();
        adminUser.setRoleIds(roleIds);
        adminUser.setTenantId(tenantId);
        userService.insertUser(adminUser, true);
        SysRole role = new SysRole();
        role.setRoleName("游客");
        role.setRemark("默认初始化游客角色");
        role.setRoleKey("tourist" + tenantId);
        role.setTenantId(tenantId);
        role.setRoleSort("0");
        role.setDataScope("5");
        role.setCreateBy(createBy);
        String menuKeyStr = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "system.tourist.menu");
        if (StringUtils.isNotEmpty(menuKeyStr)) {
            String[] menuArr = menuKeyStr.split(",");
            role.setMenuIds(Arrays.stream(menuArr).map(Long::parseLong).toArray(Long[]::new));
        }
        roleService.insertRole(role);
        SysConfig config = new SysConfig();
        config.setConfigKey("sys.tourist.role_" + tenantId);
        config.setConfigName("游客id-" + tenantId);
        config.setConfigValue(String.valueOf(role.getRoleId()));
        config.setConfigType("Y");
        config.setCreateBy("admin");
        config.setCreateTime(DateUtils.getNowDate());
        configService.insertConfig(config);
    }

    //初始化模板数据 如会计科目，凭证模板等
    private static TimerTask initData(final Long deptId, final String tenantId, final SysTenantMapper sysTenantMapper) {
        return new TimerTask() {
            @Override
            public void run() {
                sysTenantMapper.initData(deptId, tenantId);
            }
        };
    }

    /**
     * 修改租户
     *
     * @param sysTenant 租户
     * @return 结果
     */
    @Override
    public int updateSysTenant(SysTenant sysTenant) {
        return sysTenantMapper.updateSysTenant(sysTenant);
    }

    /**
     * 批量删除租户
     *
     * @param ids 需要删除的租户ID
     * @return 结果
     */
    @Override
    public int deleteSysTenantByIds(Integer[] ids) {
        return sysTenantMapper.deleteSysTenantByIds(ids);
    }

    /**
     * 删除租户信息
     *
     * @param id 租户ID
     * @return 结果
     */
    @Override
    public int deleteSysTenantById(Integer id) {
        return sysTenantMapper.deleteSysTenantById(id);
    }
}
