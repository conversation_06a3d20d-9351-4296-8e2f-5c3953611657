package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.system.domain.SysNoticeStatus;
import com.shengyu.system.mapper.SysNoticeStatusMapper;
import com.shengyu.system.service.ISysNoticeStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Service
public class SysNoticeStatusServiceImpl extends ServiceImpl<SysNoticeStatusMapper, SysNoticeStatus> implements ISysNoticeStatusService {
    @Autowired
    private SysNoticeStatusMapper sysNoticeStatusMapper;

    /**
     * 查询消息状态
     *
     * @param id 消息状态ID
     * @return 消息状态
     */
    @Override
    public SysNoticeStatus selectSysNoticeStatusById(Long id) {
        return sysNoticeStatusMapper.selectSysNoticeStatusById(id);
    }

    /**
     * 查询消息状态列表
     *
     * @param sysNoticeStatus 消息状态
     * @return 消息状态
     */
    @Override
    public List<SysNoticeStatus> selectSysNoticeStatusList(SysNoticeStatus sysNoticeStatus) {
        return sysNoticeStatusMapper.selectSysNoticeStatusList(sysNoticeStatus);
    }

    /**
     * 新增消息状态
     *
     * @param sysNoticeStatus 消息状态
     * @return 结果
     */
    @Override
    public int insertSysNoticeStatus(SysNoticeStatus sysNoticeStatus) {
        sysNoticeStatus.setCreateTime(DateUtils.getNowDate());
        return sysNoticeStatusMapper.insertSysNoticeStatus(sysNoticeStatus);
    }

    /**
     * 修改消息状态
     *
     * @param sysNoticeStatus 消息状态
     * @return 结果
     */
    @Override
    public int updateSysNoticeStatus(SysNoticeStatus sysNoticeStatus) {
        return sysNoticeStatusMapper.updateSysNoticeStatus(sysNoticeStatus);
    }

    /**
     * 批量删除消息状态
     *
     * @param ids 需要删除的消息状态ID
     * @return 结果
     */
    @Override
    public int deleteSysNoticeStatusByIds(Long[] ids) {
        return sysNoticeStatusMapper.deleteSysNoticeStatusByIds(ids);
    }

    /**
     * 删除消息状态信息
     *
     * @param id 消息状态ID
     * @return 结果
     */
    @Override
    public int deleteSysNoticeStatusById(Long id) {
        return sysNoticeStatusMapper.deleteSysNoticeStatusById(id);
    }
}
