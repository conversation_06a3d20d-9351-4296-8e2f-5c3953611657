package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.HelpDocument;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.mapper.HelpDocumentMapper;
import com.shengyu.system.service.IHelpDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 帮助文档Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Service
public class HelpDocumentServiceImpl extends ServiceImpl<HelpDocumentMapper, HelpDocument> implements IHelpDocumentService {
    @Autowired
    private HelpDocumentMapper helpDocumentMapper;

    /**
     * 查询帮助文档
     *
     * @param id 帮助文档ID
     * @return 帮助文档
     */
    @Override
    public HelpDocument selectHelpDocumentById(Long id) {
        return helpDocumentMapper.selectHelpDocumentById(id);
    }

    /**
     * 查询帮助文档列表
     *
     * @param helpDocument 帮助文档
     * @return 帮助文档
     */
    @Override
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument) {
        return helpDocumentMapper.selectHelpDocumentList(helpDocument);
    }

    /**
     * 新增帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    @Override
    public int insertHelpDocument(HelpDocument helpDocument) {
        return helpDocumentMapper.insertHelpDocument(helpDocument);
    }

    /**
     * 修改帮助文档
     *
     * @param helpDocument 帮助文档
     * @return 结果
     */
    @Override
    public int updateHelpDocument(HelpDocument helpDocument) {
        if (StringUtils.isNull(helpDocument.getParentId())) {
            helpDocument.setParentId(0L);
        }
        return helpDocumentMapper.updateHelpDocument(helpDocument);
    }

    /**
     * 批量删除帮助文档
     *
     * @param ids 需要删除的帮助文档ID
     * @return 结果
     */
    @Override
    public int deleteHelpDocumentByIds(Long[] ids) {
        return helpDocumentMapper.deleteHelpDocumentByIds(ids);
    }

    /**
     * 删除帮助文档信息
     *
     * @param id 帮助文档ID
     * @return 结果
     */
    @Override
    public int deleteHelpDocumentById(Long id) {
        return helpDocumentMapper.deleteHelpDocumentById(id);
    }

    @Override
    public List<TreeSelect> buildTreeSelect(List<HelpDocument> ds) {
        List<HelpDocument> dsTrees = buildDocumentTree(ds);
        List<TreeSelect> treeList = dsTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
        return treeList;
    }

    public List<HelpDocument> buildDocumentTree(List<HelpDocument> ds) {
        List<HelpDocument> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (HelpDocument help : ds) {
            tempList.add(help.getId());
        }
        for (Iterator<HelpDocument> iterator = ds.iterator(); iterator.hasNext(); ) {
            HelpDocument help = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(help.getParentId())) {
                recursionFn(ds, help);
                returnList.add(help);
            }
        }
        if (returnList.isEmpty()) {
            returnList = ds;
        }
        return returnList;
    }

    private void recursionFn(List<HelpDocument> list, HelpDocument t) {
        // 得到子节点列表
        List<HelpDocument> childList = getChildList(list, t);
        t.setChildren(childList);
        for (HelpDocument tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<HelpDocument> getChildList(List<HelpDocument> list, HelpDocument t) {
        List<HelpDocument> tlist = new ArrayList<>();
        Iterator<HelpDocument> it = list.iterator();
        while (it.hasNext()) {
            HelpDocument n = it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<HelpDocument> list, HelpDocument t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    @Override
    public boolean hasChildById(Long id) {
        int result = helpDocumentMapper.hasChildById(id);
        return result > 0 ? true : false;
    }
}
