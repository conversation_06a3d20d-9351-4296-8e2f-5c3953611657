package com.shengyu.system.service;

import com.shengyu.system.domain.SysTenant;
import com.shengyu.system.domain.model.TenantModel;

import java.util.List;


/**
 * 租户Service接口
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
public interface ISysTenantService {
    /**
     * 查询租户
     *
     * @param id 租户ID
     * @return 租户
     */
    public SysTenant selectSysTenantById(Integer id);

    /**
     * 查询租户列表
     *
     * @param sysTenant 租户
     * @return 租户集合
     */
    public List<SysTenant> selectSysTenantList(SysTenant sysTenant);

    /**
     * 新增租户
     *
     * @param sysTenant 租户
     * @return 结果
     */
    public int insertSysTenant(TenantModel sysTenant);

    /**
     * 修改租户
     *
     * @param sysTenant 租户
     * @return 结果
     */
    public int updateSysTenant(SysTenant sysTenant);

    /**
     * 批量删除租户
     *
     * @param ids 需要删除的租户ID
     * @return 结果
     */
    public int deleteSysTenantByIds(Integer[] ids);

    /**
     * 删除租户信息
     *
     * @param id 租户ID
     * @return 结果
     */
    public int deleteSysTenantById(Integer id);
}
