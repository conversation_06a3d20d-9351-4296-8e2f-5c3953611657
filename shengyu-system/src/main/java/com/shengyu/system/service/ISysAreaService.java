package com.shengyu.system.service;

import com.shengyu.system.domain.SysArea;

import java.util.List;

/**
 * 省市县管理Service接口
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
public interface ISysAreaService {

    /**
     * description: 初始化到redis缓存中
     * version: 1.0
     * date: 2021/7/7 15:06
     *
     * @param
     * @return void
     * @author: lwy
     */
    void initAreaToRedis();


    /**
     * 查询省市县管理
     *
     * @param id 省市县管理ID
     * @return 省市县管理
     */
    public SysArea selectSysAreaById(Long id);

    /**
     * 查询省市县管理列表
     *
     * @param sysArea 省市县管理
     * @return 省市县管理集合
     */
    public List<SysArea> selectSysAreaList(SysArea sysArea);

    /**
     * 新增省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    public int insertSysArea(SysArea sysArea);

    /**
     * 修改省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    public int updateSysArea(SysArea sysArea);

    /**
     * 批量删除省市县管理
     *
     * @param ids 需要删除的省市县管理ID
     * @return 结果
     */
    public int deleteSysAreaByIds(Long[] ids);

    /**
     * 删除省市县管理信息
     *
     * @param id 省市县管理ID
     * @return 结果
     */
    public int deleteSysAreaById(Long id);

}
