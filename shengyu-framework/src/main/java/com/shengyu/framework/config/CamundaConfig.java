package com.shengyu.framework.config;

import com.shengyu.common.config.CapitalConfig;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.Deployment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * druid 配置多数据源
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
@DependsOn("capitalConfig")
public class CamundaConfig {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private CapitalConfig capitalConfig;

    //    @PostConstruct
    public void initCamunda() {
        if (!capitalConfig.isLoadCamunda()) {
            return;
        }
        try {
            ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resourcePatternResolver.getResources("classpath*:process/**/*.*");
            List<Deployment> eList = repositoryService.createDeploymentQuery().list();
            List<String> existsName = eList.stream().map(Deployment::getName).collect(Collectors.toList());
            for (Resource resource : resources) {
                String prName = Objects.requireNonNull(resource.getFilename()).substring(0, resource.getFilename().indexOf("."));
                if (!CollectionUtils.isEmpty(existsName) && existsName.contains(prName)) {
                    continue;
                }
                repositoryService.createDeployment()
                        .name(prName) // 定义部署文件的名称
                        .addClasspathResource("process/" + resource.getFilename()) // 绑定需要部署的流程文件
                        .deploy();// 部署流程
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
