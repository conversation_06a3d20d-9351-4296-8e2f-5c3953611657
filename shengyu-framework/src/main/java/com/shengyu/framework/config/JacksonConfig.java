package com.shengyu.framework.config;

import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.shengyu.framework.manager.DecorateJackson2ObjectMapperBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.math.BigInteger;
import java.util.List;

@Configuration
public class JacksonConfig {

    /***Jackson配置***/
    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder(List<Jackson2ObjectMapperBuilderCustomizer> customizers) {
        // 用自定义的DecorateJackson2ObjectMapperBuilder替换默认的Jackson2ObjectMapperBuilder
        Jackson2ObjectMapperBuilder builder = new DecorateJackson2ObjectMapperBuilder();
        builder.applicationContext(this.applicationContext);
        for (Jackson2ObjectMapperBuilderCustomizer customizer : customizers) {
            customizer.customize(builder);
        }
        return builder;
    }

    /**
     * Jackson全局转化long类型为String，解决jackson序列化时传入前端Long类型缺失精度问题
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return jacksonObjectMapperBuilder -> {
            jacksonObjectMapperBuilder.serializerByType(BigInteger.class, ToStringSerializer.instance);
            jacksonObjectMapperBuilder.serializerByType(Long.class, ToStringSerializer.instance);
        };
    }
}