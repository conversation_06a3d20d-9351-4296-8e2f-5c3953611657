package com.shengyu.framework.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.shengyu.common.constant.WebsocketConst;
import com.shengyu.common.core.redis.RedisReceiver;
import com.shengyu.common.utils.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class RedisConfig extends CachingConfigurerSupport {
    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    /**
     * 创建并配置Redisson客户端实例
     *
     * <p>该方法创建一个单节点模式的Redisson客户端，用于与Redis服务器进行交互。
     * 当容器中不存在RedissonClient实例时才会创建，并在销毁时自动关闭连接。</p>
     *
     * @return RedissonClient 配置好的Redisson客户端实例
     * @see Config Redisson配置类
     * @see Redisson Redisson客户端入口类
     */
    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        // 创建Redisson配置对象
        Config config = new Config();

        // 配置单节点Redis服务器地址
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port);

        // 如果配置了密码，则设置Redis服务器密码
        if (StringUtils.isNotEmpty(password)) {
            config.useSingleServer().setPassword(password);
        }

        // 根据配置创建并返回Redisson客户端实例
        return Redisson.create(config);
    }

    @Bean
    public RedisCacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认配置
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))  // 默认缓存30分钟
                .disableCachingNullValues()       // 不缓存null值
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new FastJson2JsonRedisSerializer(Object.class)));

        // 创建缓存管理器
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultCacheConfig)
//                .withInitialCacheConfigurations(getCacheConfigurations()) // 自定义缓存配置
                .transactionAware() // 支持事务
                .build();
    }

    // 自定义不同缓存的配置
//    private Map<String, RedisCacheConfiguration> getCacheConfigurations() {
//        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
//
//        // 用户缓存配置 - 1小时过期
//        cacheConfigurations.put("userCache", RedisCacheConfiguration.defaultCacheConfig()
//                .entryTtl(Duration.ofHours(1))
//                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new JdkSerializationRedisSerializer())));
//
//        // 产品缓存配置 - 2小时过期
//        cacheConfigurations.put("productCache", RedisCacheConfiguration.defaultCacheConfig()
//                .entryTtl(Duration.ofHours(2)));
//
//        return cacheConfigurations;
//    }


    /**
     * 创建并配置RedisTemplate实例
     *
     * <p>该方法创建一个RedisTemplate实例，用于操作Redis数据库。配置了键和值的序列化方式，
     * 使用StringRedisSerializer序列化键，使用FastJson2JsonRedisSerializer序列化值。</p>
     *
     * @param connectionFactory Redis连接工厂，用于建立与Redis服务器的连接
     * @return RedisTemplate<String, Object> 配置好的RedisTemplate实例
     * @see StringRedisSerializer 字符串键序列化器
     * @see FastJson2JsonRedisSerializer 基于FastJson的值序列化器
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        // 创建RedisTemplate实例并设置连接工厂
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 创建FastJson序列化器并配置ObjectMapper
        FastJson2JsonRedisSerializer<Object> serializer = new FastJson2JsonRedisSerializer<>(Object.class);

        // 配置ObjectMapper以支持所有字段的自动检测和类型信息保留
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        serializer.setObjectMapper(mapper);

        // 设置键的序列化器为StringRedisSerializer
        template.setKeySerializer(new StringRedisSerializer());
        // 设置值的序列化器为FastJson序列化器
        template.setValueSerializer(serializer);

        // 设置Hash键和值的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        // 初始化模板属性
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 配置Redis消息监听容器
     *
     * <p>创建并配置一个Redis消息监听容器，用于订阅指定频道的消息并转发给对应的消息监听器</p>
     *
     * @param redisConnectionFactory Redis连接工厂，用于建立与Redis服务器的连接
     * @param commonListenerAdapter  消息监听适配器，用于处理接收到的消息
     * @return RedisMessageListenerContainer 配置好的Redis消息监听容器实例
     * @see MessageListenerAdapter 消息监听适配器，将消息路由到指定方法
     * @see ChannelTopic 定义Redis发布/订阅的频道
     * @see WebsocketConst.REDIS_TOPIC_NAME 使用的Redis频道名称常量
     */
    @Bean
    public RedisMessageListenerContainer redisContainer(RedisConnectionFactory redisConnectionFactory,
                                                        MessageListenerAdapter commonListenerAdapter) {
        // 创建Redis消息监听容器
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();

        // 设置Redis连接工厂
        container.setConnectionFactory(redisConnectionFactory);

        // 添加消息监听器，订阅指定频道的消息
        // WebsocketConst.REDIS_TOPIC_NAME定义了监听的频道名称
        container.addMessageListener(commonListenerAdapter, new ChannelTopic(WebsocketConst.REDIS_TOPIC_NAME));

        return container;
    }

    /**
     * 创建并配置Redis消息监听适配器
     *
     * <p>该方法创建一个MessageListenerAdapter实例，用于将接收到的Redis消息路由到指定方法</p>
     *
     * @param redisReceiver 消息接收器实例，实际处理消息的Bean
     * @return MessageListenerAdapter 配置好的消息监听适配器实例
     * @see MessageListenerAdapter 用于将消息路由到指定方法的适配器
     * @see RedisReceiver 实际处理消息的接收器
     */
    @Bean
    MessageListenerAdapter commonListenerAdapter(RedisReceiver redisReceiver) {
        // 创建消息监听适配器，指定接收器实例和处理方法名"onMessage"
        MessageListenerAdapter messageListenerAdapter = new MessageListenerAdapter(redisReceiver, "onMessage");

        // 创建FastJson序列化器，用于消息的序列化和反序列化
        FastJson2JsonRedisSerializer serializer = new FastJson2JsonRedisSerializer(Object.class);

        // 为适配器设置序列化器
        messageListenerAdapter.setSerializer(serializer);

        return messageListenerAdapter;
    }
}
