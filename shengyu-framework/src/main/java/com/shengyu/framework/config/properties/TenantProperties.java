package com.shengyu.framework.config.properties;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class TenantProperties {

    private static TenantProperties instance;

    private Boolean enable = true;

    private String defaultTenantId = "0";

    /**
     * 多租户字段名称
     */
    public final static String TENANT_COLUMN = "tenant_id";

    /**
     * 多租户系统数据表
     */
    private List<String> ignoreTables = new ArrayList<>();

    private static final List<String> tenantTable = new ArrayList<String>();

    static {
        tenantTable.add("sys_area");
        tenantTable.add("sys_menu");
        tenantTable.add("sys_config");
        tenantTable.add("sys_dict_data");
        tenantTable.add("sys_dict_type");
        tenantTable.add("gen_table_column");
        tenantTable.add("gen_table");
        tenantTable.add("columns");
        tenantTable.add("tables");
        tenantTable.add("sys_role_menu");
        tenantTable.add("sys_logininfor");
        tenantTable.add("sys_tenant");
        tenantTable.add("sys_user");
        tenantTable.add("sys_post");
        tenantTable.add("sys_user_post");
        tenantTable.add("sys_role_dept");
        tenantTable.add("sys_role_menu");
        tenantTable.add("sys_role");
        tenantTable.add("sys_dept");
        tenantTable.add("sys_user_role");
        tenantTable.add("sys_job");
        tenantTable.add("sys_job_log");
        tenantTable.add("sys_oper_log");
        tenantTable.add("sys_notice");
        tenantTable.add("sys_notice_status");
        tenantTable.add("sys_notice_dept");
        tenantTable.add("sys_app_info");
        tenantTable.add("sys_help_document");
        tenantTable.add("sys_child_menus");
    }

    private TenantProperties() {
    }

    public static TenantProperties getInstance() {
        if (instance == null) {
            instance = new TenantProperties();
            instance.getIgnoreTables().addAll(tenantTable);
        }
        return instance;
    }
}
