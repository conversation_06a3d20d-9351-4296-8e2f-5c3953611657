package com.shengyu.framework.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.shengyu.common.utils.ConfigUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.framework.config.properties.TenantProperties;
import lombok.AllArgsConstructor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;


@AllArgsConstructor
public class CommonTenantHandler implements TenantLineHandler {

    /**
     * 获取租户ID
     *
     * @return 租户ID
     */
    @Override
    public Expression getTenantId() {
        try {
            if (SecurityUtils.getLoginUser() != null) {
                String tenantId = ConfigUtils.getTenantId();
                if (StringUtils.isNotEmpty(tenantId)) {
                    return new StringValue(tenantId);
                }
                tenantId = SecurityUtils.getLoginUser().getTenantId();
                if (StringUtils.isNotEmpty(tenantId)) {
                    return new StringValue(tenantId);
                }
            }
        } catch (Exception ignored) {

        }
        return new StringValue(TenantProperties.getInstance().getDefaultTenantId());
    }

    /**
     * 获取租户字段名称
     *
     * @return 租户字段名称
     */
    @Override
    public String getTenantIdColumn() {
        return TenantProperties.TENANT_COLUMN;
    }

    /**
     * 过滤租户表
     *
     * @param tableName 表名
     * @return 是否进行过滤 返回true 表示不进行多租户处理
     */
    @Override
    public boolean ignoreTable(String tableName) {
        if (!TenantProperties.getInstance().getEnable()) {
            return true;
        }
        return TenantProperties.getInstance().getIgnoreTables().contains(tableName) || tableName.startsWith("act_");
    }
}
