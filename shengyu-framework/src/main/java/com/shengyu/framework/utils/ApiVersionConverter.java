package com.shengyu.framework.utils;

import com.shengyu.common.utils.StringUtils;

public class ApiVersionConverter {

    /**
     * 将x.x.x转换为对应参数
     *
     * @param version x.x.x
     * @return ApiVersionComparable
     */
    public static ApiVersionComparable convert(String version) {
        ApiVersionComparable apiItem = new ApiVersionComparable();
        if (StringUtils.isBlank(version)) {
            return apiItem;
        }

        String[] cells = StringUtils.split(version, ".");
        apiItem.setHigh(Integer.parseInt(cells[0]));
        if (cells.length > 1) {
            apiItem.setMid(Integer.parseInt(cells[1]));
        }

        if (cells.length > 2) {
            apiItem.setLow(Integer.parseInt(cells[2]));
        }

        return apiItem;
    }

}
