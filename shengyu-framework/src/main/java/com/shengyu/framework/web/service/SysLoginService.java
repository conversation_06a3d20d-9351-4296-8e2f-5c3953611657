package com.shengyu.framework.web.service;

import com.shengyu.common.config.CapitalConfig;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.domain.model.LoginBody;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.enums.UserStatus;
import com.shengyu.common.exception.BaseException;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.exception.user.CaptchaException;
import com.shengyu.common.exception.user.CaptchaExpireException;
import com.shengyu.common.exception.user.UserPasswordNotMatchException;
import com.shengyu.common.utils.MessageUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.framework.manager.AsyncManager;
import com.shengyu.framework.manager.factory.AsyncFactory;
import com.shengyu.framework.security.authenticationToken.SmsCodeAuthenticationToken;
import com.shengyu.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private RedisCache redisCache;

    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * 登录验证
     *
     * @return 结果
     */
    public String login(String idCard) {
        // 用户验证
        SysUser user = userService.selectUserByUserName(idCard);
        if (StringUtils.isNull(user)) {
            throw new BaseException("当前账号在财务系统仍未绑定身份证号，请联系管理员绑定身份账号！");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new BaseException("当前账号已经停用！");
        }
        LoginUser loginUser = createLoginUser(user);
        recordLoginInfo(loginUser.getUser());
        String manageKey = "temp_manage_dept_id_" + user.getUserName();
        String tenantKey = "temp_manage_tenant_id_" + user.getUserName();
        String deptCodeKey = "temp_manage_dept_code_" + user.getUserName();
        if (StringUtils.isNull(redisCache.getCacheObject(manageKey))) {
            redisCache.setCacheObject(manageKey, user.getDeptId());
            redisCache.setCacheObject(tenantKey, user.getDept().getTenantId());
            redisCache.setCacheObject(deptCodeKey, user.getDept().getDeptCode());
        }
        // 生成token
        return tokenService.createToken(loginUser, 0);
    }

    private LoginUser createLoginUser(SysUser user) {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }

    /**
     * 登录验证
     *
     * @return 结果
     */
    public String login(LoginBody loginBody) {
        // 用户验证
        Authentication authentication = null;
        LoginUser loginUser;
        try {
            if (!CapitalConfig.isDemoEnabled()) {
                String verifyKey = Constants.CAPTCHA_CODE_KEY + loginBody.getUuid();
                String captcha = redisCache.getCacheObject(verifyKey);
                redisCache.deleteObject(verifyKey);
                if (captcha == null) {
                    throw new CaptchaExpireException();
                }
                if (!loginBody.getCode().equalsIgnoreCase(captcha)) {
                    throw new CaptchaException();
                }
            }
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            if (loginBody.getLoginType() == 0) {
                authentication = authenticationManager
                        .authenticate(new UsernamePasswordAuthenticationToken(loginBody.getUsername(), loginBody.getPassword()));
            } else {
                authentication = authenticationManager
                        .authenticate(new SmsCodeAuthenticationToken(loginBody.getPhone(), null));
            }
            loginUser = (LoginUser) authentication.getPrincipal();
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            } else {
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS,
                MessageUtils.message("user.login.success")));
        // 生成token
        return tokenService.createToken(loginUser, 0);
    }


    /**
     * 记录登录信息
     */
    public void recordLoginInfo(SysUser user) {
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS,
                MessageUtils.message(
                        "user.login.success")));
        userService.updateUserProfile(user, false);
    }

}
