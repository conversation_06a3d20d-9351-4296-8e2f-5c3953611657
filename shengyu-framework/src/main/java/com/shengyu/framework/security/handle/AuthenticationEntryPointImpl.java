package com.shengyu.framework.security.handle;

import com.alibaba.fastjson.JSON;
import com.shengyu.common.constant.HttpStatus;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.xxl.sso.core.login.SsoTokenLoginHelper;
import com.xxl.sso.core.login.SsoWebLoginHelper;
import com.xxl.sso.core.user.XxlSsoUser;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * 认证失败处理类 返回未授权
 *
 * <AUTHOR>
 */
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable {
    private static final long serialVersionUID = -8970718410437077606L;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        XxlSsoUser xxlUser = SsoWebLoginHelper.loginCheck(request, response);
        if (StringUtils.isNull(xxlUser)) {
            xxlUser = SsoTokenLoginHelper.loginCheck(request);
        }
        if (StringUtils.isNotNull(xxlUser)) {
            ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(302, "sso认证已通过，请调用子系统登录即可!")));
            return;
        }
        int code = HttpStatus.UNAUTHORIZED;
        String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
    }
}
