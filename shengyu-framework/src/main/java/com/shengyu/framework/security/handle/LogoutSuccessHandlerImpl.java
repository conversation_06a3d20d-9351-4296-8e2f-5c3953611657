package com.shengyu.framework.security.handle;

import com.alibaba.fastjson.JSON;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.HttpStatus;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.framework.manager.AsyncManager;
import com.shengyu.framework.manager.factory.AsyncFactory;
import com.shengyu.framework.web.service.TokenService;
import com.xxl.sso.core.login.SsoWebLoginHelper;
import com.xxl.sso.core.user.XxlSsoUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Autowired
    private TokenService tokenService;
    @Value("${xxl.sso.server}")
    private String xxlSsoUrl;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        LoginUser loginUser = tokenService.getLoginUser(request);//子系统用户
        XxlSsoUser ssoUser = SsoWebLoginHelper.loginCheck(request, response);//全局令牌用户
        if (StringUtils.isNotNull(loginUser)) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            AsyncManager.me().execute(AsyncFactory.logOut(SsoWebLoginHelper.getSessionIdByCookie(request),
                    xxlSsoUrl, loginUser.getUser().getAreaId()));
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
            return;
        }
        if (StringUtils.isNotNull(ssoUser)) {
            AsyncManager.me().execute(AsyncFactory.logOut(SsoWebLoginHelper.getSessionIdByCookie(request),
                    xxlSsoUrl, loginUser.getUser().getAreaId()));
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(ssoUser.getUsername(), Constants.LOGOUT, "退出成功"));
            return;
        }
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, "无权访问!")));
    }
}
