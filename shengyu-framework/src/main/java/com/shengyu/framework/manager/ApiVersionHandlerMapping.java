package com.shengyu.framework.manager;

import com.shengyu.framework.utils.ApiVersionComparable;
import com.shengyu.framework.utils.ApiVersionConverter;
import com.shengyu.framework.web.domain.ApiVersion;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.servlet.mvc.condition.RequestCondition;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

public class ApiVersionHandlerMapping extends RequestMappingHandlerMapping {

    @Override
    protected RequestCondition<?> getCustomTypeCondition(Class<?> handlerType) {
        return buildFrom(AnnotationUtils.findAnnotation(handlerType, ApiVersion.class));
    }

    @Override
    protected RequestCondition<?> getCustomMethodCondition(Method method) {
        return buildFrom(AnnotationUtils.findAnnotation(method, ApiVersion.class));
    }

    private ApiVersionCondition buildFrom(ApiVersion apiVersion) {
        return apiVersion == null
                ? new ApiVersionCondition(new ApiVersionComparable())
                : new ApiVersionCondition(ApiVersionConverter.convert(apiVersion.value()));
    }

}

