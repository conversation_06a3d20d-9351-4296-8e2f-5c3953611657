package com.shengyu.framework.manager;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * @Author：LJ
 * @Description：自定义Jackson对数组、字符串、数值、布尔和实体为null的序列化；
 * @Date: 2020/11/25
 * @Modified By:
 */
public class CustomizeJsonComponent {
    public final static JsonSerializer<Object> NULL_ARRAY_SERIALIZER = new CustomizeJsonComponent.NullArrayJsonSerializer();

    public final static JsonSerializer<Object> NULL_STRING_SERIALIZER = new CustomizeJsonComponent.NullStringJsonSerializer();

    public final static JsonSerializer<Object> NULL_NUMBER_SERIALIZER = new CustomizeJsonComponent.NullNumberJsonSerializer();

    /**
     * 处理数组集合类型的null值
     */
    public static class NullArrayJsonSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
            if (value == null) {
                gen.writeStartArray();
                gen.writeEndArray();
            } else {
                gen.writeObject(value);
            }
        }
    }

    /**
     * 处理字符串类型的null值
     */
    public static class NullStringJsonSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
            if (value == null) {
                gen.writeString(StringUtils.EMPTY);
            } else {
                gen.writeObject(value);
            }
        }
    }

    /**
     * 处理数值类型的null值
     */
    public static class NullNumberJsonSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
            gen.writeObject(value);
        }
    }

    /**
     * 处理boolean类型的null值
     */
    public static class NullBooleanJsonSerializer extends JsonSerializer<Object> {

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
            if (value == null) {
                gen.writeBoolean(false);
            } else {
                gen.writeObject(value);
            }
        }
    }

    /**
     * 处理实体对象类型的null值
     */
    public static class NullObjectJsonSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
            if (value == null) {
                gen.writeStartObject();
                gen.writeEndObject();
            } else {
                gen.writeObject(value);
            }
        }
    }
}