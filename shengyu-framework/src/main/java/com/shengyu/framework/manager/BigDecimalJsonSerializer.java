package com.shengyu.framework.manager;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * @Author: SS
 * @Date: 2023/06/09/15:29
 * @Description:
 */
public class BigDecimalJsonSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (bigDecimal != null) {
            jsonGenerator.writeObject(bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        } else {
            jsonGenerator.writeObject(new BigDecimal("0.00").toString());
        }
    }
}
