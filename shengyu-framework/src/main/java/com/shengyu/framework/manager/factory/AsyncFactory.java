package com.shengyu.framework.manager.factory;

import com.shengyu.common.config.RedisClient;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.KeysConstants;
import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.core.domain.dto.ForeignParam;
import com.shengyu.common.utils.JsonUtils;
import com.shengyu.common.utils.LogUtils;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.http.HttpClientUtil;
import com.shengyu.common.utils.ip.AddressUtils;
import com.shengyu.common.utils.ip.IpUtils;
import com.shengyu.common.utils.sign.SecurityDesUtil;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.system.domain.SysLogininfor;
import com.shengyu.system.domain.SysOperLog;
import com.shengyu.system.service.ISysLogininforService;
import com.shengyu.system.service.ISysOperLogService;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.SneakyThrows;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimerTask;

/**
 * 异步工厂（产生任务用）
 *
 * <AUTHOR>
 */
public class AsyncFactory {
    private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息
     * @param args     列表
     * @return 任务task
     */
    public static TimerTask recordLogininfor(final String username, final String status, final String message,
                                             final Object... args) {
        final UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        return new TimerTask() {
            @Override
            public void run() {
                String address = AddressUtils.getRealAddressByIP(ip);
                StringBuilder s = new StringBuilder();
                s.append(LogUtils.getBlock(ip));
                s.append(address);
                s.append(LogUtils.getBlock(username));
                s.append(LogUtils.getBlock(status));
                s.append(LogUtils.getBlock(message));
                // 打印信息到日志
                sys_user_logger.info(s.toString(), args);
                // 获取客户端操作系统
                String os = userAgent.getOperatingSystem().getName();
                // 获取客户端浏览器
                String browser = userAgent.getBrowser().getName();
                // 封装对象
                SysLogininfor logininfor = new SysLogininfor();
                logininfor.setUserName(username);
                logininfor.setIpaddr(ip);
                logininfor.setLoginLocation(address);
                logininfor.setBrowser(browser);
                logininfor.setOs(os);
                logininfor.setMsg(message);
                // 日志状态
                if (Constants.LOGIN_SUCCESS.equals(status) || Constants.LOGOUT.equals(status)) {
                    logininfor.setStatus(Constants.SUCCESS);
                } else if (Constants.LOGIN_FAIL.equals(status)) {
                    logininfor.setStatus(Constants.FAIL);
                }
                // 插入数据
                SpringUtils.getBean(ISysLogininforService.class).insertLogininfor(logininfor);
            }
        };
    }

    /**
     * 操作日志记录
     *
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final SysOperLog operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
                SpringUtils.getBean(ISysOperLogService.class).insertOperlog(operLog);
            }
        };
    }

    public static TimerTask sendMsg(final SocketMsg params) {
        return new TimerTask() {
            @Override
            public void run() {
                RedisClient redisClient = SpringUtils.getBean(RedisClient.class);
                redisClient.sendMessage(params);
            }
        };
    }

    public static TimerTask logOut(final String sessionId, final String url, final Long areaId) {
        return new TimerTask() {
            @SneakyThrows
            @Override
            public void run() {
                String syncMethod = "/api/logOut";
                String data = "{\"allSession\":\"" + sessionId + "\",\"areaId\":" + areaId + "}";
                Long timeStamp = System.currentTimeMillis();
                String sign = SecurityDesUtil.signMethod(data + timeStamp, KeysConstants.PRI_KEY);
                String desData = SecurityDesUtil.tripleDESEncrypt(data, KeysConstants.RSA_SECRET);
                ForeignParam foreignParam = new ForeignParam(sign, desData, timeStamp);
                HttpClientUtil.httpSendPostStr(url + syncMethod, JsonUtils.getJsonString(foreignParam),
                        "utf-8", ContentType.APPLICATION_JSON);
            }
        };
    }

}
