package com.shengyu.framework.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.math.BigDecimal;

/**
 * @Author：LJ
 * @Description： <p>
 * 本类主要目的是通过继承{@link Jackson2ObjectMapperBuilder}来重写{@link Jackson2ObjectMapperBuilder#configure(ObjectMapper)}方法
 * 来达到给ObjectMapper设置自定义DefaultSerializerProvider的目的
 * </p>
 * @Date: 2020/11/25
 * @Modified By:
 */
public class DecorateJackson2ObjectMapperBuilder extends Jackson2ObjectMapperBuilder {

    @Override
    public void configure(ObjectMapper objectMapper) {
        super.configure(objectMapper);
        /**
         * 给ObjectMapper设置自定义的DefaultSerializerProvider
         * {@link NullValueSerializerProvider}
         */
        objectMapper.setSerializerProvider(new NullValueSerializerProvider());
        SimpleModule module = new SimpleModule();
        module.addSerializer(BigDecimal.class, new BigDecimalJsonSerializer());
        objectMapper.registerModule(module);
    }
}