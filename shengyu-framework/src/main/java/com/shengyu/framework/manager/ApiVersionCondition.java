package com.shengyu.framework.manager;

import com.shengyu.framework.utils.ApiVersionComparable;
import com.shengyu.framework.utils.ApiVersionConverter;
import lombok.AllArgsConstructor;
import org.springframework.web.servlet.mvc.condition.RequestCondition;

import javax.servlet.http.HttpServletRequest;

@AllArgsConstructor
public class ApiVersionCondition implements RequestCondition<ApiVersionCondition> {

    private ApiVersionComparable version;

    @Override
    public ApiVersionCondition combine(ApiVersionCondition other) {
        // 选择版本最大的接口
        return version.compareTo(other.version) >= 0
                ? new ApiVersionCondition(version)
                : new ApiVersionCondition(other.version);
    }

    @Override
    public ApiVersionCondition getMatchingCondition(HttpServletRequest request) {
        //获取前端传递的版本号
        String version = request.getHeader("interface-version");
        ApiVersionComparable item = ApiVersionConverter.convert(version);
        // 获取所有小于等于版本的接口
        if (item.compareTo(this.version) >= 0) {
            return this;
        }

        return null;
    }

    @Override
    public int compareTo(ApiVersionCondition other, HttpServletRequest request) {
        // 获取最大版本对应的接口
        return other.version.compareTo(this.version);
    }
}
