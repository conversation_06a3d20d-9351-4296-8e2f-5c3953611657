package com.shengyu.framework.aspectj;

import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.BaseEntity;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.framework.web.service.TokenService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * description: PbFiledValueAspect <br>
 *
 * @date: 2021/7/31 11:50 <br>
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
@Aspect
@Component
public class PbFiledValueAspect {

    @Pointcut("execution(* com.shengyu.*.controller..*.add(..)) || execution(* com.shengyu.*.controller..*.edit(..))")
    public void setFileValuePointCut() {

    }

    @Before("setFileValuePointCut()")
    public void doBefore(JoinPoint point) {
        // 获取当前的用户
        String createBy = "admin";
        try {
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            createBy = loginUser.getUsername();
        }catch (Exception e){
        }
        //访问目标方法的参数：
        Object[] args = point.getArgs();
        BaseEntity baseEntity = (BaseEntity) args[0];
        String mothName = point.getSignature().getName();
        if (mothName.contains(Constants.ADD)) {
            //是新增方法，将创建时间和创建人赋值
            baseEntity.setCreateBy(createBy);
            if(StringUtils.isNull(baseEntity.getCreateTime())){
                baseEntity.setCreateTime(new Date());
            }
        } else if (mothName.contains(Constants.EDIT)) {
            //编辑方法，将更新时间和更新人赋值
            baseEntity.setUpdateBy(createBy);
            baseEntity.setUpdateTime(new Date());
        }
    }
}
