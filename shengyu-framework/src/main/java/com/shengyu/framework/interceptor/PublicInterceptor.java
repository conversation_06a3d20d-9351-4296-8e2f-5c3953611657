package com.shengyu.framework.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.sign.SecurityDesUtil;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * @Author: SS
 * @Date: 2023/02/23/11:17
 * @Description:
 */
@Component
public class PublicInterceptor implements HandlerInterceptor {

    private final String pubKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCE/8ELXLJNRM0ULvU0VfGAk5o1ewenslzSqNdJgFUAloNvKnkDj+Yn6RwuK1i3LWkPtEszVqS3wEBOA1PBZg4+u1Z5AzkgJd1iCE9OA2hTLteQyjAtGHGFYMDp6A0U8PbuTUl4rLcUexYcYxrbvZHlbai0EXlMbqSgKYtiFRCI3QIDAQAB";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Long deptId = null;
        try {
            deptId = Long.parseLong(request.getParameter("deptId"));
            request.setAttribute("deptId",deptId);
        } catch (Exception e) {
        }
        if (handler instanceof HandlerMethod) {
            String timeStamp = request.getHeader("timeStamp");
            String sign = request.getHeader("sign");
            String idCard = request.getHeader("idCard");
            if (StringUtils.isEmpty(timeStamp) || StringUtils.isEmpty(sign)
                    || (StringUtils.isNull(deptId) && StringUtils.isEmpty(idCard))) {
                AjaxResult<String> ajaxResult = AjaxResult.error("签名校验未通过!");
                ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                return false;
            }
            String params = "deptId=" + (StringUtils.isNull(deptId) ? "" : deptId)
                            + "&idCard=" + (StringUtils.isEmpty(idCard) ? "" : idCard) + "&timeStamp=" + timeStamp;
            try {
                boolean isPre = SecurityDesUtil.verifyMethod(params.getBytes(StandardCharsets.UTF_8), Base64.decode(sign), pubKey);
                if (!isPre) {
                    AjaxResult<String> ajaxResult = AjaxResult.error("签名校验未通过!");
                    ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                    return false;
                }
            } catch (Exception e) {
                AjaxResult<String> ajaxResult = AjaxResult.error("签名校验未通过!");
                ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                return false;
            }
//            if (StringUtils.isNull(deptId)) {
//                deptId = asyncService.getDeptByIdCard(idCard);
//                if (StringUtils.isNull(deptId)) {
//                    AjaxResult<String> ajaxResult = AjaxResult.error("该身份证号未通过系统验证(请上报相关部门注册)!");
//                    ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
//                    return false;
//                }
//                request.setAttribute("deptId",deptId);
//            }
            return true;
        }
        AjaxResult<String> ajaxResult = AjaxResult.error("签名校验未通过!");
        ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
        return false;
    }
}
