package com.shengyu.framework.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.sign.SecurityDesUtil;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * @Author: SS
 * @Date: 2023/02/23/11:17
 * @Description:
 */
@Component
public class ApiSignInterceptor implements HandlerInterceptor {

    @Value("${token.header}")
    private String header;

    private final String pubKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJFE1+aJaYQtwlzu8UAT0TbHT8NtHCMz34e0xi9U" +
            "/zquuQTZDRBWj0p+sQjronZiYAI9ziIKG/bZnF83v3dUdp7BK9bJTjN0tmHBRYBfoHPaA7gqC/bGm/WKmATUwyrMVQVP5DHG+k065RuroQKHf7WchuKaqiihRl9P+eEzgJGQIDAQAB";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String forever = request.getHeader("forever");
        if (handler instanceof HandlerMethod && "y".equals(forever)) {
            String token = getToken(request);
            String timeStamp = request.getHeader("timeStamp");
            String sign = request.getHeader("sign");
            if (StringUtils.isEmpty(token) || StringUtils.isEmpty(timeStamp) || StringUtils.isEmpty(sign)) {
                AjaxResult ajaxResult = AjaxResult.error("签名校验未通过!");
                ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                return false;
            }
            String params = "token=" + token + "&timeStamp=" + timeStamp;
            try {
                boolean isPre = SecurityDesUtil.verifyMethod(params.getBytes(StandardCharsets.UTF_8), Base64.decode(sign), pubKey);
                if (!isPre) {
                    AjaxResult ajaxResult = AjaxResult.error("签名校验未通过!");
                    ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                    return false;
                }
            }catch (Exception e){
                AjaxResult ajaxResult = AjaxResult.error("签名校验未通过!");
                ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
                return false;
            }
            return true;
        }
        AjaxResult<String> ajaxResult = AjaxResult.error("签名校验未通过!");
        ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
        return false;
    }

    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }
}
