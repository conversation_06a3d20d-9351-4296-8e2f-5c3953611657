package com.shengyu.framework.interceptor;

import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.github.pagehelper.Page;
import com.github.pagehelper.cache.Cache;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: SS
 * @Date: 2024/10/28/10:34
 * @Description:
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode()
public class OwnTenantLineInterceptor extends TenantLineInnerInterceptor {

    public OwnTenantLineInterceptor(TenantLineHandler tenantLineHandler) {
        super(tenantLineHandler);
    }

    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        Cache<String, MappedStatement> mappedStatementCache = null;
        List<Interceptor> interceptors = ms.getConfiguration().getInterceptors();
        MappedStatement mappedStatement = null;
        for (Interceptor interceptor : interceptors) {
            Class<? extends Interceptor> aClass = interceptor.getClass();
            if (aClass.getName().equals("com.github.pagehelper.PageInterceptor")) {
                try {
                    Field msCountMap = aClass.getDeclaredField("msCountMap");
                    if (msCountMap != null) {
                        msCountMap.setAccessible(true);
                        mappedStatementCache = (Cache<String, MappedStatement>) msCountMap.get(interceptor);
                        if (mappedStatementCache != null) {
                            mappedStatement = mappedStatementCache.get(ms.getId());
                        }
                    }
                } catch (NoSuchFieldException e) {
                    // throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
//                    throw new RuntimeException(e);
                }
            }
        }

        // 如果缓存中有，并且pageHelper的缓存中也有，证明此方法被pageHepler代理，并且被@InterceptorIgnore 修饰
        if (mappedStatement != null) {
            Class<InterceptorIgnoreHelper> interceptorIgnoreHelperClass = InterceptorIgnoreHelper.class;
            ConcurrentHashMap<String, InterceptorIgnoreHelper.InterceptorIgnoreCache> cache = null;
            try {
                Field interceptorIgnoreCache = interceptorIgnoreHelperClass.getDeclaredField("INTERCEPTOR_IGNORE_CACHE");
                if (interceptorIgnoreCache != null) {
                    interceptorIgnoreCache.setAccessible(true);
                    cache = (ConcurrentHashMap<String, InterceptorIgnoreHelper.InterceptorIgnoreCache>) interceptorIgnoreCache.get(interceptorIgnoreHelperClass);
                }
            } catch (NoSuchFieldException e) {
                super.beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
                // throw new RuntimeException(e);
            } catch (IllegalAccessException e) {
                // throw new RuntimeException(e);
                super.beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
            }
            if (willIgnore(ms.getId(), InterceptorIgnoreHelper.InterceptorIgnoreCache::getTenantLine, cache)) return;
        } else {
            if (InterceptorIgnoreHelper.willIgnoreTenantLine(ms.getId())) return;
        }


        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        mpBs.sql(parserSingle(mpBs.sql(), null));
    }

    public static boolean willIgnore(String id, Page.Function<InterceptorIgnoreHelper.InterceptorIgnoreCache, Boolean> function, ConcurrentHashMap<String, InterceptorIgnoreHelper.InterceptorIgnoreCache> map) {
        InterceptorIgnoreHelper.InterceptorIgnoreCache ignoreCache = map.get(id);
        if (ignoreCache == null) {
            int i = id.lastIndexOf("_");
            if (i >= 0) {
                String substring = id.substring(0, i);
                ignoreCache = map.get(substring);
            }
        }
        if (ignoreCache == null) {
            ignoreCache = map.get(id.substring(0, id.lastIndexOf(StringPool.DOT)));
        }
        if (ignoreCache != null) {
            Boolean apply = function.apply(ignoreCache);
            return apply != null && apply;
        }
        return false;
    }

    @Override
    protected void processSelect(Select select, int index, String sql, Object obj) {
        if (sql.contains("t.tenant_id IS NOT NULL")) {
            return;
        }
        super.processSelect(select, index, sql, obj);
    }
}
