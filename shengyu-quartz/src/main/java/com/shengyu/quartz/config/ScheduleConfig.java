//package com.shengyu.quartz.config;
//
//import com.shengyu.framework.datasource.DynamicDataSource;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.quartz.SchedulerFactoryBean;
//
//import javax.sql.DataSource;
//import java.util.Properties;
//
/// **
// * 定时任务配置
// *
// * <AUTHOR>
// */
//@Configuration
//public class ScheduleConfig {
//
//    @Autowired
//    private DynamicDataSource dynamicDataSource;
//
//    @Bean
//    public SchedulerFactoryBean schedulerFactoryBean() {
//        DataSource dataSource = dynamicDataSource.getResolvedDefaultDataSource();
//        SchedulerFactoryBean factory = new SchedulerFactoryBean();
//        factory.setDataSource(dataSource);
//        Properties prop = new Properties();
//        prop.put("org.quartz.scheduler.instanceName", "AmychScheduler");
//        prop.put("org.quartz.scheduler.instanceId", "AUTO");
//        prop.put("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
//        prop.put("org.quartz.threadPool.threadCount", "20");
//        prop.put("org.quartz.threadPool.threadPriority", "5");
//        prop.put("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
//        prop.put("org.quartz.jobStore.isClustered", "true");
//        prop.put("org.quartz.jobStore.clusterCheckinInterval", "15000");
//        prop.put("org.quartz.jobStore.maxMisfiresToHandleAtATime", "1");
//        prop.put("org.quartz.jobStore.misfireThreshold", "12000");
//        prop.put("org.quartz.jobStore.tablePrefix", "QRTZ_");
//        prop.put("org.quartz.jobStore.selectWithLockSQL", "SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?");
//        factory.setQuartzProperties(prop);
//        factory.setSchedulerName("AmychScheduler");
//        factory.setStartupDelay(30);
//        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
//        factory.setOverwriteExistingJobs(true);
//        factory.setAutoStartup(true);
//        return factory;
//    }
//}
